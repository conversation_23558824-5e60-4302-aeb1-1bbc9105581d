import React from 'react';
import WhatsAppConnector from './WhatsAppConnector';
import TelegramConnector from './TelegramConnector';
import { Box, Heading, Text, Button, VStack } from '@chakra-ui/react';

interface ChannelConnectorProps {
  channelName: string;
  onBack: () => void;
  onSuccess: () => void;
}

const ChannelConnector: React.FC<ChannelConnectorProps> = ({ channelName, onBack, onSuccess }) => {
  return (
    <Box>
      <Button onClick={onBack} mb={4}>Back to Channels</Button>
            <VStack spacing={4} align="stretch">
        <Heading size="lg">Configure {channelName}</Heading>
        {channelName === 'WhatsApp' ? (
          <WhatsAppConnector onSuccess={onSuccess} />
        ) : channelName === 'Telegram' ? (
          <TelegramConnector onSuccess={onSuccess} />
        ) : (
          <Text>Configuration for {channelName} is not yet available.</Text>
        )}
      </VStack>
    </Box>
  );
};

export default ChannelConnector;
