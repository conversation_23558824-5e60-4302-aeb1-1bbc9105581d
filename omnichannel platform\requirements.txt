# WhatsApp Business API Management Platform

# Core Framework
fastapi>=0.100.0
uvicorn>=0.22.0

# Database & ORM
sqlmodel
sqlalchemy>=2.0.0
alembic>=1.12.0
# sqlite3 is built into Python

# Authentication & Security
bcrypt>=4.0.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# WhatsApp Business API
requests>=2.31.0
httpx>=0.25.0

# Data Processing
pandas>=2.1.0
numpy>=1.24.0

# File Handling
python-multipart>=0.0.6
pillow>=10.0.0

# Environment & Configuration
python-dotenv>=1.0.0
pydantic>=2.4.0
pydantic-settings>=2.0.0

# Background Tasks
celery>=5.3.0
redis>=5.0.0

# Email & Notifications
sendgrid>=6.10.0
twilio>=8.10.0

# Monitoring & Logging
structlog>=23.1.0
sentry-sdk>=1.32.0

# Development & Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
black>=23.7.0
pylint>=3.0.0
mypy>=1.5.0
httpx>=0.25.0
fastapi>=0.100.0
uvicorn>=0.22.0
isort>=5.12.0
mypy>=1.5.0

# Additional Utilities
python-dateutil>=2.8.0
pytz>=2023.3
# uuid is built into Python
