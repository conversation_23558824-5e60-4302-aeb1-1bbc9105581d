from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from uuid import UUID, uuid4

from sqlmodel import SQLModel, Field, Column, JSON, DateTime, func, Relationship
from sqlalchemy import Integer

if TYPE_CHECKING:
    from .company import Company
    from .omnichannel import Channel

class ConversationStatus(str, Enum):
    ACTIVE = "active"
    ARCHIVED = "archived"
    SPAM = "spam"

class MessageType(str, Enum):
    TEXT = "text"
    IMAGE = "image"
    DOCUMENT = "document"
    AUDIO = "audio"
    VIDEO = "video"

class MessageStatus(str, Enum):
    SENDING = "sending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"

class ConversationMessageBase(SQLModel):
    """Base model for messages."""
    content: str
    message_type: MessageType = Field(default=MessageType.TEXT)
    status: MessageStatus = Field(default=MessageStatus.SENDING)
    sender_id: str  # User ID or system
    sender_type: str = "user"  # 'user', 'contact', or 'system'
    media_url: Optional[str] = None
    media_type: Optional[str] = None
    error: Optional[str] = None
    metadata_: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))
    conversation_id: UUID = Field(foreign_key="conversations.id")

class ConversationMessage(ConversationMessageBase, table=True):
    __tablename__ = "conversation_messages"
    """Message model for database storage."""
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    updated_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), onupdate=func.now())
    )

    conversation: "Conversation" = Relationship(back_populates="messages")

class ConversationBase(SQLModel):
    """Base model for conversations."""
    title: str
    status: ConversationStatus = Field(default=ConversationStatus.ACTIVE)
    channel: str  # whatsapp, email, sms, etc.
    channel_id: int = Field(foreign_key="channels.id")  # ID from the channel (e.g., WhatsApp number, email address)
    contact_id: Optional[int] = Field(default=None, foreign_key="customers.id")  # Link to contact if available
    metadata_: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))
    last_message_at: Optional[datetime] = None
    unread_count: int = 0
    company_id: int = Field(foreign_key="companies.id")

class Conversation(ConversationBase, table=True):
    """Conversation model for database storage."""
    __tablename__ = "conversations"
    
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    updated_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), onupdate=func.now())
    )
    
    # Relationships
    messages: List["ConversationMessage"] = Relationship(back_populates="conversation")
    company: "Company" = Relationship(back_populates="conversations")
    channel: "Channel" = Relationship(back_populates="conversations")
    customer: "Customer" = Relationship(back_populates="conversations")
    omnichannel_messages: List["OmnichannelMessage"] = Relationship(back_populates="conversation")

class ConversationMessageCreate(ConversationMessageBase):
    """Model for creating new messages."""
    pass

class ConversationMessageRead(ConversationMessageBase):
    """Model for reading message data."""
    id: UUID
    created_at: datetime
    updated_at: datetime
    conversation: "ConversationRead" = None

    class Config:
        from_attributes = True

class ConversationCreate(ConversationBase):
    """Model for creating new conversations."""
    pass

class ConversationRead(ConversationBase):
    """Model for reading conversation data."""
    id: UUID
    created_at: datetime
    updated_at: datetime
    messages: List[ConversationMessageRead] = []

    class Config:
        from_attributes = True

class ConversationUpdate(SQLModel):
    """Model for updating conversations."""
    title: Optional[str] = None
    status: Optional[ConversationStatus] = None
    metadata_: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    unread_count: Optional[int] = None
