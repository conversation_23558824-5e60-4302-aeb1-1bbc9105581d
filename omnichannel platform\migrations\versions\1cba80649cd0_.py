"""empty message

Revision ID: 1cba80649cd0
Revises: 
Create Date: 2025-07-02 19:27:59.604395+00:00

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '1cba80649cd0'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bot_flows',
    sa.<PERSON>umn('id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.<PERSON>umn('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('version', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('flow_metadata', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('bot_flows', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_bot_flows_id'), ['id'], unique=False)

    op.create_table('companies',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('settings', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('companies', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_companies_name'), ['name'], unique=True)

    op.create_table('messages',
    sa.Column('recipient', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('content', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'SENDING', 'SENT', 'DELIVERED', 'FAILED', 'READ', name='messagestatus'), nullable=False),
    sa.Column('message_type', sa.Enum('TEXT', 'IMAGE', 'DOCUMENT', 'AUDIO', 'VIDEO', name='messagetype'), nullable=False),
    sa.Column('media_url', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('media_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('error', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('metadata_', sa.JSON(), nullable=True),
    sa.Column('task_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('messages', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_messages_recipient'), ['recipient'], unique=False)
        batch_op.create_index(batch_op.f('ix_messages_task_id'), ['task_id'], unique=False)

    op.create_table('tasks',
    sa.Column('total', sa.Integer(), nullable=False),
    sa.Column('completed', sa.Integer(), nullable=False),
    sa.Column('succeeded', sa.Integer(), nullable=False),
    sa.Column('failed', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='taskstatus'), nullable=False),
    sa.Column('metadata_', sa.JSON(), nullable=True),
    sa.Column('id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('channel_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('channel_type', sa.String(length=20), nullable=False),
    sa.Column('config', sa.JSON(), nullable=False),
    sa.Column('is_active', sa.Boolean(), server_default='1', nullable=False),
    sa.Column('metadata', sa.JSON(), nullable=False),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('channel_accounts', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_channel_accounts_channel_type'), ['channel_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_channel_accounts_company_id'), ['company_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_channel_accounts_id'), ['id'], unique=False)

    op.create_table('customers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.Column('first_name', sa.String(length=100), nullable=True),
    sa.Column('last_name', sa.String(length=100), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('phone', sa.String(length=50), nullable=True),
    sa.Column('external_ids', sa.JSON(), nullable=False),
    sa.Column('metadata', sa.JSON(), nullable=False),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('customers', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_customers_company_id'), ['company_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_customers_email'), ['email'], unique=False)
        batch_op.create_index('ix_customers_email_phone', ['email', 'phone'], unique=False)
        batch_op.create_index(batch_op.f('ix_customers_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_customers_phone'), ['phone'], unique=False)

    op.create_table('flow_nodes',
    sa.Column('id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('node_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('position_x', sa.Float(), nullable=False),
    sa.Column('position_y', sa.Float(), nullable=False),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('style', sa.JSON(), nullable=True),
    sa.Column('z_index', sa.Integer(), nullable=True),
    sa.Column('selected', sa.Boolean(), nullable=True),
    sa.Column('dragging', sa.Boolean(), nullable=True),
    sa.Column('is_valid', sa.Boolean(), nullable=True),
    sa.Column('validation_errors', sa.JSON(), nullable=True),
    sa.Column('width', sa.Float(), nullable=True),
    sa.Column('height', sa.Float(), nullable=True),
    sa.Column('source_handles', sa.JSON(), nullable=True),
    sa.Column('target_handles', sa.JSON(), nullable=True),
    sa.Column('node_metadata', sa.JSON(), nullable=True),
    sa.Column('flow_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['flow_id'], ['bot_flows.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('flow_nodes', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_flow_nodes_flow_id'), ['flow_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_flow_nodes_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_flow_nodes_node_id'), ['node_id'], unique=False)

    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('username', sa.String(length=100), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('first_name', sa.String(length=100), nullable=False),
    sa.Column('last_name', sa.String(length=100), nullable=False),
    sa.Column('phone_number', sa.String(length=20), nullable=True),
    sa.Column('avatar_url', sa.String(length=500), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('role', sa.Enum('SUPER_ADMIN', 'ADMIN', 'MANAGER', 'USER', name='userrole'), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'PENDING_VERIFICATION', 'SUSPENDED', name='userstatus'), nullable=True),
    sa.Column('is_email_verified', sa.Boolean(), nullable=False),
    sa.Column('is_phone_verified', sa.Boolean(), nullable=False),
    sa.Column('two_factor_enabled', sa.Boolean(), nullable=False),
    sa.Column('two_factor_secret', sa.String(length=255), nullable=True),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('failed_login_attempts', sa.Integer(), nullable=False),
    sa.Column('locked_until', sa.DateTime(timezone=True), nullable=True),
    sa.Column('timezone', sa.String(length=50), nullable=False),
    sa.Column('language', sa.String(length=10), nullable=False),
    sa.Column('theme', sa.String(length=20), nullable=False),
    sa.Column('email_notifications', sa.Boolean(), nullable=False),
    sa.Column('marketing_emails', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_company_id'), ['company_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=True)

    op.create_table('bot_configuration',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('bot_name', sa.String(length=255), nullable=False),
    sa.Column('bot_type', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('test_scenarios', sa.JSON(), nullable=False),
    sa.Column('expected_responses', sa.JSON(), nullable=True),
    sa.Column('test_settings', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('channels',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('channel_type', sa.String(length=20), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('config', sa.JSON(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('metadata', sa.JSON(), server_default=sa.text("'{}'"), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['channel_accounts.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('channels', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_channels_account_id'), ['account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_channels_channel_type'), ['channel_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_channels_company_id'), ['company_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_channels_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_channels_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('ix_channels_status'), ['status'], unique=False)

    op.create_table('flow_edges',
    sa.Column('id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('source_handle', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('target_handle', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('animated', sa.Boolean(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('style', sa.JSON(), nullable=True),
    sa.Column('z_index', sa.Integer(), nullable=True),
    sa.Column('selected', sa.Boolean(), nullable=True),
    sa.Column('edge_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_valid', sa.Boolean(), nullable=True),
    sa.Column('validation_errors', sa.JSON(), nullable=True),
    sa.Column('waypoints', sa.JSON(), nullable=True),
    sa.Column('edge_metadata', sa.JSON(), nullable=True),
    sa.Column('flow_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('source_node_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('target_node_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['flow_id'], ['bot_flows.id'], ),
    sa.ForeignKeyConstraint(['source_node_id'], ['flow_nodes.id'], ),
    sa.ForeignKeyConstraint(['target_node_id'], ['flow_nodes.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('flow_edges', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_flow_edges_flow_id'), ['flow_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_flow_edges_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_flow_edges_source_node_id'), ['source_node_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_flow_edges_target_node_id'), ['target_node_id'], unique=False)

    op.create_table('telegram_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('account_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('bot_token', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('username', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'PENDING', 'SUSPENDED', 'DELETED', name='telegramaccountstatus'), nullable=False),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('verified_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('telegram_accounts', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_telegram_accounts_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_telegram_accounts_user_id'), ['user_id'], unique=False)

    op.create_table('user_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_token', sa.String(length=255), nullable=False),
    sa.Column('ip_address', sa.String(length=50), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('device_type', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('last_activity', sa.DateTime(timezone=True), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_sessions_session_token'), ['session_token'], unique=True)
        batch_op.create_index(batch_op.f('ix_user_sessions_user_id'), ['user_id'], unique=False)

    op.create_table('whatsapp_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('account_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('business_account_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('phone_number_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('phone_number', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('access_token', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('webhook_verify_token', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'PENDING', 'SUSPENDED', 'DELETED', name='whatsappaccountstatus'), nullable=False),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('verified_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('business_account_id')
    )
    with op.batch_alter_table('whatsapp_accounts', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_whatsapp_accounts_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_whatsapp_accounts_user_id'), ['user_id'], unique=False)

    op.create_table('bot_test',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('configuration_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('test_name', sa.String(length=255), nullable=False),
    sa.Column('target_phone', sa.String(length=20), nullable=False),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('total_scenarios', sa.Integer(), nullable=False),
    sa.Column('passed_scenarios', sa.Integer(), nullable=False),
    sa.Column('failed_scenarios', sa.Integer(), nullable=False),
    sa.Column('total_messages_sent', sa.Integer(), nullable=False),
    sa.Column('average_response_time', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['configuration_id'], ['bot_configuration.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('contact_groups',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_dynamic', sa.Boolean(), nullable=False),
    sa.Column('filter_criteria', sa.JSON(), nullable=False),
    sa.Column('member_count', sa.Integer(), nullable=False),
    sa.Column('metadata', sa.JSON(), server_default=sa.text("'{}'"), nullable=False),
    sa.Column('last_updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['whatsapp_accounts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('contact_groups', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_contact_groups_account_id'), ['account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_contact_groups_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_contact_groups_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_contact_groups_name'), ['name'], unique=False)

    op.create_table('contacts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('phone_number', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('country_code', sqlmodel.sql.sqltypes.AutoString(length=5), nullable=True),
    sa.Column('first_name', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('last_name', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('email', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'OPTED_OUT', 'BOUNCED', 'INVALID', name='contactstatus'), nullable=False),
    sa.Column('is_whatsapp_user', sa.Boolean(), nullable=True),
    sa.Column('metadata', sa.JSON(), server_default=sa.text("'{}'"), nullable=False),
    sa.ForeignKeyConstraint(['account_id'], ['whatsapp_accounts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('contacts', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_contacts_account_id'), ['account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_contacts_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_contacts_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_contacts_phone_number'), ['phone_number'], unique=True)

    op.create_table('conversations',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'ARCHIVED', 'SPAM', name='conversationstatus'), nullable=False),
    sa.Column('channel_id', sa.Integer(), nullable=False),
    sa.Column('contact_id', sa.Integer(), nullable=True),
    sa.Column('metadata_', sa.JSON(), nullable=True),
    sa.Column('last_message_at', sa.DateTime(), nullable=True),
    sa.Column('unread_count', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['channel_id'], ['channels.id'], ),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ),
    sa.ForeignKeyConstraint(['contact_id'], ['customers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('message_templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('language', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('category', sa.Enum('MARKETING', 'UTILITY', 'AUTHENTICATION', 'ALERT', 'APPOINTMENT', name='templatecategory'), nullable=False),
    sa.Column('header_text', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('body_text', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('footer_text', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('buttons', sa.JSON(), nullable=True),
    sa.Column('status', sa.Enum('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'PAUSED', 'DISABLED', name='templatestatus'), nullable=False),
    sa.Column('whatsapp_template_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('namespace', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('submitted_at', sa.DateTime(), nullable=True),
    sa.Column('approved_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['whatsapp_accounts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('message_templates', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_message_templates_account_id'), ['account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_message_templates_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_message_templates_name'), ['name'], unique=False)

    op.create_table('whatsapp_channels',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('channel_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', name='whatsappchannelstatus'), nullable=False),
    sa.Column('metadata_', sa.JSON(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['whatsapp_accounts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('whatsapp_channels', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_whatsapp_channels_account_id'), ['account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_whatsapp_channels_id'), ['id'], unique=False)

    op.create_table('whatsappflow',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('flow_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('flow_data', sa.JSON(), nullable=False),
    sa.Column('status', sa.Enum('DRAFT', 'PUBLISHED', 'ARCHIVED', 'DISABLED', name='flowstatus'), nullable=False),
    sa.Column('published_at', sa.DateTime(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['whatsapp_accounts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('flow_id')
    )
    with op.batch_alter_table('whatsappflow', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_whatsappflow_account_id'), ['account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_whatsappflow_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_whatsappflow_name'), ['name'], unique=False)

    op.create_table('bot_test_result',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('test_id', sa.Integer(), nullable=False),
    sa.Column('scenario_name', sa.String(length=255), nullable=False),
    sa.Column('scenario_order', sa.Integer(), nullable=False),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('test_message', sa.Text(), nullable=False),
    sa.Column('expected_response', sa.Text(), nullable=True),
    sa.Column('actual_response', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('response_time', sa.Float(), nullable=True),
    sa.Column('test_data', sa.JSON(), nullable=True),
    sa.Column('error_details', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['test_id'], ['bot_test.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('campaigns',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('message_template_id', sa.Integer(), nullable=True),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('message_content', sa.JSON(), nullable=False),
    sa.Column('target_audience', sa.JSON(), nullable=False),
    sa.Column('scheduled_for', sa.DateTime(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('status', sa.Enum('DRAFT', 'SCHEDULED', 'SENDING', 'SENT', 'PAUSED', 'COMPLETED', 'CANCELED', 'FAILED', name='campaignstatus'), nullable=False),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=False),
    sa.Column('total_recipients', sa.Integer(), nullable=False),
    sa.Column('messages_sent', sa.Integer(), nullable=False),
    sa.Column('messages_delivered', sa.Integer(), nullable=False),
    sa.Column('messages_failed', sa.Integer(), nullable=False),
    sa.Column('error_message', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('target_group_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['whatsapp_accounts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['message_template_id'], ['message_templates.id'], ),
    sa.ForeignKeyConstraint(['target_group_id'], ['contact_groups.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('campaigns', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_campaigns_account_id'), ['account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_campaigns_created_by'), ['created_by'], unique=False)
        batch_op.create_index(batch_op.f('ix_campaigns_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_campaigns_message_template_id'), ['message_template_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_campaigns_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('ix_campaigns_target_group_id'), ['target_group_id'], unique=False)

    op.create_table('contact_group_members',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('contact_id', sa.Integer(), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('metadata', sa.JSON(), server_default=sa.text("'{}'"), nullable=False),
    sa.ForeignKeyConstraint(['contact_id'], ['contacts.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['group_id'], ['contact_groups.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id', 'contact_id', 'group_id')
    )
    with op.batch_alter_table('contact_group_members', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_contact_group_members_id'), ['id'], unique=False)

    op.create_table('conversation_messages',
    sa.Column('content', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('message_type', sa.Enum('TEXT', 'IMAGE', 'DOCUMENT', 'AUDIO', 'VIDEO', name='messagetype'), nullable=False),
    sa.Column('status', sa.Enum('SENDING', 'SENT', 'DELIVERED', 'READ', 'FAILED', name='messagestatus'), nullable=False),
    sa.Column('sender_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('sender_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('media_url', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('media_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('error', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('metadata_', sa.JSON(), nullable=True),
    sa.Column('conversation_id', sa.Uuid(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('campaign_analytics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('campaign_id', sa.Integer(), nullable=False),
    sa.Column('total_recipients', sa.Integer(), nullable=False),
    sa.Column('messages_sent', sa.Integer(), nullable=False),
    sa.Column('messages_delivered', sa.Integer(), nullable=False),
    sa.Column('messages_read', sa.Integer(), nullable=False),
    sa.Column('responses_received', sa.Integer(), nullable=False),
    sa.Column('delivery_rate', sa.Float(), nullable=False),
    sa.Column('read_rate', sa.Float(), nullable=False),
    sa.Column('response_rate', sa.Float(), nullable=False),
    sa.Column('average_delivery_time', sa.Float(), nullable=True),
    sa.Column('average_read_time', sa.Float(), nullable=True),
    sa.Column('average_response_time', sa.Float(), nullable=True),
    sa.Column('total_cost', sa.Float(), nullable=True),
    sa.Column('cost_per_message', sa.Float(), nullable=True),
    sa.Column('analytics_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('omnichannel_messages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.Column('conversation_id', sa.Integer(), nullable=False),
    sa.Column('channel_id', sa.Integer(), nullable=False),
    sa.Column('customer_id', sa.Integer(), nullable=False),
    sa.Column('flow_id', sa.Integer(), nullable=True),
    sa.Column('campaign_id', sa.Integer(), nullable=True),
    sa.Column('message_type', sa.String(length=20), nullable=True),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('content_json', sa.JSON(), nullable=False),
    sa.Column('direction', sa.String(length=20), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('status_updates', sa.JSON(), nullable=False),
    sa.Column('external_id', sa.String(length=255), nullable=True),
    sa.Column('external_conversation_id', sa.String(length=255), nullable=True),
    sa.Column('error_code', sa.String(length=100), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('sent_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['channel_id'], ['channels.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['flow_id'], ['whatsappflow.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('omnichannel_messages', schema=None) as batch_op:
        batch_op.create_index('ix_messages_conversation_created', ['conversation_id', 'created_at'], unique=False)
        batch_op.create_index('ix_messages_customer_created', ['customer_id', 'created_at'], unique=False)
        batch_op.create_index('ix_messages_external_id', ['channel_id', 'external_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_campaign_id'), ['campaign_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_channel_id'), ['channel_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_company_id'), ['company_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_conversation_id'), ['conversation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_customer_id'), ['customer_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_direction'), ['direction'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_external_conversation_id'), ['external_conversation_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_external_id'), ['external_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_flow_id'), ['flow_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_message_type'), ['message_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_sent_at'), ['sent_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_omnichannel_messages_status'), ['status'], unique=False)

    op.create_table('message_analytics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('deleted_by', sa.Integer(), nullable=True),
    sa.Column('metadata_', sa.JSON(), server_default=sa.text("'{}'"), nullable=True),
    sa.Column('message_id', sa.Integer(), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('delivery_status', sa.String(length=20), nullable=False),
    sa.Column('sent_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('read_status', sa.Boolean(), nullable=False),
    sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('response_received', sa.Boolean(), nullable=False),
    sa.Column('response_message_id', sa.Integer(), nullable=True),
    sa.Column('delivery_time', sa.Float(), nullable=True),
    sa.Column('read_time', sa.Float(), nullable=True),
    sa.Column('response_time', sa.Float(), nullable=True),
    sa.Column('link_clicks', sa.Integer(), nullable=False),
    sa.Column('button_clicks', sa.JSON(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('analytics_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['whatsapp_accounts.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['message_id'], ['omnichannel_messages.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['response_message_id'], ['omnichannel_messages.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('message_analytics', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_message_analytics_id'), ['id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_analytics', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_message_analytics_id'))

    op.drop_table('message_analytics')
    with op.batch_alter_table('omnichannel_messages', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_status'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_sent_at'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_message_type'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_id'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_flow_id'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_external_id'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_external_conversation_id'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_direction'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_customer_id'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_conversation_id'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_company_id'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_channel_id'))
        batch_op.drop_index(batch_op.f('ix_omnichannel_messages_campaign_id'))
        batch_op.drop_index('ix_messages_external_id')
        batch_op.drop_index('ix_messages_customer_created')
        batch_op.drop_index('ix_messages_conversation_created')

    op.drop_table('omnichannel_messages')
    op.drop_table('campaign_analytics')
    op.drop_table('conversation_messages')
    with op.batch_alter_table('contact_group_members', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_contact_group_members_id'))

    op.drop_table('contact_group_members')
    with op.batch_alter_table('campaigns', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_campaigns_target_group_id'))
        batch_op.drop_index(batch_op.f('ix_campaigns_name'))
        batch_op.drop_index(batch_op.f('ix_campaigns_message_template_id'))
        batch_op.drop_index(batch_op.f('ix_campaigns_id'))
        batch_op.drop_index(batch_op.f('ix_campaigns_created_by'))
        batch_op.drop_index(batch_op.f('ix_campaigns_account_id'))

    op.drop_table('campaigns')
    op.drop_table('bot_test_result')
    with op.batch_alter_table('whatsappflow', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_whatsappflow_name'))
        batch_op.drop_index(batch_op.f('ix_whatsappflow_id'))
        batch_op.drop_index(batch_op.f('ix_whatsappflow_account_id'))

    op.drop_table('whatsappflow')
    with op.batch_alter_table('whatsapp_channels', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_whatsapp_channels_id'))
        batch_op.drop_index(batch_op.f('ix_whatsapp_channels_account_id'))

    op.drop_table('whatsapp_channels')
    with op.batch_alter_table('message_templates', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_message_templates_name'))
        batch_op.drop_index(batch_op.f('ix_message_templates_id'))
        batch_op.drop_index(batch_op.f('ix_message_templates_account_id'))

    op.drop_table('message_templates')
    op.drop_table('conversations')
    with op.batch_alter_table('contacts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_contacts_phone_number'))
        batch_op.drop_index(batch_op.f('ix_contacts_id'))
        batch_op.drop_index(batch_op.f('ix_contacts_created_by'))
        batch_op.drop_index(batch_op.f('ix_contacts_account_id'))

    op.drop_table('contacts')
    with op.batch_alter_table('contact_groups', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_contact_groups_name'))
        batch_op.drop_index(batch_op.f('ix_contact_groups_id'))
        batch_op.drop_index(batch_op.f('ix_contact_groups_created_by'))
        batch_op.drop_index(batch_op.f('ix_contact_groups_account_id'))

    op.drop_table('contact_groups')
    op.drop_table('bot_test')
    with op.batch_alter_table('whatsapp_accounts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_whatsapp_accounts_user_id'))
        batch_op.drop_index(batch_op.f('ix_whatsapp_accounts_id'))

    op.drop_table('whatsapp_accounts')
    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_sessions_user_id'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_session_token'))

    op.drop_table('user_sessions')
    with op.batch_alter_table('telegram_accounts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_telegram_accounts_user_id'))
        batch_op.drop_index(batch_op.f('ix_telegram_accounts_id'))

    op.drop_table('telegram_accounts')
    with op.batch_alter_table('flow_edges', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_flow_edges_target_node_id'))
        batch_op.drop_index(batch_op.f('ix_flow_edges_source_node_id'))
        batch_op.drop_index(batch_op.f('ix_flow_edges_id'))
        batch_op.drop_index(batch_op.f('ix_flow_edges_flow_id'))

    op.drop_table('flow_edges')
    with op.batch_alter_table('channels', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_channels_status'))
        batch_op.drop_index(batch_op.f('ix_channels_name'))
        batch_op.drop_index(batch_op.f('ix_channels_id'))
        batch_op.drop_index(batch_op.f('ix_channels_company_id'))
        batch_op.drop_index(batch_op.f('ix_channels_channel_type'))
        batch_op.drop_index(batch_op.f('ix_channels_account_id'))

    op.drop_table('channels')
    op.drop_table('bot_configuration')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_username'))
        batch_op.drop_index(batch_op.f('ix_users_email'))
        batch_op.drop_index(batch_op.f('ix_users_company_id'))

    op.drop_table('users')
    with op.batch_alter_table('flow_nodes', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_flow_nodes_node_id'))
        batch_op.drop_index(batch_op.f('ix_flow_nodes_id'))
        batch_op.drop_index(batch_op.f('ix_flow_nodes_flow_id'))

    op.drop_table('flow_nodes')
    with op.batch_alter_table('customers', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_customers_phone'))
        batch_op.drop_index(batch_op.f('ix_customers_id'))
        batch_op.drop_index('ix_customers_email_phone')
        batch_op.drop_index(batch_op.f('ix_customers_email'))
        batch_op.drop_index(batch_op.f('ix_customers_company_id'))

    op.drop_table('customers')
    with op.batch_alter_table('channel_accounts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_channel_accounts_id'))
        batch_op.drop_index(batch_op.f('ix_channel_accounts_company_id'))
        batch_op.drop_index(batch_op.f('ix_channel_accounts_channel_type'))

    op.drop_table('channel_accounts')
    op.drop_table('tasks')
    with op.batch_alter_table('messages', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_messages_task_id'))
        batch_op.drop_index(batch_op.f('ix_messages_recipient'))

    op.drop_table('messages')
    with op.batch_alter_table('companies', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_companies_name'))

    op.drop_table('companies')
    with op.batch_alter_table('bot_flows', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bot_flows_id'))

    op.drop_table('bot_flows')
    # ### end Alembic commands ###
