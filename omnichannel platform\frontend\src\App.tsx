import { Box } from '@chakra-ui/react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Layout components
import Layout from './components/layout/Layout';

// Pages
import Dashboard from './pages/Dashboard';
import Conversations from './pages/Conversations';
import BotBuilder from './pages/BotBuilder';
import Settings from './pages/Settings';
import BulkMessaging from './pages/BulkMessaging';
import Login from './pages/Login';
import InboxList from './pages/inbox/InboxList';
import AddInbox from './pages/inbox/AddInbox';
import InboxDetail from './pages/inbox/InboxDetail';
import InboxEdit from './pages/inbox/InboxEdit';

// Create a client
const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Box minH="100vh">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Layout />}>
              <Route index element={<Dashboard />} />
              <Route path="conversations" element={<Conversations />} />
              <Route path="bot-builder" element={<BotBuilder />} />
              <Route path="settings" element={<Settings />} />
              <Route path="bulk-messaging" element={<BulkMessaging />} />
              <Route path="inboxes">
                <Route index element={<InboxList />} />
                <Route path="new" element={<AddInbox />} />
                <Route path=":inboxId" element={<InboxDetail />} />
                <Route path=":inboxId/edit" element={<InboxEdit />} />
              </Route>
            </Route>
          </Routes>
        </Box>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
