<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Type Object Structures" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/typeobj.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Perhaps one of the most important structures of the Python object system is the structure that defines a new type: the PyTypeObject structure. Type objects can be handled using any of the PyObject_..." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Perhaps one of the most important structures of the Python object system is the structure that defines a new type: the PyTypeObject structure. Type objects can be handled using any of the PyObject_..." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Type Object Structures &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Supporting Cyclic Garbage Collection" href="gcsupport.html" />
    <link rel="prev" title="Common Object Structures" href="structures.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/typeobj.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Type Object Structures</a><ul>
<li><a class="reference internal" href="#quick-reference">Quick Reference</a><ul>
<li><a class="reference internal" href="#tp-slots">“tp slots”</a></li>
<li><a class="reference internal" href="#sub-slots">sub-slots</a></li>
<li><a class="reference internal" href="#slot-typedefs">slot typedefs</a></li>
</ul>
</li>
<li><a class="reference internal" href="#pytypeobject-definition">PyTypeObject Definition</a></li>
<li><a class="reference internal" href="#pyobject-slots">PyObject Slots</a></li>
<li><a class="reference internal" href="#pyvarobject-slots">PyVarObject Slots</a></li>
<li><a class="reference internal" href="#pytypeobject-slots">PyTypeObject Slots</a></li>
<li><a class="reference internal" href="#static-types">Static Types</a></li>
<li><a class="reference internal" href="#heap-types">Heap Types</a></li>
<li><a class="reference internal" href="#number-object-structures">Number Object Structures</a></li>
<li><a class="reference internal" href="#mapping-object-structures">Mapping Object Structures</a></li>
<li><a class="reference internal" href="#sequence-object-structures">Sequence Object Structures</a></li>
<li><a class="reference internal" href="#buffer-object-structures">Buffer Object Structures</a></li>
<li><a class="reference internal" href="#async-object-structures">Async Object Structures</a></li>
<li><a class="reference internal" href="#slot-type-typedefs">Slot Type typedefs</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="structures.html"
                          title="previous chapter">Common Object Structures</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="gcsupport.html"
                          title="next chapter">Supporting Cyclic Garbage Collection</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/typeobj.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="gcsupport.html" title="Supporting Cyclic Garbage Collection"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="structures.html" title="Common Object Structures"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="objimpl.html" accesskey="U">Object Implementation Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Type Object Structures</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="type-object-structures">
<span id="type-structs"></span><h1>Type Object Structures<a class="headerlink" href="#type-object-structures" title="Link to this heading">¶</a></h1>
<p>Perhaps one of the most important structures of the Python object system is the
structure that defines a new type: the <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> structure.  Type
objects can be handled using any of the <code class="docutils literal notranslate"><span class="pre">PyObject_*</span></code> or
<code class="docutils literal notranslate"><span class="pre">PyType_*</span></code> functions, but do not offer much that’s interesting to most
Python applications. These objects are fundamental to how objects behave, so
they are very important to the interpreter itself and to any extension module
that implements new types.</p>
<p>Type objects are fairly large compared to most of the standard types. The reason
for the size is that each type object stores a large number of values, mostly C
function pointers, each of which implements a small part of the type’s
functionality.  The fields of the type object are examined in detail in this
section.  The fields will be described in the order in which they occur in the
structure.</p>
<p>In addition to the following quick reference, the <a class="reference internal" href="#typedef-examples"><span class="std std-ref">Examples</span></a>
section provides at-a-glance insight into the meaning and use of
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a>.</p>
<section id="quick-reference">
<h2>Quick Reference<a class="headerlink" href="#quick-reference" title="Link to this heading">¶</a></h2>
<section id="tp-slots">
<span id="tp-slots-table"></span><h3>“tp slots”<a class="headerlink" href="#tp-slots" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<colgroup>
<col style="width: 31.0%" />
<col style="width: 31.0%" />
<col style="width: 31.0%" />
<col style="width: 1.7%" />
<col style="width: 1.7%" />
<col style="width: 1.7%" />
<col style="width: 1.7%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head" rowspan="2"><p>PyTypeObject Slot <a class="footnote-reference brackets" href="#slots" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p></th>
<th class="head" rowspan="2"><p><a class="reference internal" href="#slot-typedefs-table"><span class="std std-ref">Type</span></a></p></th>
<th class="head" rowspan="2"><p>special
methods/attrs</p></th>
<th class="head" colspan="4"><p>Info <a class="footnote-reference brackets" href="#cols" id="id2" role="doc-noteref"><span class="fn-bracket">[</span>2<span class="fn-bracket">]</span></a></p></th>
</tr>
<tr class="row-even"><th class="head"><p>O</p></th>
<th class="head"><p>T</p></th>
<th class="head"><p>D</p></th>
<th class="head"><p>I</p></th>
</tr>
</thead>
<tbody>
<tr class="row-odd"><td><p>&lt;R&gt; <a class="reference internal" href="#c.PyTypeObject.tp_name" title="PyTypeObject.tp_name"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_name</span></code></a></p></td>
<td><p>const char *</p></td>
<td><p>__name__</p></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_basicsize" title="PyTypeObject.tp_basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code></a></p></td>
<td><p><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></p></td>
<td></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a></p></td>
<td><p><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_dealloc" title="PyTypeObject.tp_dealloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dealloc</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.destructor" title="destructor"><code class="xref c c-type docutils literal notranslate"><span class="pre">destructor</span></code></a></p></td>
<td></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_vectorcall_offset" title="PyTypeObject.tp_vectorcall_offset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_vectorcall_offset</span></code></a></p></td>
<td><p><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p>(<a class="reference internal" href="#c.PyTypeObject.tp_getattr" title="PyTypeObject.tp_getattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattr</span></code></a>)</p></td>
<td><p><a class="reference internal" href="#c.getattrfunc" title="getattrfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getattrfunc</span></code></a></p></td>
<td><p>__getattribute__,
__getattr__</p></td>
<td></td>
<td></td>
<td></td>
<td><p>G</p></td>
</tr>
<tr class="row-odd"><td><p>(<a class="reference internal" href="#c.PyTypeObject.tp_setattr" title="PyTypeObject.tp_setattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattr</span></code></a>)</p></td>
<td><p><a class="reference internal" href="#c.setattrfunc" title="setattrfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">setattrfunc</span></code></a></p></td>
<td><p>__setattr__,
__delattr__</p></td>
<td></td>
<td></td>
<td></td>
<td><p>G</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_as_async" title="PyTypeObject.tp_as_async"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_async</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.PyAsyncMethods" title="PyAsyncMethods"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyAsyncMethods</span></code></a> *</p></td>
<td><p><a class="reference internal" href="#sub-slots"><span class="std std-ref">sub-slots</span></a></p></td>
<td></td>
<td></td>
<td></td>
<td><p>%</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_repr" title="PyTypeObject.tp_repr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_repr</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.reprfunc" title="reprfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">reprfunc</span></code></a></p></td>
<td><p>__repr__</p></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_as_number" title="PyTypeObject.tp_as_number"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_number</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyNumberMethods</span></code></a> *</p></td>
<td><p><a class="reference internal" href="#sub-slots"><span class="std std-ref">sub-slots</span></a></p></td>
<td></td>
<td></td>
<td></td>
<td><p>%</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_as_sequence" title="PyTypeObject.tp_as_sequence"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_sequence</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><code class="xref c c-type docutils literal notranslate"><span class="pre">PySequenceMethods</span></code></a> *</p></td>
<td><p><a class="reference internal" href="#sub-slots"><span class="std std-ref">sub-slots</span></a></p></td>
<td></td>
<td></td>
<td></td>
<td><p>%</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_as_mapping" title="PyTypeObject.tp_as_mapping"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_mapping</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.PyMappingMethods" title="PyMappingMethods"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMappingMethods</span></code></a> *</p></td>
<td><p><a class="reference internal" href="#sub-slots"><span class="std std-ref">sub-slots</span></a></p></td>
<td></td>
<td></td>
<td></td>
<td><p>%</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.hashfunc" title="hashfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">hashfunc</span></code></a></p></td>
<td><p>__hash__</p></td>
<td><p>X</p></td>
<td></td>
<td></td>
<td><p>G</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.ternaryfunc" title="ternaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ternaryfunc</span></code></a></p></td>
<td><p>__call__</p></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_str" title="PyTypeObject.tp_str"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_str</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.reprfunc" title="reprfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">reprfunc</span></code></a></p></td>
<td><p>__str__</p></td>
<td><p>X</p></td>
<td></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.getattrofunc" title="getattrofunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getattrofunc</span></code></a></p></td>
<td><p>__getattribute__,
__getattr__</p></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td><p>G</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.setattrofunc" title="setattrofunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">setattrofunc</span></code></a></p></td>
<td><p>__setattr__,
__delattr__</p></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td><p>G</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_as_buffer" title="PyTypeObject.tp_as_buffer"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_buffer</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.PyBufferProcs" title="PyBufferProcs"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyBufferProcs</span></code></a> *</p></td>
<td><p><a class="reference internal" href="#sub-slots"><span class="std std-ref">sub-slots</span></a></p></td>
<td></td>
<td></td>
<td></td>
<td><p>%</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a></p></td>
<td><p>unsigned long</p></td>
<td></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td><p>?</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_doc" title="PyTypeObject.tp_doc"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_doc</span></code></a></p></td>
<td><p>const char *</p></td>
<td><p>__doc__</p></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a></p></td>
<td><p><a class="reference internal" href="gcsupport.html#c.traverseproc" title="traverseproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">traverseproc</span></code></a></p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>G</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a></p></td>
<td><p><a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><code class="xref c c-type docutils literal notranslate"><span class="pre">inquiry</span></code></a></p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>G</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.richcmpfunc" title="richcmpfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">richcmpfunc</span></code></a></p></td>
<td><p>__lt__,
__le__,
__eq__,
__ne__,
__gt__,
__ge__</p></td>
<td><p>X</p></td>
<td></td>
<td></td>
<td><p>G</p></td>
</tr>
<tr class="row-even"><td><p>(<a class="reference internal" href="#c.PyTypeObject.tp_weaklistoffset" title="PyTypeObject.tp_weaklistoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklistoffset</span></code></a>)</p></td>
<td><p><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>?</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_iter" title="PyTypeObject.tp_iter"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_iter</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.getiterfunc" title="getiterfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getiterfunc</span></code></a></p></td>
<td><p>__iter__</p></td>
<td></td>
<td></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_iternext" title="PyTypeObject.tp_iternext"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_iternext</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.iternextfunc" title="iternextfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">iternextfunc</span></code></a></p></td>
<td><p>__next__</p></td>
<td></td>
<td></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_methods" title="PyTypeObject.tp_methods"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_methods</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyMethodDef" title="PyMethodDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMethodDef</span></code></a> []</p></td>
<td></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_members" title="PyTypeObject.tp_members"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_members</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyMemberDef" title="PyMemberDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMemberDef</span></code></a> []</p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_getset" title="PyTypeObject.tp_getset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getset</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyGetSetDef" title="PyGetSetDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyGetSetDef</span></code></a> []</p></td>
<td></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_base" title="PyTypeObject.tp_base"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_base</span></code></a></p></td>
<td><p><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> *</p></td>
<td><p>__base__</p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_dict" title="PyTypeObject.tp_dict"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dict</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p>__dict__</p></td>
<td></td>
<td></td>
<td><p>?</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_descr_get" title="PyTypeObject.tp_descr_get"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_descr_get</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.descrgetfunc" title="descrgetfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">descrgetfunc</span></code></a></p></td>
<td><p>__get__</p></td>
<td></td>
<td></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_descr_set" title="PyTypeObject.tp_descr_set"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_descr_set</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.descrsetfunc" title="descrsetfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">descrsetfunc</span></code></a></p></td>
<td><p>__set__,
__delete__</p></td>
<td></td>
<td></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p>(<a class="reference internal" href="#c.PyTypeObject.tp_dictoffset" title="PyTypeObject.tp_dictoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dictoffset</span></code></a>)</p></td>
<td><p><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>?</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.initproc" title="initproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">initproc</span></code></a></p></td>
<td><p>__init__</p></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_alloc" title="PyTypeObject.tp_alloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_alloc</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.allocfunc" title="allocfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">allocfunc</span></code></a></p></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>?</p></td>
<td><p>?</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.newfunc" title="newfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">newfunc</span></code></a></p></td>
<td><p>__new__</p></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td><p>?</p></td>
<td><p>?</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_free" title="PyTypeObject.tp_free"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_free</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.freefunc" title="freefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">freefunc</span></code></a></p></td>
<td></td>
<td><p>X</p></td>
<td><p>X</p></td>
<td><p>?</p></td>
<td><p>?</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_is_gc" title="PyTypeObject.tp_is_gc"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_is_gc</span></code></a></p></td>
<td><p><a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><code class="xref c c-type docutils literal notranslate"><span class="pre">inquiry</span></code></a></p></td>
<td></td>
<td></td>
<td><p>X</p></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p>&lt;<a class="reference internal" href="#c.PyTypeObject.tp_bases" title="PyTypeObject.tp_bases"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_bases</span></code></a>&gt;</p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p>__bases__</p></td>
<td></td>
<td></td>
<td><p>~</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p>&lt;<a class="reference internal" href="#c.PyTypeObject.tp_mro" title="PyTypeObject.tp_mro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_mro</span></code></a>&gt;</p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p>__mro__</p></td>
<td></td>
<td></td>
<td><p>~</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p>[<a class="reference internal" href="#c.PyTypeObject.tp_cache" title="PyTypeObject.tp_cache"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_cache</span></code></a>]</p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td></td>
<td></td>
<td></td>
<td colspan="2"></td>
</tr>
<tr class="row-odd"><td><p>[<a class="reference internal" href="#c.PyTypeObject.tp_subclasses" title="PyTypeObject.tp_subclasses"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_subclasses</span></code></a>]</p></td>
<td><p>void *</p></td>
<td><p>__subclasses__</p></td>
<td></td>
<td></td>
<td colspan="2"></td>
</tr>
<tr class="row-even"><td><p>[<a class="reference internal" href="#c.PyTypeObject.tp_weaklist" title="PyTypeObject.tp_weaklist"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklist</span></code></a>]</p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td></td>
<td></td>
<td></td>
<td colspan="2"></td>
</tr>
<tr class="row-odd"><td><p>(<a class="reference internal" href="#c.PyTypeObject.tp_del" title="PyTypeObject.tp_del"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_del</span></code></a>)</p></td>
<td><p><a class="reference internal" href="#c.destructor" title="destructor"><code class="xref c c-type docutils literal notranslate"><span class="pre">destructor</span></code></a></p></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr class="row-even"><td><p>[<a class="reference internal" href="#c.PyTypeObject.tp_version_tag" title="PyTypeObject.tp_version_tag"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_version_tag</span></code></a>]</p></td>
<td><p>unsigned int</p></td>
<td></td>
<td></td>
<td></td>
<td colspan="2"></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_finalize" title="PyTypeObject.tp_finalize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_finalize</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.destructor" title="destructor"><code class="xref c c-type docutils literal notranslate"><span class="pre">destructor</span></code></a></p></td>
<td><p>__del__</p></td>
<td></td>
<td></td>
<td></td>
<td><p>X</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTypeObject.tp_vectorcall" title="PyTypeObject.tp_vectorcall"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_vectorcall</span></code></a></p></td>
<td><p><a class="reference internal" href="call.html#c.vectorcallfunc" title="vectorcallfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">vectorcallfunc</span></code></a></p></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr class="row-odd"><td><p>[<a class="reference internal" href="#c.PyTypeObject.tp_watched" title="PyTypeObject.tp_watched"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_watched</span></code></a>]</p></td>
<td><p>unsigned char</p></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="slots" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p><strong>()</strong>: A slot name in parentheses indicates it is (effectively) deprecated.</p>
<p><strong>&lt;&gt;</strong>: Names in angle brackets should be initially set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and
treated as read-only.</p>
<p><strong>[]</strong>: Names in square brackets are for internal use only.</p>
<p><strong>&lt;R&gt;</strong> (as a prefix) means the field is required (must be non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code>).</p>
</aside>
<aside class="footnote brackets" id="cols" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id2">2</a><span class="fn-bracket">]</span></span>
<p>Columns:</p>
<p><strong>“O”</strong>:  set on <a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a></p>
<p><strong>“T”</strong>:  set on <a class="reference internal" href="type.html#c.PyType_Type" title="PyType_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyType_Type</span></code></a></p>
<p><strong>“D”</strong>:  default (if slot is set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>)</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>X - PyType_Ready sets this value if it is NULL
~ - PyType_Ready always sets this value (it should be NULL)
? - PyType_Ready may set this value depending on other slots

Also see the inheritance column (&quot;I&quot;).
</pre></div>
</div>
<p><strong>“I”</strong>:  inheritance</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>X - type slot is inherited via *PyType_Ready* if defined with a *NULL* value
% - the slots of the sub-struct are inherited individually
G - inherited, but only in combination with other slots; see the slot&#39;s description
? - it&#39;s complicated; see the slot&#39;s description
</pre></div>
</div>
<p>Note that some slots are effectively inherited through the normal
attribute lookup chain.</p>
</aside>
</aside>
</section>
<section id="sub-slots">
<span id="id3"></span><h3>sub-slots<a class="headerlink" href="#sub-slots" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<colgroup>
<col style="width: 47.3%" />
<col style="width: 30.9%" />
<col style="width: 21.8%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Slot</p></th>
<th class="head"><p><a class="reference internal" href="#slot-typedefs-table"><span class="std std-ref">Type</span></a></p></th>
<th class="head"><p>special
methods</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyAsyncMethods.am_await" title="PyAsyncMethods.am_await"><code class="xref c c-member docutils literal notranslate"><span class="pre">am_await</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__await__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyAsyncMethods.am_aiter" title="PyAsyncMethods.am_aiter"><code class="xref c c-member docutils literal notranslate"><span class="pre">am_aiter</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__aiter__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyAsyncMethods.am_anext" title="PyAsyncMethods.am_anext"><code class="xref c c-member docutils literal notranslate"><span class="pre">am_anext</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__anext__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyAsyncMethods.am_send" title="PyAsyncMethods.am_send"><code class="xref c c-member docutils literal notranslate"><span class="pre">am_send</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.sendfunc" title="sendfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">sendfunc</span></code></a></p></td>
<td></td>
</tr>
<tr class="row-even"><td colspan="3"></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_add" title="PyNumberMethods.nb_add"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_add</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__add__
__radd__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_add" title="PyNumberMethods.nb_inplace_add"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_add</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__iadd__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_subtract" title="PyNumberMethods.nb_subtract"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_subtract</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__sub__
__rsub__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_subtract" title="PyNumberMethods.nb_inplace_subtract"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_subtract</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__isub__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_multiply" title="PyNumberMethods.nb_multiply"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_multiply</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__mul__
__rmul__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_multiply" title="PyNumberMethods.nb_inplace_multiply"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_multiply</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__imul__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_remainder" title="PyNumberMethods.nb_remainder"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_remainder</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__mod__
__rmod__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_remainder" title="PyNumberMethods.nb_inplace_remainder"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_remainder</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__imod__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_divmod" title="PyNumberMethods.nb_divmod"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_divmod</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__divmod__
__rdivmod__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_power" title="PyNumberMethods.nb_power"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_power</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.ternaryfunc" title="ternaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ternaryfunc</span></code></a></p></td>
<td><p>__pow__
__rpow__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_power" title="PyNumberMethods.nb_inplace_power"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_power</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.ternaryfunc" title="ternaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ternaryfunc</span></code></a></p></td>
<td><p>__ipow__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_negative" title="PyNumberMethods.nb_negative"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_negative</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__neg__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_positive" title="PyNumberMethods.nb_positive"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_positive</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__pos__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_absolute" title="PyNumberMethods.nb_absolute"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_absolute</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__abs__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_bool" title="PyNumberMethods.nb_bool"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_bool</span></code></a></p></td>
<td><p><a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><code class="xref c c-type docutils literal notranslate"><span class="pre">inquiry</span></code></a></p></td>
<td><p>__bool__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_invert" title="PyNumberMethods.nb_invert"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_invert</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__invert__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_lshift" title="PyNumberMethods.nb_lshift"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_lshift</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__lshift__
__rlshift__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_lshift" title="PyNumberMethods.nb_inplace_lshift"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_lshift</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__ilshift__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_rshift" title="PyNumberMethods.nb_rshift"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_rshift</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__rshift__
__rrshift__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_rshift" title="PyNumberMethods.nb_inplace_rshift"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_rshift</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__irshift__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_and" title="PyNumberMethods.nb_and"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_and</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__and__
__rand__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_and" title="PyNumberMethods.nb_inplace_and"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_and</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__iand__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_xor" title="PyNumberMethods.nb_xor"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_xor</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__xor__
__rxor__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_xor" title="PyNumberMethods.nb_inplace_xor"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_xor</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__ixor__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_or" title="PyNumberMethods.nb_or"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_or</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__or__
__ror__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_or" title="PyNumberMethods.nb_inplace_or"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_or</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__ior__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_int" title="PyNumberMethods.nb_int"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_int</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__int__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_reserved" title="PyNumberMethods.nb_reserved"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_reserved</span></code></a></p></td>
<td><p>void *</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_float" title="PyNumberMethods.nb_float"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_float</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__float__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_floor_divide" title="PyNumberMethods.nb_floor_divide"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_floor_divide</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__floordiv__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_floor_divide" title="PyNumberMethods.nb_inplace_floor_divide"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_floor_divide</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__ifloordiv__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_true_divide" title="PyNumberMethods.nb_true_divide"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_true_divide</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__truediv__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_true_divide" title="PyNumberMethods.nb_inplace_true_divide"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_true_divide</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__itruediv__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_index" title="PyNumberMethods.nb_index"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_index</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><p>__index__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_matrix_multiply" title="PyNumberMethods.nb_matrix_multiply"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_matrix_multiply</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__matmul__
__rmatmul__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyNumberMethods.nb_inplace_matrix_multiply" title="PyNumberMethods.nb_inplace_matrix_multiply"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_matrix_multiply</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__imatmul__</p></td>
</tr>
<tr class="row-odd"><td colspan="3"></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyMappingMethods.mp_length" title="PyMappingMethods.mp_length"><code class="xref c c-member docutils literal notranslate"><span class="pre">mp_length</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.lenfunc" title="lenfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">lenfunc</span></code></a></p></td>
<td><p>__len__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyMappingMethods.mp_subscript" title="PyMappingMethods.mp_subscript"><code class="xref c c-member docutils literal notranslate"><span class="pre">mp_subscript</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__getitem__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyMappingMethods.mp_ass_subscript" title="PyMappingMethods.mp_ass_subscript"><code class="xref c c-member docutils literal notranslate"><span class="pre">mp_ass_subscript</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.objobjargproc" title="objobjargproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">objobjargproc</span></code></a></p></td>
<td><p>__setitem__,
__delitem__</p></td>
</tr>
<tr class="row-odd"><td colspan="3"></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PySequenceMethods.sq_length" title="PySequenceMethods.sq_length"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_length</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.lenfunc" title="lenfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">lenfunc</span></code></a></p></td>
<td><p>__len__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PySequenceMethods.sq_concat" title="PySequenceMethods.sq_concat"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_concat</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__add__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PySequenceMethods.sq_repeat" title="PySequenceMethods.sq_repeat"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_repeat</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.ssizeargfunc" title="ssizeargfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizeargfunc</span></code></a></p></td>
<td><p>__mul__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PySequenceMethods.sq_item" title="PySequenceMethods.sq_item"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_item</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.ssizeargfunc" title="ssizeargfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizeargfunc</span></code></a></p></td>
<td><p>__getitem__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PySequenceMethods.sq_ass_item" title="PySequenceMethods.sq_ass_item"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_ass_item</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.ssizeobjargproc" title="ssizeobjargproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizeobjargproc</span></code></a></p></td>
<td><p>__setitem__
__delitem__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PySequenceMethods.sq_contains" title="PySequenceMethods.sq_contains"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_contains</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.objobjproc" title="objobjproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">objobjproc</span></code></a></p></td>
<td><p>__contains__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PySequenceMethods.sq_inplace_concat" title="PySequenceMethods.sq_inplace_concat"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_inplace_concat</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><p>__iadd__</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PySequenceMethods.sq_inplace_repeat" title="PySequenceMethods.sq_inplace_repeat"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_inplace_repeat</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.ssizeargfunc" title="ssizeargfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizeargfunc</span></code></a></p></td>
<td><p>__imul__</p></td>
</tr>
<tr class="row-even"><td colspan="3"></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyBufferProcs.bf_getbuffer" title="PyBufferProcs.bf_getbuffer"><code class="xref c c-member docutils literal notranslate"><span class="pre">bf_getbuffer</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.getbufferproc" title="getbufferproc"><code class="xref c c-func docutils literal notranslate"><span class="pre">getbufferproc()</span></code></a></p></td>
<td><p>__buffer__</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyBufferProcs.bf_releasebuffer" title="PyBufferProcs.bf_releasebuffer"><code class="xref c c-member docutils literal notranslate"><span class="pre">bf_releasebuffer</span></code></a></p></td>
<td><p><a class="reference internal" href="#c.releasebufferproc" title="releasebufferproc"><code class="xref c c-func docutils literal notranslate"><span class="pre">releasebufferproc()</span></code></a></p></td>
<td><p>__release_buffer__</p></td>
</tr>
</tbody>
</table>
</section>
<section id="slot-typedefs">
<span id="slot-typedefs-table"></span><h3>slot typedefs<a class="headerlink" href="#slot-typedefs" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>typedef</p></th>
<th class="head"><p>Parameter Types</p></th>
<th class="head"><p>Return Type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#c.allocfunc" title="allocfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">allocfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.destructor" title="destructor"><code class="xref c c-type docutils literal notranslate"><span class="pre">destructor</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p>void</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.freefunc" title="freefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">freefunc</span></code></a></p></td>
<td><p>void *</p></td>
<td><p>void</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="gcsupport.html#c.traverseproc" title="traverseproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">traverseproc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="gcsupport.html#c.visitproc" title="visitproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">visitproc</span></code></a></div>
<div class="line">void *</div>
</div>
</td>
<td><p>int</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.newfunc" title="newfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">newfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.initproc" title="initproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">initproc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p>int</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.reprfunc" title="reprfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">reprfunc</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.getattrfunc" title="getattrfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getattrfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line">const char *</div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.setattrfunc" title="setattrfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">setattrfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line">const char *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p>int</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.getattrofunc" title="getattrofunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getattrofunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.setattrofunc" title="setattrofunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">setattrofunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p>int</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.descrgetfunc" title="descrgetfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">descrgetfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.descrsetfunc" title="descrsetfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">descrsetfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p>int</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.hashfunc" title="hashfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">hashfunc</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p>Py_hash_t</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.richcmpfunc" title="richcmpfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">richcmpfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line">int</div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.getiterfunc" title="getiterfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getiterfunc</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.iternextfunc" title="iternextfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">iternextfunc</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.lenfunc" title="lenfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">lenfunc</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.getbufferproc" title="getbufferproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getbufferproc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="buffer.html#c.Py_buffer" title="Py_buffer"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_buffer</span></code></a> *</div>
<div class="line">int</div>
</div>
</td>
<td><p>int</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.releasebufferproc" title="releasebufferproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">releasebufferproc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="buffer.html#c.Py_buffer" title="Py_buffer"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_buffer</span></code></a> *</div>
</div>
</td>
<td><p>void</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><code class="xref c c-type docutils literal notranslate"><span class="pre">inquiry</span></code></a></p></td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
<td><p>int</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.ternaryfunc" title="ternaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ternaryfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.ssizeargfunc" title="ssizeargfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizeargfunc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></div>
</div>
</td>
<td><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.ssizeobjargproc" title="ssizeobjargproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizeobjargproc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p>int</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.objobjproc" title="objobjproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">objobjproc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p>int</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.objobjargproc" title="objobjargproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">objobjargproc</span></code></a></p></td>
<td><div class="line-block">
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
<div class="line"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> *</div>
</div>
</td>
<td><p>int</p></td>
</tr>
</tbody>
</table>
<p>See <a class="reference internal" href="#id6"><span class="std std-ref">Slot Type typedefs</span></a> below for more detail.</p>
</section>
</section>
<section id="pytypeobject-definition">
<h2>PyTypeObject Definition<a class="headerlink" href="#pytypeobject-definition" title="Link to this heading">¶</a></h2>
<p>The structure definition for <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> can be found in
<code class="file docutils literal notranslate"><span class="pre">Include/cpython/object.h</span></code>.  For convenience of reference, this repeats the
definition found there:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="nc">_typeobject</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyObject_VAR_HEAD</span>
<span class="w">    </span><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">tp_name</span><span class="p">;</span><span class="w"> </span><span class="cm">/* For printing, in format &quot;&lt;module&gt;.&lt;name&gt;&quot; */</span>
<span class="w">    </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="n">tp_basicsize</span><span class="p">,</span><span class="w"> </span><span class="n">tp_itemsize</span><span class="p">;</span><span class="w"> </span><span class="cm">/* For allocation */</span>

<span class="w">    </span><span class="cm">/* Methods to implement standard operations */</span>

<span class="w">    </span><span class="n">destructor</span><span class="w"> </span><span class="n">tp_dealloc</span><span class="p">;</span>
<span class="w">    </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="n">tp_vectorcall_offset</span><span class="p">;</span>
<span class="w">    </span><span class="n">getattrfunc</span><span class="w"> </span><span class="n">tp_getattr</span><span class="p">;</span>
<span class="w">    </span><span class="n">setattrfunc</span><span class="w"> </span><span class="n">tp_setattr</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyAsyncMethods</span><span class="w"> </span><span class="o">*</span><span class="n">tp_as_async</span><span class="p">;</span><span class="w"> </span><span class="cm">/* formerly known as tp_compare (Python 2)</span>
<span class="cm">                                    or tp_reserved (Python 3) */</span>
<span class="w">    </span><span class="n">reprfunc</span><span class="w"> </span><span class="n">tp_repr</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* Method suites for standard classes */</span>

<span class="w">    </span><span class="n">PyNumberMethods</span><span class="w"> </span><span class="o">*</span><span class="n">tp_as_number</span><span class="p">;</span>
<span class="w">    </span><span class="n">PySequenceMethods</span><span class="w"> </span><span class="o">*</span><span class="n">tp_as_sequence</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyMappingMethods</span><span class="w"> </span><span class="o">*</span><span class="n">tp_as_mapping</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* More standard operations (here for binary compatibility) */</span>

<span class="w">    </span><span class="n">hashfunc</span><span class="w"> </span><span class="n">tp_hash</span><span class="p">;</span>
<span class="w">    </span><span class="n">ternaryfunc</span><span class="w"> </span><span class="n">tp_call</span><span class="p">;</span>
<span class="w">    </span><span class="n">reprfunc</span><span class="w"> </span><span class="n">tp_str</span><span class="p">;</span>
<span class="w">    </span><span class="n">getattrofunc</span><span class="w"> </span><span class="n">tp_getattro</span><span class="p">;</span>
<span class="w">    </span><span class="n">setattrofunc</span><span class="w"> </span><span class="n">tp_setattro</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* Functions to access object as input/output buffer */</span>
<span class="w">    </span><span class="n">PyBufferProcs</span><span class="w"> </span><span class="o">*</span><span class="n">tp_as_buffer</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* Flags to define presence of optional/expanded features */</span>
<span class="w">    </span><span class="kt">unsigned</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="n">tp_flags</span><span class="p">;</span>

<span class="w">    </span><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">tp_doc</span><span class="p">;</span><span class="w"> </span><span class="cm">/* Documentation string */</span>

<span class="w">    </span><span class="cm">/* Assigned meaning in release 2.0 */</span>
<span class="w">    </span><span class="cm">/* call function for all accessible objects */</span>
<span class="w">    </span><span class="n">traverseproc</span><span class="w"> </span><span class="n">tp_traverse</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* delete references to contained objects */</span>
<span class="w">    </span><span class="n">inquiry</span><span class="w"> </span><span class="n">tp_clear</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* Assigned meaning in release 2.1 */</span>
<span class="w">    </span><span class="cm">/* rich comparisons */</span>
<span class="w">    </span><span class="n">richcmpfunc</span><span class="w"> </span><span class="n">tp_richcompare</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* weak reference enabler */</span>
<span class="w">    </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="n">tp_weaklistoffset</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* Iterators */</span>
<span class="w">    </span><span class="n">getiterfunc</span><span class="w"> </span><span class="n">tp_iter</span><span class="p">;</span>
<span class="w">    </span><span class="n">iternextfunc</span><span class="w"> </span><span class="n">tp_iternext</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* Attribute descriptor and subclassing stuff */</span>
<span class="w">    </span><span class="k">struct</span><span class="w"> </span><span class="nc">PyMethodDef</span><span class="w"> </span><span class="o">*</span><span class="n">tp_methods</span><span class="p">;</span>
<span class="w">    </span><span class="k">struct</span><span class="w"> </span><span class="nc">PyMemberDef</span><span class="w"> </span><span class="o">*</span><span class="n">tp_members</span><span class="p">;</span>
<span class="w">    </span><span class="k">struct</span><span class="w"> </span><span class="nc">PyGetSetDef</span><span class="w"> </span><span class="o">*</span><span class="n">tp_getset</span><span class="p">;</span>
<span class="w">    </span><span class="c1">// Strong reference on a heap type, borrowed reference on a static type</span>
<span class="w">    </span><span class="k">struct</span><span class="w"> </span><span class="nc">_typeobject</span><span class="w"> </span><span class="o">*</span><span class="n">tp_base</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">tp_dict</span><span class="p">;</span>
<span class="w">    </span><span class="n">descrgetfunc</span><span class="w"> </span><span class="n">tp_descr_get</span><span class="p">;</span>
<span class="w">    </span><span class="n">descrsetfunc</span><span class="w"> </span><span class="n">tp_descr_set</span><span class="p">;</span>
<span class="w">    </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="n">tp_dictoffset</span><span class="p">;</span>
<span class="w">    </span><span class="n">initproc</span><span class="w"> </span><span class="n">tp_init</span><span class="p">;</span>
<span class="w">    </span><span class="n">allocfunc</span><span class="w"> </span><span class="n">tp_alloc</span><span class="p">;</span>
<span class="w">    </span><span class="n">newfunc</span><span class="w"> </span><span class="n">tp_new</span><span class="p">;</span>
<span class="w">    </span><span class="n">freefunc</span><span class="w"> </span><span class="n">tp_free</span><span class="p">;</span><span class="w"> </span><span class="cm">/* Low-level free-memory routine */</span>
<span class="w">    </span><span class="n">inquiry</span><span class="w"> </span><span class="n">tp_is_gc</span><span class="p">;</span><span class="w"> </span><span class="cm">/* For PyObject_IS_GC */</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">tp_bases</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">tp_mro</span><span class="p">;</span><span class="w"> </span><span class="cm">/* method resolution order */</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">tp_cache</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">tp_subclasses</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">tp_weaklist</span><span class="p">;</span>
<span class="w">    </span><span class="n">destructor</span><span class="w"> </span><span class="n">tp_del</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* Type attribute cache version tag. Added in version 2.6 */</span>
<span class="w">    </span><span class="kt">unsigned</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">tp_version_tag</span><span class="p">;</span>

<span class="w">    </span><span class="n">destructor</span><span class="w"> </span><span class="n">tp_finalize</span><span class="p">;</span>
<span class="w">    </span><span class="n">vectorcallfunc</span><span class="w"> </span><span class="n">tp_vectorcall</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* bitset of which type-watchers care about this type */</span>
<span class="w">    </span><span class="kt">unsigned</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="n">tp_watched</span><span class="p">;</span>
<span class="p">}</span><span class="w"> </span><span class="n">PyTypeObject</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="pyobject-slots">
<h2>PyObject Slots<a class="headerlink" href="#pyobject-slots" title="Link to this heading">¶</a></h2>
<p>The type object structure extends the <a class="reference internal" href="structures.html#c.PyVarObject" title="PyVarObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyVarObject</span></code></a> structure. The
<a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field is used for dynamic types (created by <code class="xref c c-func docutils literal notranslate"><span class="pre">type_new()</span></code>,
usually called from a class statement). Note that <a class="reference internal" href="type.html#c.PyType_Type" title="PyType_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyType_Type</span></code></a> (the
metatype) initializes <a class="reference internal" href="#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a>, which means that its instances (i.e.
type objects) <em>must</em> have the <a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field.</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyObject.ob_refcnt">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">ob_refcnt</span></span></span><a class="headerlink" href="#c.PyObject.ob_refcnt" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This is the type object’s reference count, initialized to <code class="docutils literal notranslate"><span class="pre">1</span></code> by the
<code class="docutils literal notranslate"><span class="pre">PyObject_HEAD_INIT</span></code> macro.  Note that for <a class="reference internal" href="#static-types"><span class="std std-ref">statically allocated type
objects</span></a>, the type’s instances (objects whose <a class="reference internal" href="#c.PyObject.ob_type" title="PyObject.ob_type"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_type</span></code></a>
points back to the type) do <em>not</em> count as references.  But for
<a class="reference internal" href="#heap-types"><span class="std std-ref">dynamically allocated type objects</span></a>, the instances <em>do</em>
count as references.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyObject.ob_type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">ob_type</span></span></span><a class="headerlink" href="#c.PyObject.ob_type" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This is the type’s type, in other words its metatype.  It is initialized by the
argument to the <code class="docutils literal notranslate"><span class="pre">PyObject_HEAD_INIT</span></code> macro, and its value should normally be
<code class="docutils literal notranslate"><span class="pre">&amp;PyType_Type</span></code>.  However, for dynamically loadable extension modules that must
be usable on Windows (at least), the compiler complains that this is not a valid
initializer.  Therefore, the convention is to pass <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to the
<code class="docutils literal notranslate"><span class="pre">PyObject_HEAD_INIT</span></code> macro and to initialize this field explicitly at the
start of the module’s initialization function, before doing anything else.  This
is typically done like this:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">Foo_Type</span><span class="p">.</span><span class="n">ob_type</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">&amp;</span><span class="n">PyType_Type</span><span class="p">;</span>
</pre></div>
</div>
<p>This should be done before any instances of the type are created.
<a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> checks if <a class="reference internal" href="#c.PyObject.ob_type" title="PyObject.ob_type"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_type</span></code></a> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, and if so,
initializes it to the <a class="reference internal" href="#c.PyObject.ob_type" title="PyObject.ob_type"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_type</span></code></a> field of the base class.
<a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> will not change this field if it is non-zero.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
</dd></dl>

</section>
<section id="pyvarobject-slots">
<h2>PyVarObject Slots<a class="headerlink" href="#pyvarobject-slots" title="Link to this heading">¶</a></h2>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyVarObject.ob_size">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="structures.html#c.PyVarObject" title="PyVarObject"><span class="n"><span class="pre">PyVarObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">ob_size</span></span></span><a class="headerlink" href="#c.PyVarObject.ob_size" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>For <a class="reference internal" href="#static-types"><span class="std std-ref">statically allocated type objects</span></a>, this should be
initialized to zero. For <a class="reference internal" href="#heap-types"><span class="std std-ref">dynamically allocated type objects</span></a>, this field has a special internal meaning.</p>
<p>This field should be accessed using the <a class="reference internal" href="structures.html#c.Py_SIZE" title="Py_SIZE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SIZE()</span></code></a> and
<a class="reference internal" href="structures.html#c.Py_SET_SIZE" title="Py_SET_SIZE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SET_SIZE()</span></code></a> macros.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited by subtypes.</p>
</dd></dl>

</section>
<section id="pytypeobject-slots">
<h2>PyTypeObject Slots<a class="headerlink" href="#pytypeobject-slots" title="Link to this heading">¶</a></h2>
<p>Each slot has a section describing inheritance.  If <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a>
may set a value when the field is set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> then there will also be
a “Default” section.  (Note that many fields set on <a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a>
and <a class="reference internal" href="type.html#c.PyType_Type" title="PyType_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyType_Type</span></code></a> effectively act as defaults.)</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_name">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_name</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_name" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pointer to a NUL-terminated string containing the name of the type. For types
that are accessible as module globals, the string should be the full module
name, followed by a dot, followed by the type name; for built-in types, it
should be just the type name.  If the module is a submodule of a package, the
full package name is part of the full module name.  For example, a type named
<code class="xref py py-class docutils literal notranslate"><span class="pre">T</span></code> defined in module <code class="xref py py-mod docutils literal notranslate"><span class="pre">M</span></code> in subpackage <code class="xref py py-mod docutils literal notranslate"><span class="pre">Q</span></code> in package <code class="xref py py-mod docutils literal notranslate"><span class="pre">P</span></code>
should have the <a class="reference internal" href="#c.PyTypeObject.tp_name" title="PyTypeObject.tp_name"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_name</span></code></a> initializer <code class="docutils literal notranslate"><span class="pre">&quot;P.Q.M.T&quot;</span></code>.</p>
<p>For <a class="reference internal" href="#heap-types"><span class="std std-ref">dynamically allocated type objects</span></a>,
this should just be the type name, and
the module name explicitly stored in the type dict as the value for key
<code class="docutils literal notranslate"><span class="pre">'__module__'</span></code>.</p>
<p>For <a class="reference internal" href="#static-types"><span class="std std-ref">statically allocated type objects</span></a>,
the <em>tp_name</em> field should contain a dot.
Everything before the last dot is made accessible as the <a class="reference internal" href="../reference/datamodel.html#type.__module__" title="type.__module__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__module__</span></code></a>
attribute, and everything after the last dot is made accessible as the
<a class="reference internal" href="../reference/datamodel.html#type.__name__" title="type.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> attribute.</p>
<p>If no dot is present, the entire <a class="reference internal" href="#c.PyTypeObject.tp_name" title="PyTypeObject.tp_name"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_name</span></code></a> field is made accessible as the
<a class="reference internal" href="../reference/datamodel.html#type.__name__" title="type.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> attribute, and the <a class="reference internal" href="../reference/datamodel.html#type.__module__" title="type.__module__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__module__</span></code></a> attribute is undefined
(unless explicitly set in the dictionary, as explained above).  This means your
type will be impossible to pickle.  Additionally, it will not be listed in
module documentations created with pydoc.</p>
<p>This field must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  It is the only required field
in <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTypeObject()</span></code></a> (other than potentially
<a class="reference internal" href="#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a>).</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_basicsize">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_basicsize</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_basicsize" title="Link to this definition">¶</a><br /></dt>
<dt class="sig sig-object c" id="c.PyTypeObject.tp_itemsize">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_itemsize</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_itemsize" title="Link to this definition">¶</a><br /></dt>
<dd><p>These fields allow calculating the size in bytes of instances of the type.</p>
<p>There are two kinds of types: types with fixed-length instances have a zero
<code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code> field, types with variable-length instances have a non-zero
<code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code> field.  For a type with fixed-length instances, all
instances have the same size, given in <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code>.
(Exceptions to this rule can be made using
<a class="reference internal" href="gcsupport.html#c.PyUnstable_Object_GC_NewWithExtraData" title="PyUnstable_Object_GC_NewWithExtraData"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnstable_Object_GC_NewWithExtraData()</span></code></a>.)</p>
<p>For a type with variable-length instances, the instances must have an
<a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field, and the instance size is
<code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code> plus N times <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code>,
where N is the “length” of the object.</p>
<p>Functions like <a class="reference internal" href="allocation.html#c.PyObject_NewVar" title="PyObject_NewVar"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_NewVar()</span></code></a> will take the value of N as an
argument, and store in the instance’s <a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field.
Note that the <a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field may later be used for
other purposes. For example, <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-type docutils literal notranslate"><span class="pre">int</span></code></a> instances use the bits of
<a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> in an implementation-defined
way; the underlying storage and its size should be accessed using
<code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_Export()</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field should be accessed using
the <a class="reference internal" href="structures.html#c.Py_SIZE" title="Py_SIZE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SIZE()</span></code></a> and <a class="reference internal" href="structures.html#c.Py_SET_SIZE" title="Py_SET_SIZE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SET_SIZE()</span></code></a> macros.</p>
</div>
<p>Also, the presence of an <a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field in the
instance layout doesn’t mean that the instance structure is variable-length.
For example, the <a class="reference internal" href="../library/stdtypes.html#list" title="list"><code class="xref py py-type docutils literal notranslate"><span class="pre">list</span></code></a> type has fixed-length instances, yet those
instances have a <a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field.
(As with <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-type docutils literal notranslate"><span class="pre">int</span></code></a>, avoid reading lists’ <code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code> directly.
Call <a class="reference internal" href="list.html#c.PyList_Size" title="PyList_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_Size()</span></code></a> instead.)</p>
<p>The <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code> includes size needed for data of the type’s
<a class="reference internal" href="#c.PyTypeObject.tp_base" title="PyTypeObject.tp_base"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_base</span></code></a>, plus any extra data needed
by each instance.</p>
<p>The  correct way to set <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code> is to use the
<code class="docutils literal notranslate"><span class="pre">sizeof</span></code> operator on the struct used to declare the instance layout.
This struct must include the struct used to declare the base type.
In other words, <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code> must be greater than or equal
to the base’s <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code>.</p>
<p>Since every type is a subtype of <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-type docutils literal notranslate"><span class="pre">object</span></code></a>, this struct must
include <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> or <a class="reference internal" href="structures.html#c.PyVarObject" title="PyVarObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyVarObject</span></code></a> (depending on
whether <a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> should be included). These are
usually defined by the macro <a class="reference internal" href="structures.html#c.PyObject_HEAD" title="PyObject_HEAD"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_HEAD</span></code></a> or
<a class="reference internal" href="structures.html#c.PyObject_VAR_HEAD" title="PyObject_VAR_HEAD"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_VAR_HEAD</span></code></a>, respectively.</p>
<p>The basic size does not include the GC header size, as that header is not
part of <a class="reference internal" href="structures.html#c.PyObject_HEAD" title="PyObject_HEAD"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_HEAD</span></code></a>.</p>
<p>For cases where struct used to declare the base type is unknown,
see <a class="reference internal" href="type.html#c.PyType_Spec.basicsize" title="PyType_Spec.basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyType_Spec.basicsize</span></code></a> and <a class="reference internal" href="type.html#c.PyType_FromMetaclass" title="PyType_FromMetaclass"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromMetaclass()</span></code></a>.</p>
<p>Notes about alignment:</p>
<ul class="simple">
<li><p><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code> must be a multiple of <code class="docutils literal notranslate"><span class="pre">_Alignof(PyObject)</span></code>.
When using <code class="docutils literal notranslate"><span class="pre">sizeof</span></code> on a <code class="docutils literal notranslate"><span class="pre">struct</span></code> that includes
<a class="reference internal" href="structures.html#c.PyObject_HEAD" title="PyObject_HEAD"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_HEAD</span></code></a>, as recommended, the compiler ensures this.
When not using a C <code class="docutils literal notranslate"><span class="pre">struct</span></code>, or when using compiler
extensions like <code class="docutils literal notranslate"><span class="pre">__attribute__((packed))</span></code>, it is up to you.</p></li>
<li><p>If the variable items require a particular alignment,
<code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code> and <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code> must each be a
multiple of that alignment.
For example, if a type’s variable part stores a <code class="docutils literal notranslate"><span class="pre">double</span></code>, it is
your responsibility that both fields are a multiple of
<code class="docutils literal notranslate"><span class="pre">_Alignof(double)</span></code>.</p></li>
</ul>
<p><strong>Inheritance:</strong></p>
<p>These fields are inherited separately by subtypes.
(That is, if the field is set to zero, <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> will copy
the value from the base type, indicating that the instances do not
need additional storage.)</p>
<p>If the base type has a non-zero <a class="reference internal" href="#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a>, it is generally not safe to set
<a class="reference internal" href="#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a> to a different non-zero value in a subtype (though this
depends on the implementation of the base type).</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_dealloc">
<a class="reference internal" href="#c.destructor" title="destructor"><span class="n"><span class="pre">destructor</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_dealloc</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_dealloc" title="Link to this definition">¶</a><br /></dt>
<dd><p>A pointer to the instance destructor function.  This function must be defined
unless the type guarantees that its instances will never be deallocated (as is
the case for the singletons <code class="docutils literal notranslate"><span class="pre">None</span></code> and <code class="docutils literal notranslate"><span class="pre">Ellipsis</span></code>).  The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">tp_dealloc</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>The destructor function is called by the <a class="reference internal" href="refcounting.html#c.Py_DECREF" title="Py_DECREF"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DECREF()</span></code></a> and
<a class="reference internal" href="refcounting.html#c.Py_XDECREF" title="Py_XDECREF"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_XDECREF()</span></code></a> macros when the new reference count is zero.  At this point,
the instance is still in existence, but there are no references to it.  The
destructor function should free all references which the instance owns, free all
memory buffers owned by the instance (using the freeing function corresponding
to the allocation function used to allocate the buffer), and call the type’s
<a class="reference internal" href="#c.PyTypeObject.tp_free" title="PyTypeObject.tp_free"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_free</span></code></a> function.  If the type is not subtypable
(doesn’t have the <a class="reference internal" href="#c.Py_TPFLAGS_BASETYPE" title="Py_TPFLAGS_BASETYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_BASETYPE</span></code></a> flag bit set), it is
permissible to call the object deallocator directly instead of via
<a class="reference internal" href="#c.PyTypeObject.tp_free" title="PyTypeObject.tp_free"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_free</span></code></a>.  The object deallocator should be the one used to allocate the
instance; this is normally <a class="reference internal" href="allocation.html#c.PyObject_Del" title="PyObject_Del"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Del()</span></code></a> if the instance was allocated
using <a class="reference internal" href="allocation.html#c.PyObject_New" title="PyObject_New"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_New</span></code></a> or <a class="reference internal" href="allocation.html#c.PyObject_NewVar" title="PyObject_NewVar"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_NewVar</span></code></a>, or
<a class="reference internal" href="gcsupport.html#c.PyObject_GC_Del" title="PyObject_GC_Del"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GC_Del()</span></code></a> if the instance was allocated using
<a class="reference internal" href="gcsupport.html#c.PyObject_GC_New" title="PyObject_GC_New"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_GC_New</span></code></a> or <a class="reference internal" href="gcsupport.html#c.PyObject_GC_NewVar" title="PyObject_GC_NewVar"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_GC_NewVar</span></code></a>.</p>
<p>If the type supports garbage collection (has the <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a>
flag bit set), the destructor should call <a class="reference internal" href="gcsupport.html#c.PyObject_GC_UnTrack" title="PyObject_GC_UnTrack"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GC_UnTrack()</span></code></a>
before clearing any member fields.</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">foo_dealloc</span><span class="p">(</span><span class="n">foo_object</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyObject_GC_UnTrack</span><span class="p">(</span><span class="n">self</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_CLEAR</span><span class="p">(</span><span class="n">self</span><span class="o">-&gt;</span><span class="n">ref</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_TYPE</span><span class="p">(</span><span class="n">self</span><span class="p">)</span><span class="o">-&gt;</span><span class="n">tp_free</span><span class="p">((</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="n">self</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Finally, if the type is heap allocated (<a class="reference internal" href="#c.Py_TPFLAGS_HEAPTYPE" title="Py_TPFLAGS_HEAPTYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HEAPTYPE</span></code></a>), the
deallocator should release the owned reference to its type object
(via <a class="reference internal" href="refcounting.html#c.Py_DECREF" title="Py_DECREF"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DECREF()</span></code></a>)  after
calling the type deallocator. In order to avoid dangling pointers, the
recommended way to achieve this is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">foo_dealloc</span><span class="p">(</span><span class="n">foo_object</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyTypeObject</span><span class="w"> </span><span class="o">*</span><span class="n">tp</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_TYPE</span><span class="p">(</span><span class="n">self</span><span class="p">);</span>
<span class="w">    </span><span class="c1">// free references and buffers here</span>
<span class="w">    </span><span class="n">tp</span><span class="o">-&gt;</span><span class="n">tp_free</span><span class="p">(</span><span class="n">self</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_DECREF</span><span class="p">(</span><span class="n">tp</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>In a garbage collected Python, <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dealloc</span></code> may be called from
any Python thread, not just the thread which created the object (if the
object becomes part of a refcount cycle, that cycle might be collected by
a garbage collection on any thread).  This is not a problem for Python
API calls, since the thread on which <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dealloc</span></code> is called
will own the Global Interpreter Lock (GIL).  However, if the object being
destroyed in turn destroys objects from some other C or C++ library, care
should be taken to ensure that destroying those objects on the thread
which called <code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dealloc</span></code> will not violate any assumptions of
the library.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_vectorcall_offset">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_vectorcall_offset</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_vectorcall_offset" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional offset to a per-instance function that implements calling
the object using the <a class="reference internal" href="call.html#vectorcall"><span class="std std-ref">vectorcall protocol</span></a>,
a more efficient alternative
of the simpler <a class="reference internal" href="#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a>.</p>
<p>This field is only used if the flag <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_VECTORCALL" title="Py_TPFLAGS_HAVE_VECTORCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></code></a>
is set. If so, this must be a positive integer containing the offset in the
instance of a <a class="reference internal" href="call.html#c.vectorcallfunc" title="vectorcallfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">vectorcallfunc</span></code></a> pointer.</p>
<p>The <em>vectorcallfunc</em> pointer may be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, in which case the instance behaves
as if <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_VECTORCALL" title="Py_TPFLAGS_HAVE_VECTORCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></code></a> was not set: calling the instance
falls back to <a class="reference internal" href="#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a>.</p>
<p>Any class that sets <code class="docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></code> must also set
<a class="reference internal" href="#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a> and make sure its behaviour is consistent
with the <em>vectorcallfunc</em> function.
This can be done by setting <em>tp_call</em> to <a class="reference internal" href="call.html#c.PyVectorcall_Call" title="PyVectorcall_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyVectorcall_Call()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Before version 3.8, this slot was named <code class="docutils literal notranslate"><span class="pre">tp_print</span></code>.
In Python 2.x, it was used for printing to a file.
In Python 3.0 to 3.7, it was unused.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Before version 3.12, it was not recommended for
<a class="reference internal" href="#heap-types"><span class="std std-ref">mutable heap types</span></a> to implement the vectorcall
protocol.
When a user sets <a class="reference internal" href="../reference/datamodel.html#object.__call__" title="object.__call__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__call__</span></code></a> in Python code, only <em>tp_call</em> is
updated, likely making it inconsistent with the vectorcall function.
Since 3.12, setting <code class="docutils literal notranslate"><span class="pre">__call__</span></code> will disable vectorcall optimization
by clearing the <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_VECTORCALL" title="Py_TPFLAGS_HAVE_VECTORCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></code></a> flag.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is always inherited.
However, the <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_VECTORCALL" title="Py_TPFLAGS_HAVE_VECTORCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></code></a> flag is not
always inherited. If it’s not set, then the subclass won’t use
<a class="reference internal" href="call.html#vectorcall"><span class="std std-ref">vectorcall</span></a>, except when
<a class="reference internal" href="call.html#c.PyVectorcall_Call" title="PyVectorcall_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyVectorcall_Call()</span></code></a> is explicitly called.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_getattr">
<a class="reference internal" href="#c.getattrfunc" title="getattrfunc"><span class="n"><span class="pre">getattrfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_getattr</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_getattr" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to the get-attribute-string function.</p>
<p>This field is deprecated.  When it is defined, it should point to a function
that acts the same as the <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a> function, but taking a C string
instead of a Python string object to give the attribute name.</p>
<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.PyTypeObject.tp_getattr" title="PyTypeObject.tp_getattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattr</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a></p>
<p>This field is inherited by subtypes together with <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a>: a subtype
inherits both <a class="reference internal" href="#c.PyTypeObject.tp_getattr" title="PyTypeObject.tp_getattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattr</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a> from its base type when
the subtype’s <a class="reference internal" href="#c.PyTypeObject.tp_getattr" title="PyTypeObject.tp_getattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattr</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a> are both <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_setattr">
<a class="reference internal" href="#c.setattrfunc" title="setattrfunc"><span class="n"><span class="pre">setattrfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_setattr</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_setattr" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to the function for setting and deleting attributes.</p>
<p>This field is deprecated.  When it is defined, it should point to a function
that acts the same as the <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a> function, but taking a C string
instead of a Python string object to give the attribute name.</p>
<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.PyTypeObject.tp_setattr" title="PyTypeObject.tp_setattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattr</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a></p>
<p>This field is inherited by subtypes together with <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a>: a subtype
inherits both <a class="reference internal" href="#c.PyTypeObject.tp_setattr" title="PyTypeObject.tp_setattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattr</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a> from its base type when
the subtype’s <a class="reference internal" href="#c.PyTypeObject.tp_setattr" title="PyTypeObject.tp_setattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattr</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a> are both <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_as_async">
<a class="reference internal" href="#c.PyAsyncMethods" title="PyAsyncMethods"><span class="n"><span class="pre">PyAsyncMethods</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_as_async</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_as_async" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pointer to an additional structure that contains fields relevant only to
objects which implement <a class="reference internal" href="../glossary.html#term-awaitable"><span class="xref std std-term">awaitable</span></a> and <a class="reference internal" href="../glossary.html#term-asynchronous-iterator"><span class="xref std std-term">asynchronous iterator</span></a>
protocols at the C-level.  See <a class="reference internal" href="#async-structs"><span class="std std-ref">Async Object Structures</span></a> for details.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.5: </span>Formerly known as <code class="docutils literal notranslate"><span class="pre">tp_compare</span></code> and <code class="docutils literal notranslate"><span class="pre">tp_reserved</span></code>.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_as_async" title="PyTypeObject.tp_as_async"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_async</span></code></a> field is not inherited,
but the contained fields are inherited individually.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_repr">
<a class="reference internal" href="#c.reprfunc" title="reprfunc"><span class="n"><span class="pre">reprfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_repr</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_repr" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-0">An optional pointer to a function that implements the built-in function
<a class="reference internal" href="../library/functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a>.</p>
<p>The signature is the same as for <a class="reference internal" href="object.html#c.PyObject_Repr" title="PyObject_Repr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Repr()</span></code></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_repr</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>The function must return a string or a Unicode object.  Ideally,
this function should return a string that, when passed to
<a class="reference internal" href="../library/functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>, given a suitable environment, returns an object with the
same value.  If this is not feasible, it should return a string starting with
<code class="docutils literal notranslate"><span class="pre">'&lt;'</span></code> and ending with <code class="docutils literal notranslate"><span class="pre">'&gt;'</span></code> from which both the type and the value of the
object can be deduced.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
<p><strong>Default:</strong></p>
<p>When this field is not set, a string of the form <code class="docutils literal notranslate"><span class="pre">&lt;%s</span> <span class="pre">object</span> <span class="pre">at</span> <span class="pre">%p&gt;</span></code> is
returned, where <code class="docutils literal notranslate"><span class="pre">%s</span></code> is replaced by the type name, and <code class="docutils literal notranslate"><span class="pre">%p</span></code> by the object’s
memory address.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_as_number">
<a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_as_number</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_as_number" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pointer to an additional structure that contains fields relevant only to
objects which implement the number protocol.  These fields are documented in
<a class="reference internal" href="#number-structs"><span class="std std-ref">Number Object Structures</span></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_as_number" title="PyTypeObject.tp_as_number"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_number</span></code></a> field is not inherited, but the contained fields are
inherited individually.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_as_sequence">
<a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_as_sequence</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_as_sequence" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pointer to an additional structure that contains fields relevant only to
objects which implement the sequence protocol.  These fields are documented
in <a class="reference internal" href="#sequence-structs"><span class="std std-ref">Sequence Object Structures</span></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_as_sequence" title="PyTypeObject.tp_as_sequence"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_sequence</span></code></a> field is not inherited, but the contained fields
are inherited individually.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_as_mapping">
<a class="reference internal" href="#c.PyMappingMethods" title="PyMappingMethods"><span class="n"><span class="pre">PyMappingMethods</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_as_mapping</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_as_mapping" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pointer to an additional structure that contains fields relevant only to
objects which implement the mapping protocol.  These fields are documented in
<a class="reference internal" href="#mapping-structs"><span class="std std-ref">Mapping Object Structures</span></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_as_mapping" title="PyTypeObject.tp_as_mapping"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_mapping</span></code></a> field is not inherited, but the contained fields
are inherited individually.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_hash">
<a class="reference internal" href="#c.hashfunc" title="hashfunc"><span class="n"><span class="pre">hashfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_hash</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_hash" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-1">An optional pointer to a function that implements the built-in function
<a class="reference internal" href="../library/functions.html#hash" title="hash"><code class="xref py py-func docutils literal notranslate"><span class="pre">hash()</span></code></a>.</p>
<p>The signature is the same as for <a class="reference internal" href="object.html#c.PyObject_Hash" title="PyObject_Hash"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Hash()</span></code></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">Py_hash_t</span><span class="w"> </span><span class="nf">tp_hash</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="p">);</span>
</pre></div>
</div>
<p>The value <code class="docutils literal notranslate"><span class="pre">-1</span></code> should not be returned as a
normal return value; when an error occurs during the computation of the hash
value, the function should set an exception and return <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p>
<p>When this field is not set (<em>and</em> <a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a> is not set),
an attempt to take the hash of the object raises <a class="reference internal" href="../library/exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.
This is the same as setting it to <a class="reference internal" href="object.html#c.PyObject_HashNotImplemented" title="PyObject_HashNotImplemented"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_HashNotImplemented()</span></code></a>.</p>
<p>This field can be set explicitly to <a class="reference internal" href="object.html#c.PyObject_HashNotImplemented" title="PyObject_HashNotImplemented"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_HashNotImplemented()</span></code></a> to
block inheritance of the hash method from a parent type. This is interpreted
as the equivalent of <code class="docutils literal notranslate"><span class="pre">__hash__</span> <span class="pre">=</span> <span class="pre">None</span></code> at the Python level, causing
<code class="docutils literal notranslate"><span class="pre">isinstance(o,</span> <span class="pre">collections.Hashable)</span></code> to correctly return <code class="docutils literal notranslate"><span class="pre">False</span></code>. Note
that the converse is also true - setting <code class="docutils literal notranslate"><span class="pre">__hash__</span> <span class="pre">=</span> <span class="pre">None</span></code> on a class at
the Python level will result in the <code class="docutils literal notranslate"><span class="pre">tp_hash</span></code> slot being set to
<a class="reference internal" href="object.html#c.PyObject_HashNotImplemented" title="PyObject_HashNotImplemented"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_HashNotImplemented()</span></code></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a></p>
<p>This field is inherited by subtypes together with
<a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a>: a subtype inherits both of
<a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a>, when the subtype’s
<a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a> are both <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p><strong>Default:</strong></p>
<p><a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a> uses <a class="reference internal" href="hash.html#c.PyObject_GenericHash" title="PyObject_GenericHash"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericHash()</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_call">
<a class="reference internal" href="#c.ternaryfunc" title="ternaryfunc"><span class="n"><span class="pre">ternaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_call</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_call" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a function that implements calling the object.  This
should be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if the object is not callable.  The signature is the same as
for <a class="reference internal" href="call.html#c.PyObject_Call" title="PyObject_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Call()</span></code></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_call</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">kwargs</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_str">
<a class="reference internal" href="#c.reprfunc" title="reprfunc"><span class="n"><span class="pre">reprfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_str</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_str" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a function that implements the built-in operation
<a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a>.  (Note that <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> is a type now, and <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> calls the
constructor for that type.  This constructor calls <a class="reference internal" href="object.html#c.PyObject_Str" title="PyObject_Str"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Str()</span></code></a> to do
the actual work, and <a class="reference internal" href="object.html#c.PyObject_Str" title="PyObject_Str"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Str()</span></code></a> will call this handler.)</p>
<p>The signature is the same as for <a class="reference internal" href="object.html#c.PyObject_Str" title="PyObject_Str"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Str()</span></code></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_str</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>The function must return a string or a Unicode object.  It should be a “friendly” string
representation of the object, as this is the representation that will be used,
among other things, by the <a class="reference internal" href="../library/functions.html#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a> function.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
<p><strong>Default:</strong></p>
<p>When this field is not set, <a class="reference internal" href="object.html#c.PyObject_Repr" title="PyObject_Repr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Repr()</span></code></a> is called to return a string
representation.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_getattro">
<a class="reference internal" href="#c.getattrofunc" title="getattrofunc"><span class="n"><span class="pre">getattrofunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_getattro</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_getattro" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to the get-attribute function.</p>
<p>The signature is the same as for <a class="reference internal" href="object.html#c.PyObject_GetAttr" title="PyObject_GetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetAttr()</span></code></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_getattro</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">attr</span><span class="p">);</span>
</pre></div>
</div>
<p>It is usually convenient to set this field to <a class="reference internal" href="object.html#c.PyObject_GenericGetAttr" title="PyObject_GenericGetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericGetAttr()</span></code></a>,
which implements the normal way of looking for object attributes.</p>
<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.PyTypeObject.tp_getattr" title="PyTypeObject.tp_getattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattr</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a></p>
<p>This field is inherited by subtypes together with <a class="reference internal" href="#c.PyTypeObject.tp_getattr" title="PyTypeObject.tp_getattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattr</span></code></a>: a subtype
inherits both <a class="reference internal" href="#c.PyTypeObject.tp_getattr" title="PyTypeObject.tp_getattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattr</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a> from its base type when
the subtype’s <a class="reference internal" href="#c.PyTypeObject.tp_getattr" title="PyTypeObject.tp_getattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattr</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a> are both <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p><strong>Default:</strong></p>
<p><a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a> uses <a class="reference internal" href="object.html#c.PyObject_GenericGetAttr" title="PyObject_GenericGetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericGetAttr()</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_setattro">
<a class="reference internal" href="#c.setattrofunc" title="setattrofunc"><span class="n"><span class="pre">setattrofunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_setattro</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_setattro" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to the function for setting and deleting attributes.</p>
<p>The signature is the same as for <a class="reference internal" href="object.html#c.PyObject_SetAttr" title="PyObject_SetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SetAttr()</span></code></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">int</span><span class="w"> </span><span class="nf">tp_setattro</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">attr</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">value</span><span class="p">);</span>
</pre></div>
</div>
<p>In addition, setting <em>value</em> to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to delete an attribute must be
supported.  It is usually convenient to set this field to
<a class="reference internal" href="object.html#c.PyObject_GenericSetAttr" title="PyObject_GenericSetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericSetAttr()</span></code></a>, which implements the normal
way of setting object attributes.</p>
<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.PyTypeObject.tp_setattr" title="PyTypeObject.tp_setattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattr</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a></p>
<p>This field is inherited by subtypes together with <a class="reference internal" href="#c.PyTypeObject.tp_setattr" title="PyTypeObject.tp_setattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattr</span></code></a>: a subtype
inherits both <a class="reference internal" href="#c.PyTypeObject.tp_setattr" title="PyTypeObject.tp_setattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattr</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a> from its base type when
the subtype’s <a class="reference internal" href="#c.PyTypeObject.tp_setattr" title="PyTypeObject.tp_setattr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattr</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a> are both <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p><strong>Default:</strong></p>
<p><a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a> uses <a class="reference internal" href="object.html#c.PyObject_GenericSetAttr" title="PyObject_GenericSetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericSetAttr()</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_as_buffer">
<a class="reference internal" href="#c.PyBufferProcs" title="PyBufferProcs"><span class="n"><span class="pre">PyBufferProcs</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_as_buffer</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_as_buffer" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pointer to an additional structure that contains fields relevant only to objects
which implement the buffer interface.  These fields are documented in
<a class="reference internal" href="#buffer-structs"><span class="std std-ref">Buffer Object Structures</span></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_as_buffer" title="PyTypeObject.tp_as_buffer"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_buffer</span></code></a> field is not inherited,
but the contained fields are inherited individually.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_flags">
<span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">long</span></span><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_flags</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_flags" title="Link to this definition">¶</a><br /></dt>
<dd><p>This field is a bit mask of various flags.  Some flags indicate variant
semantics for certain situations; others are used to indicate that certain
fields in the type object (or in the extension structures referenced via
<a class="reference internal" href="#c.PyTypeObject.tp_as_number" title="PyTypeObject.tp_as_number"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_number</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_as_sequence" title="PyTypeObject.tp_as_sequence"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_sequence</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_as_mapping" title="PyTypeObject.tp_as_mapping"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_mapping</span></code></a>, and
<a class="reference internal" href="#c.PyTypeObject.tp_as_buffer" title="PyTypeObject.tp_as_buffer"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_as_buffer</span></code></a>) that were historically not always present are valid; if
such a flag bit is clear, the type fields it guards must not be accessed and
must be considered to have a zero or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> value instead.</p>
<p><strong>Inheritance:</strong></p>
<p>Inheritance of this field is complicated.  Most flag bits are inherited
individually, i.e. if the base type has a flag bit set, the subtype inherits
this flag bit.  The flag bits that pertain to extension structures are strictly
inherited if the extension structure is inherited, i.e. the base type’s value of
the flag bit is copied into the subtype together with a pointer to the extension
structure.  The <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit is inherited together with
the <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> fields, i.e. if the
<a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit is clear in the subtype and the
<a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> fields in the subtype exist and have
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> values.
.. XXX are most flag bits <em>really</em> inherited individually?</p>
<p><strong>Default:</strong></p>
<p><a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a> uses
<code class="docutils literal notranslate"><span class="pre">Py_TPFLAGS_DEFAULT</span> <span class="pre">|</span> <span class="pre">Py_TPFLAGS_BASETYPE</span></code>.</p>
<p><strong>Bit Masks:</strong></p>
<p>The following bit masks are currently defined; these can be ORed together using
the <code class="docutils literal notranslate"><span class="pre">|</span></code> operator to form the value of the <a class="reference internal" href="#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a> field.  The macro
<a class="reference internal" href="type.html#c.PyType_HasFeature" title="PyType_HasFeature"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_HasFeature()</span></code></a> takes a type and a flags value, <em>tp</em> and <em>f</em>, and
checks whether <code class="docutils literal notranslate"><span class="pre">tp-&gt;tp_flags</span> <span class="pre">&amp;</span> <span class="pre">f</span></code> is non-zero.</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_HEAPTYPE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_HEAPTYPE</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_HEAPTYPE" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit is set when the type object itself is allocated on the heap, for
example, types created dynamically using <a class="reference internal" href="type.html#c.PyType_FromSpec" title="PyType_FromSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpec()</span></code></a>.  In this
case, the <a class="reference internal" href="#c.PyObject.ob_type" title="PyObject.ob_type"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_type</span></code></a> field of its instances is considered a reference to
the type, and the type object is INCREF’ed when a new instance is created, and
DECREF’ed when an instance is destroyed (this does not apply to instances of
subtypes; only the type referenced by the instance’s ob_type gets INCREF’ed or
DECREF’ed). Heap types should also <a class="reference internal" href="gcsupport.html#supporting-cycle-detection"><span class="std std-ref">support garbage collection</span></a>
as they can form a reference cycle with their own module object.</p>
<p><strong>Inheritance:</strong></p>
<p>???</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_BASETYPE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_BASETYPE</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_BASETYPE" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit is set when the type can be used as the base type of another type.  If
this bit is clear, the type cannot be subtyped (similar to a “final” class in
Java).</p>
<p><strong>Inheritance:</strong></p>
<p>???</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_READY">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_READY</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_READY" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit is set when the type object has been fully initialized by
<a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>???</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_READYING">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_READYING</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_READYING" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit is set while <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> is in the process of initializing
the type object.</p>
<p><strong>Inheritance:</strong></p>
<p>???</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_HAVE_GC">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_HAVE_GC</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_HAVE_GC" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit is set when the object supports garbage collection.  If this bit
is set, instances must be created using <a class="reference internal" href="gcsupport.html#c.PyObject_GC_New" title="PyObject_GC_New"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyObject_GC_New</span></code></a> and
destroyed using <a class="reference internal" href="gcsupport.html#c.PyObject_GC_Del" title="PyObject_GC_Del"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GC_Del()</span></code></a>.  More information in section
<a class="reference internal" href="gcsupport.html#supporting-cycle-detection"><span class="std std-ref">Supporting Cyclic Garbage Collection</span></a>.  This bit also implies that the
GC-related fields <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> are present in
the type object.</p>
<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a></p>
<p>The <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit is inherited
together with the <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a>
fields, i.e.  if the <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit is
clear in the subtype and the <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> and
<a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> fields in the subtype exist and have <code class="docutils literal notranslate"><span class="pre">NULL</span></code>
values.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_DEFAULT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_DEFAULT</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_DEFAULT" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a bitmask of all the bits that pertain to the existence of certain
fields in the type object and its extension structures. Currently, it includes
the following bits: <code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_STACKLESS_EXTENSION</span></code>.</p>
<p><strong>Inheritance:</strong></p>
<p>???</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_METHOD_DESCRIPTOR">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_METHOD_DESCRIPTOR</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_METHOD_DESCRIPTOR" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit indicates that objects behave like unbound methods.</p>
<p>If this flag is set for <code class="docutils literal notranslate"><span class="pre">type(meth)</span></code>, then:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">meth.__get__(obj,</span> <span class="pre">cls)(*args,</span> <span class="pre">**kwds)</span></code> (with <code class="docutils literal notranslate"><span class="pre">obj</span></code> not None)
must be equivalent to <code class="docutils literal notranslate"><span class="pre">meth(obj,</span> <span class="pre">*args,</span> <span class="pre">**kwds)</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">meth.__get__(None,</span> <span class="pre">cls)(*args,</span> <span class="pre">**kwds)</span></code>
must be equivalent to <code class="docutils literal notranslate"><span class="pre">meth(*args,</span> <span class="pre">**kwds)</span></code>.</p></li>
</ul>
<p>This flag enables an optimization for typical method calls like
<code class="docutils literal notranslate"><span class="pre">obj.meth()</span></code>: it avoids creating a temporary “bound method” object for
<code class="docutils literal notranslate"><span class="pre">obj.meth</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.8.</span></p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This flag is never inherited by types without the
<a class="reference internal" href="#c.Py_TPFLAGS_IMMUTABLETYPE" title="Py_TPFLAGS_IMMUTABLETYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_IMMUTABLETYPE</span></code></a> flag set.  For extension types, it is
inherited whenever <a class="reference internal" href="#c.PyTypeObject.tp_descr_get" title="PyTypeObject.tp_descr_get"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_descr_get</span></code></a> is inherited.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_MANAGED_DICT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_MANAGED_DICT" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit indicates that instances of the class have a <a class="reference internal" href="../reference/datamodel.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a>
attribute, and that the space for the dictionary is managed by the VM.</p>
<p>If this flag is set, <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> should also be set.</p>
<p>The type traverse function must call <a class="reference internal" href="object.html#c.PyObject_VisitManagedDict" title="PyObject_VisitManagedDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_VisitManagedDict()</span></code></a>
and its clear function must call <a class="reference internal" href="object.html#c.PyObject_ClearManagedDict" title="PyObject_ClearManagedDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_ClearManagedDict()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This flag is inherited unless the
<a class="reference internal" href="#c.PyTypeObject.tp_dictoffset" title="PyTypeObject.tp_dictoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dictoffset</span></code></a> field is set in a superclass.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_MANAGED_WEAKREF">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_MANAGED_WEAKREF</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_MANAGED_WEAKREF" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit indicates that instances of the class should be weakly
referenceable.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This flag is inherited unless the
<a class="reference internal" href="#c.PyTypeObject.tp_weaklistoffset" title="PyTypeObject.tp_weaklistoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklistoffset</span></code></a> field is set in a superclass.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_ITEMS_AT_END">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_ITEMS_AT_END</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_ITEMS_AT_END" title="Link to this definition">¶</a><br /></dt>
<dd><p>Only usable with variable-size types, i.e. ones with non-zero
<a class="reference internal" href="#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a>.</p>
<p>Indicates that the variable-sized portion of an instance of this type is
at the end of the instance’s memory area, at an offset of
<code class="docutils literal notranslate"><span class="pre">Py_TYPE(obj)-&gt;tp_basicsize</span></code> (which may be different in each
subclass).</p>
<p>When setting this flag, be sure that all superclasses either
use this memory layout, or are not variable-sized.
Python does not check this.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This flag is inherited.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_LONG_SUBCLASS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_LONG_SUBCLASS</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_LONG_SUBCLASS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_LIST_SUBCLASS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_LIST_SUBCLASS</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_LIST_SUBCLASS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_TUPLE_SUBCLASS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_TUPLE_SUBCLASS</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_TUPLE_SUBCLASS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_BYTES_SUBCLASS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_BYTES_SUBCLASS</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_BYTES_SUBCLASS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_UNICODE_SUBCLASS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_UNICODE_SUBCLASS</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_UNICODE_SUBCLASS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_DICT_SUBCLASS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_DICT_SUBCLASS</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_DICT_SUBCLASS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_BASE_EXC_SUBCLASS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_BASE_EXC_SUBCLASS</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_BASE_EXC_SUBCLASS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_TYPE_SUBCLASS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_TYPE_SUBCLASS</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_TYPE_SUBCLASS" title="Link to this definition">¶</a><br /></dt>
<dd><p>These flags are used by functions such as
<a class="reference internal" href="long.html#c.PyLong_Check" title="PyLong_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_Check()</span></code></a> to quickly determine if a type is a subclass
of a built-in type; such specific checks are faster than a generic
check, like <a class="reference internal" href="object.html#c.PyObject_IsInstance" title="PyObject_IsInstance"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_IsInstance()</span></code></a>. Custom types that inherit
from built-ins should have their <a class="reference internal" href="#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a>
set appropriately, or the code that interacts with such types
will behave differently depending on what kind of check is used.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_HAVE_FINALIZE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_HAVE_FINALIZE</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_HAVE_FINALIZE" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit is set when the <a class="reference internal" href="#c.PyTypeObject.tp_finalize" title="PyTypeObject.tp_finalize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_finalize</span></code></a> slot is present in the
type structure.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.4.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.8: </span>This flag isn’t necessary anymore, as the interpreter assumes the
<a class="reference internal" href="#c.PyTypeObject.tp_finalize" title="PyTypeObject.tp_finalize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_finalize</span></code></a> slot is always present in the
type structure.</p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_HAVE_VECTORCALL">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_HAVE_VECTORCALL" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit is set when the class implements
the <a class="reference internal" href="call.html#vectorcall"><span class="std std-ref">vectorcall protocol</span></a>.
See <a class="reference internal" href="#c.PyTypeObject.tp_vectorcall_offset" title="PyTypeObject.tp_vectorcall_offset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_vectorcall_offset</span></code></a> for details.</p>
<p><strong>Inheritance:</strong></p>
<p>This bit is inherited if <a class="reference internal" href="#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a> is also
inherited.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>This flag is now removed from a class when the class’s
<a class="reference internal" href="../reference/datamodel.html#object.__call__" title="object.__call__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__call__()</span></code></a> method is reassigned.</p>
<p>This flag can now be inherited by mutable classes.</p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_IMMUTABLETYPE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_IMMUTABLETYPE</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_IMMUTABLETYPE" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit is set for type objects that are immutable: type attributes cannot be set nor deleted.</p>
<p><a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> automatically applies this flag to
<a class="reference internal" href="#static-types"><span class="std std-ref">static types</span></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>This flag is not inherited.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_DISALLOW_INSTANTIATION">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_DISALLOW_INSTANTIATION</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_DISALLOW_INSTANTIATION" title="Link to this definition">¶</a><br /></dt>
<dd><p>Disallow creating instances of the type: set
<a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> to NULL and don’t create the <code class="docutils literal notranslate"><span class="pre">__new__</span></code>
key in the type dictionary.</p>
<p>The flag must be set before creating the type, not after. For example, it
must be set before <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> is called on the type.</p>
<p>The flag is set automatically on <a class="reference internal" href="#static-types"><span class="std std-ref">static types</span></a> if
<a class="reference internal" href="#c.PyTypeObject.tp_base" title="PyTypeObject.tp_base"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_base</span></code></a> is NULL or <code class="docutils literal notranslate"><span class="pre">&amp;PyBaseObject_Type</span></code> and
<a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> is NULL.</p>
<p><strong>Inheritance:</strong></p>
<p>This flag is not inherited.
However, subclasses will not be instantiable unless they provide a
non-NULL <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> (which is only possible
via the C API).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>To disallow instantiating a class directly but allow instantiating
its subclasses (e.g. for an <a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">abstract base class</span></a>),
do not use this flag.
Instead, make <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> only succeed for
subclasses.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_MAPPING">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_MAPPING</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_MAPPING" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit indicates that instances of the class may match mapping patterns
when used as the subject of a <a class="reference internal" href="../reference/compound_stmts.html#match"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">match</span></code></a> block. It is automatically
set when registering or subclassing <a class="reference internal" href="../library/collections.abc.html#collections.abc.Mapping" title="collections.abc.Mapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Mapping</span></code></a>, and
unset when registering <a class="reference internal" href="../library/collections.abc.html#collections.abc.Sequence" title="collections.abc.Sequence"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Sequence</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#c.Py_TPFLAGS_MAPPING" title="Py_TPFLAGS_MAPPING"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MAPPING</span></code></a> and <a class="reference internal" href="#c.Py_TPFLAGS_SEQUENCE" title="Py_TPFLAGS_SEQUENCE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_SEQUENCE</span></code></a> are
mutually exclusive; it is an error to enable both flags simultaneously.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This flag is inherited by types that do not already set
<a class="reference internal" href="#c.Py_TPFLAGS_SEQUENCE" title="Py_TPFLAGS_SEQUENCE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_SEQUENCE</span></code></a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0634/"><strong>PEP 634</strong></a> – Structural Pattern Matching: Specification</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_SEQUENCE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_SEQUENCE</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_SEQUENCE" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit indicates that instances of the class may match sequence patterns
when used as the subject of a <a class="reference internal" href="../reference/compound_stmts.html#match"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">match</span></code></a> block. It is automatically
set when registering or subclassing <a class="reference internal" href="../library/collections.abc.html#collections.abc.Sequence" title="collections.abc.Sequence"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Sequence</span></code></a>, and
unset when registering <a class="reference internal" href="../library/collections.abc.html#collections.abc.Mapping" title="collections.abc.Mapping"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Mapping</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#c.Py_TPFLAGS_MAPPING" title="Py_TPFLAGS_MAPPING"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MAPPING</span></code></a> and <a class="reference internal" href="#c.Py_TPFLAGS_SEQUENCE" title="Py_TPFLAGS_SEQUENCE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_SEQUENCE</span></code></a> are
mutually exclusive; it is an error to enable both flags simultaneously.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This flag is inherited by types that do not already set
<a class="reference internal" href="#c.Py_TPFLAGS_MAPPING" title="Py_TPFLAGS_MAPPING"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MAPPING</span></code></a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0634/"><strong>PEP 634</strong></a> – Structural Pattern Matching: Specification</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_TPFLAGS_VALID_VERSION_TAG">
<span class="sig-name descname"><span class="n"><span class="pre">Py_TPFLAGS_VALID_VERSION_TAG</span></span></span><a class="headerlink" href="#c.Py_TPFLAGS_VALID_VERSION_TAG" title="Link to this definition">¶</a><br /></dt>
<dd><p>Internal. Do not set or unset this flag.
To indicate that a class has changed call <a class="reference internal" href="type.html#c.PyType_Modified" title="PyType_Modified"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Modified()</span></code></a></p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This flag is present in header files, but is not be used.
It will be removed in a future version of CPython</p>
</div>
</dd></dl>

</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_doc">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_doc</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_doc" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a NUL-terminated C string giving the docstring for this
type object.  This is exposed as the <a class="reference internal" href="../reference/datamodel.html#type.__doc__" title="type.__doc__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__doc__</span></code></a> attribute on the
type and instances of the type.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is <em>not</em> inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_traverse">
<a class="reference internal" href="gcsupport.html#c.traverseproc" title="traverseproc"><span class="n"><span class="pre">traverseproc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_traverse</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_traverse" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a traversal function for the garbage collector.  This is
only used if the <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit is set.  The signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">int</span><span class="w"> </span><span class="nf">tp_traverse</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">visitproc</span><span class="w"> </span><span class="n">visit</span><span class="p">,</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="o">*</span><span class="n">arg</span><span class="p">);</span>
</pre></div>
</div>
<p>More information about Python’s garbage collection scheme can be found
in section <a class="reference internal" href="gcsupport.html#supporting-cycle-detection"><span class="std std-ref">Supporting Cyclic Garbage Collection</span></a>.</p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> pointer is used by the garbage collector to detect
reference cycles. A typical implementation of a <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> function
simply calls <a class="reference internal" href="gcsupport.html#c.Py_VISIT" title="Py_VISIT"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_VISIT()</span></code></a> on each of the instance’s members that are Python
objects that the instance owns. For example, this is function <code class="xref c c-func docutils literal notranslate"><span class="pre">local_traverse()</span></code> from the
<code class="xref py py-mod docutils literal notranslate"><span class="pre">_thread</span></code> extension module:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">int</span>
<span class="nf">local_traverse</span><span class="p">(</span><span class="n">localobject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">visitproc</span><span class="w"> </span><span class="n">visit</span><span class="p">,</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="o">*</span><span class="n">arg</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">Py_VISIT</span><span class="p">(</span><span class="n">self</span><span class="o">-&gt;</span><span class="n">args</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_VISIT</span><span class="p">(</span><span class="n">self</span><span class="o">-&gt;</span><span class="n">kw</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_VISIT</span><span class="p">(</span><span class="n">self</span><span class="o">-&gt;</span><span class="n">dict</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Note that <a class="reference internal" href="gcsupport.html#c.Py_VISIT" title="Py_VISIT"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_VISIT()</span></code></a> is called only on those members that can participate
in reference cycles.  Although there is also a <code class="docutils literal notranslate"><span class="pre">self-&gt;key</span></code> member, it can only
be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> or a Python string and therefore cannot be part of a reference cycle.</p>
<p>On the other hand, even if you know a member can never be part of a cycle, as a
debugging aid you may want to visit it anyway just so the <a class="reference internal" href="../library/gc.html#module-gc" title="gc: Interface to the cycle-detecting garbage collector."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code></a> module’s
<a class="reference internal" href="../library/gc.html#gc.get_referents" title="gc.get_referents"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_referents()</span></code></a> function will include it.</p>
<p>Heap types (<a class="reference internal" href="#c.Py_TPFLAGS_HEAPTYPE" title="Py_TPFLAGS_HEAPTYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HEAPTYPE</span></code></a>) must visit their type with:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">Py_VISIT</span><span class="p">(</span><span class="n">Py_TYPE</span><span class="p">(</span><span class="n">self</span><span class="p">));</span>
</pre></div>
</div>
<p>It is only needed since Python 3.9. To support Python 3.8 and older, this
line must be conditional:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="cp">#if PY_VERSION_HEX &gt;= 0x03090000</span>
<span class="w">    </span><span class="n">Py_VISIT</span><span class="p">(</span><span class="n">Py_TYPE</span><span class="p">(</span><span class="n">self</span><span class="p">));</span>
<span class="cp">#endif</span>
</pre></div>
</div>
<p>If the <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_DICT" title="Py_TPFLAGS_MANAGED_DICT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></code></a> bit is set in the
<a class="reference internal" href="#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a> field, the traverse function must call
<a class="reference internal" href="object.html#c.PyObject_VisitManagedDict" title="PyObject_VisitManagedDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_VisitManagedDict()</span></code></a> like this:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject_VisitManagedDict</span><span class="p">((</span><span class="n">PyObject</span><span class="o">*</span><span class="p">)</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">visit</span><span class="p">,</span><span class="w"> </span><span class="n">arg</span><span class="p">);</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>When implementing <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a>, only the
members that the instance <em>owns</em> (by having <a class="reference internal" href="../glossary.html#term-strong-reference"><span class="xref std std-term">strong references</span></a> to them) must be
visited. For instance, if an object supports weak references via the
<a class="reference internal" href="#c.PyTypeObject.tp_weaklist" title="PyTypeObject.tp_weaklist"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklist</span></code></a> slot, the pointer supporting
the linked list (what <em>tp_weaklist</em> points to) must <strong>not</strong> be
visited as the instance does not directly own the weak references to itself
(the weakreference list is there to support the weak reference machinery,
but the instance has no strong reference to the elements inside it, as they
are allowed to be removed even if the instance is still alive).</p>
</div>
<p>Note that <a class="reference internal" href="gcsupport.html#c.Py_VISIT" title="Py_VISIT"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_VISIT()</span></code></a> requires the <em>visit</em> and <em>arg</em> parameters to
<code class="xref c c-func docutils literal notranslate"><span class="pre">local_traverse()</span></code> to have these specific names; don’t name them just
anything.</p>
<p>Instances of <a class="reference internal" href="#heap-types"><span class="std std-ref">heap-allocated types</span></a> hold a reference to
their type. Their traversal function must therefore either visit
<a class="reference internal" href="structures.html#c.Py_TYPE" title="Py_TYPE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_TYPE(self)</span></code></a>, or delegate this responsibility by
calling <code class="docutils literal notranslate"><span class="pre">tp_traverse</span></code> of another heap-allocated type (such as a
heap-allocated superclass).
If they do not, the type object may not be garbage-collected.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Heap-allocated types are expected to visit <code class="docutils literal notranslate"><span class="pre">Py_TYPE(self)</span></code> in
<code class="docutils literal notranslate"><span class="pre">tp_traverse</span></code>.  In earlier versions of Python, due to
<a class="reference external" href="https://bugs.python.org/issue40217">bug 40217</a>, doing this
may lead to crashes in subclasses.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a></p>
<p>This field is inherited by subtypes together with <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> and the
<a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit: the flag bit, <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a>, and
<a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> are all inherited from the base type if they are all zero in
the subtype.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_clear">
<a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><span class="n"><span class="pre">inquiry</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_clear</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_clear" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a clear function for the garbage collector. This is only
used if the <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit is set.  The signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">int</span><span class="w"> </span><span class="nf">tp_clear</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="p">);</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> member function is used to break reference cycles in cyclic
garbage detected by the garbage collector.  Taken together, all <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a>
functions in the system must combine to break all reference cycles.  This is
subtle, and if in any doubt supply a <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> function.  For example,
the tuple type does not implement a <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> function, because it’s
possible to prove that no reference cycle can be composed entirely of tuples.
Therefore the <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> functions of other types must be sufficient to
break any cycle containing a tuple.  This isn’t immediately obvious, and there’s
rarely a good reason to avoid implementing <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a>.</p>
<p>Implementations of <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> should drop the instance’s references to
those of its members that may be Python objects, and set its pointers to those
members to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, as in the following example:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">int</span>
<span class="nf">local_clear</span><span class="p">(</span><span class="n">localobject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">Py_CLEAR</span><span class="p">(</span><span class="n">self</span><span class="o">-&gt;</span><span class="n">key</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_CLEAR</span><span class="p">(</span><span class="n">self</span><span class="o">-&gt;</span><span class="n">args</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_CLEAR</span><span class="p">(</span><span class="n">self</span><span class="o">-&gt;</span><span class="n">kw</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_CLEAR</span><span class="p">(</span><span class="n">self</span><span class="o">-&gt;</span><span class="n">dict</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="refcounting.html#c.Py_CLEAR" title="Py_CLEAR"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CLEAR()</span></code></a> macro should be used, because clearing references is
delicate:  the reference to the contained object must not be released
(via <a class="reference internal" href="refcounting.html#c.Py_DECREF" title="Py_DECREF"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DECREF()</span></code></a>) until
after the pointer to the contained object is set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  This is because
releasing the reference may cause the contained object to become trash,
triggering a chain of reclamation activity that may include invoking arbitrary
Python code (due to finalizers, or weakref callbacks, associated with the
contained object). If it’s possible for such code to reference <em>self</em> again,
it’s important that the pointer to the contained object be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> at that time,
so that <em>self</em> knows the contained object can no longer be used.  The
<a class="reference internal" href="refcounting.html#c.Py_CLEAR" title="Py_CLEAR"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CLEAR()</span></code></a> macro performs the operations in a safe order.</p>
<p>If the <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_DICT" title="Py_TPFLAGS_MANAGED_DICT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></code></a> bit is set in the
<a class="reference internal" href="#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a> field, the traverse function must call
<a class="reference internal" href="object.html#c.PyObject_ClearManagedDict" title="PyObject_ClearManagedDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_ClearManagedDict()</span></code></a> like this:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject_ClearManagedDict</span><span class="p">((</span><span class="n">PyObject</span><span class="o">*</span><span class="p">)</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>Note that <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> is not <em>always</em> called
before an instance is deallocated. For example, when reference counting
is enough to determine that an object is no longer used, the cyclic garbage
collector is not involved and <a class="reference internal" href="#c.PyTypeObject.tp_dealloc" title="PyTypeObject.tp_dealloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dealloc</span></code></a> is
called directly.</p>
<p>Because the goal of <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> functions is to break reference cycles,
it’s not necessary to clear contained objects like Python strings or Python
integers, which can’t participate in reference cycles. On the other hand, it may
be convenient to clear all contained Python objects, and write the type’s
<a class="reference internal" href="#c.PyTypeObject.tp_dealloc" title="PyTypeObject.tp_dealloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dealloc</span></code></a> function to invoke <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a>.</p>
<p>More information about Python’s garbage collection scheme can be found in
section <a class="reference internal" href="gcsupport.html#supporting-cycle-detection"><span class="std std-ref">Supporting Cyclic Garbage Collection</span></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a></p>
<p>This field is inherited by subtypes together with <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> and the
<a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit: the flag bit, <a class="reference internal" href="#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a>, and
<a class="reference internal" href="#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_clear</span></code></a> are all inherited from the base type if they are all zero in
the subtype.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_richcompare">
<a class="reference internal" href="#c.richcmpfunc" title="richcmpfunc"><span class="n"><span class="pre">richcmpfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_richcompare</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_richcompare" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to the rich comparison function, whose signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_richcompare</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">other</span><span class="p">,</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">op</span><span class="p">);</span>
</pre></div>
</div>
<p>The first parameter is guaranteed to be an instance of the type
that is defined by <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a>.</p>
<p>The function should return the result of the comparison (usually <code class="docutils literal notranslate"><span class="pre">Py_True</span></code>
or <code class="docutils literal notranslate"><span class="pre">Py_False</span></code>).  If the comparison is undefined, it must return
<code class="docutils literal notranslate"><span class="pre">Py_NotImplemented</span></code>, if another error occurred it must return <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and
set an exception condition.</p>
<p>The following constants are defined to be used as the third argument for
<a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a> and for <a class="reference internal" href="object.html#c.PyObject_RichCompare" title="PyObject_RichCompare"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_RichCompare()</span></code></a>:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Constant</p></th>
<th class="head"><p>Comparison</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_LT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_LT</span></span></span><a class="headerlink" href="#c.Py_LT" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;</span></code></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_LE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_LE</span></span></span><a class="headerlink" href="#c.Py_LE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><code class="docutils literal notranslate"><span class="pre">&lt;=</span></code></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_EQ">
<span class="sig-name descname"><span class="n"><span class="pre">Py_EQ</span></span></span><a class="headerlink" href="#c.Py_EQ" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><code class="docutils literal notranslate"><span class="pre">==</span></code></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_NE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_NE</span></span></span><a class="headerlink" href="#c.Py_NE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><code class="docutils literal notranslate"><span class="pre">!=</span></code></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_GT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_GT</span></span></span><a class="headerlink" href="#c.Py_GT" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><code class="docutils literal notranslate"><span class="pre">&gt;</span></code></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_GE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_GE</span></span></span><a class="headerlink" href="#c.Py_GE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><code class="docutils literal notranslate"><span class="pre">&gt;=</span></code></p></td>
</tr>
</tbody>
</table>
<p>The following macro is defined to ease writing rich comparison functions:</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_RETURN_RICHCOMPARE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_RETURN_RICHCOMPARE</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">VAL_A</span></span>, <span class="n"><span class="pre">VAL_B</span></span>, <span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_RETURN_RICHCOMPARE" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">Py_True</span></code> or <code class="docutils literal notranslate"><span class="pre">Py_False</span></code> from the function, depending on the
result of a comparison.
VAL_A and VAL_B must be orderable by C comparison operators (for example,
they may be C ints or floats). The third argument specifies the requested
operation, as for <a class="reference internal" href="object.html#c.PyObject_RichCompare" title="PyObject_RichCompare"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_RichCompare()</span></code></a>.</p>
<p>The returned value is a new <a class="reference internal" href="../glossary.html#term-strong-reference"><span class="xref std std-term">strong reference</span></a>.</p>
<p>On error, sets an exception and returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> from the function.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.7.</span></p>
</div>
</dd></dl>

<p><strong>Inheritance:</strong></p>
<p>Group: <a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a>, <a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a></p>
<p>This field is inherited by subtypes together with <a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a>:
a subtype inherits <a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a> when
the subtype’s <a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a> and <a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a> are both
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p><strong>Default:</strong></p>
<p><a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a> provides a <a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a>
implementation, which may be inherited.  However, if only
<a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a> is defined, not even the inherited function is used
and instances of the type will not be able to participate in any
comparisons.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_weaklistoffset">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_weaklistoffset</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_weaklistoffset" title="Link to this definition">¶</a><br /></dt>
<dd><p>While this field is still supported, <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_WEAKREF" title="Py_TPFLAGS_MANAGED_WEAKREF"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_WEAKREF</span></code></a>
should be used instead, if at all possible.</p>
<p>If the instances of this type are weakly referenceable, this field is greater
than zero and contains the offset in the instance structure of the weak
reference list head (ignoring the GC header, if present); this offset is used by
<a class="reference internal" href="weakref.html#c.PyObject_ClearWeakRefs" title="PyObject_ClearWeakRefs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_ClearWeakRefs()</span></code></a> and the <code class="docutils literal notranslate"><span class="pre">PyWeakref_*</span></code> functions.  The
instance structure needs to include a field of type <span class="c-expr sig sig-inline c"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> which is
initialized to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Do not confuse this field with <a class="reference internal" href="#c.PyTypeObject.tp_weaklist" title="PyTypeObject.tp_weaklist"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklist</span></code></a>; that is the list head for
weak references to the type object itself.</p>
<p>It is an error to set both the <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_WEAKREF" title="Py_TPFLAGS_MANAGED_WEAKREF"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_WEAKREF</span></code></a> bit and
<a class="reference internal" href="#c.PyTypeObject.tp_weaklistoffset" title="PyTypeObject.tp_weaklistoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklistoffset</span></code></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes, but see the rules listed below. A subtype
may override this offset; this means that the subtype uses a different weak
reference list head than the base type.  Since the list head is always found via
<a class="reference internal" href="#c.PyTypeObject.tp_weaklistoffset" title="PyTypeObject.tp_weaklistoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklistoffset</span></code></a>, this should not be a problem.</p>
<p><strong>Default:</strong></p>
<p>If the <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_WEAKREF" title="Py_TPFLAGS_MANAGED_WEAKREF"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_WEAKREF</span></code></a> bit is set in the
<a class="reference internal" href="#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a> field, then
<a class="reference internal" href="#c.PyTypeObject.tp_weaklistoffset" title="PyTypeObject.tp_weaklistoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklistoffset</span></code></a> will be set to a negative value,
to indicate that it is unsafe to use this field.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_iter">
<a class="reference internal" href="#c.getiterfunc" title="getiterfunc"><span class="n"><span class="pre">getiterfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_iter</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_iter" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a function that returns an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> for the
object.  Its presence normally signals that the instances of this type are
<a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a> (although sequences may be iterable without this function).</p>
<p>This function has the same signature as <a class="reference internal" href="object.html#c.PyObject_GetIter" title="PyObject_GetIter"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetIter()</span></code></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_iter</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_iternext">
<a class="reference internal" href="#c.iternextfunc" title="iternextfunc"><span class="n"><span class="pre">iternextfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_iternext</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_iternext" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a function that returns the next item in an
<a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a>. The signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_iternext</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>When the iterator is exhausted, it must return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>; a <a class="reference internal" href="../library/exceptions.html#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a>
exception may or may not be set.  When another error occurs, it must return
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> too.  Its presence signals that the instances of this type are
iterators.</p>
<p>Iterator types should also define the <a class="reference internal" href="#c.PyTypeObject.tp_iter" title="PyTypeObject.tp_iter"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_iter</span></code></a> function, and that
function should return the iterator instance itself (not a new iterator
instance).</p>
<p>This function has the same signature as <a class="reference internal" href="iter.html#c.PyIter_Next" title="PyIter_Next"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyIter_Next()</span></code></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_methods">
<span class="k"><span class="pre">struct</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyMethodDef" title="PyMethodDef"><span class="n"><span class="pre">PyMethodDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_methods</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_methods" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a static <code class="docutils literal notranslate"><span class="pre">NULL</span></code>-terminated array of <a class="reference internal" href="structures.html#c.PyMethodDef" title="PyMethodDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMethodDef</span></code></a>
structures, declaring regular methods of this type.</p>
<p>For each entry in the array, an entry is added to the type’s dictionary (see
<a class="reference internal" href="#c.PyTypeObject.tp_dict" title="PyTypeObject.tp_dict"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dict</span></code></a> below) containing a method descriptor.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited by subtypes (methods are inherited through a
different mechanism).</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_members">
<span class="k"><span class="pre">struct</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyMemberDef" title="PyMemberDef"><span class="n"><span class="pre">PyMemberDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_members</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_members" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a static <code class="docutils literal notranslate"><span class="pre">NULL</span></code>-terminated array of <a class="reference internal" href="structures.html#c.PyMemberDef" title="PyMemberDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMemberDef</span></code></a>
structures, declaring regular data members (fields or slots) of instances of
this type.</p>
<p>For each entry in the array, an entry is added to the type’s dictionary (see
<a class="reference internal" href="#c.PyTypeObject.tp_dict" title="PyTypeObject.tp_dict"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dict</span></code></a> below) containing a member descriptor.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited by subtypes (members are inherited through a
different mechanism).</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_getset">
<span class="k"><span class="pre">struct</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyGetSetDef" title="PyGetSetDef"><span class="n"><span class="pre">PyGetSetDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_getset</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_getset" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a static <code class="docutils literal notranslate"><span class="pre">NULL</span></code>-terminated array of <a class="reference internal" href="structures.html#c.PyGetSetDef" title="PyGetSetDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyGetSetDef</span></code></a>
structures, declaring computed attributes of instances of this type.</p>
<p>For each entry in the array, an entry is added to the type’s dictionary (see
<a class="reference internal" href="#c.PyTypeObject.tp_dict" title="PyTypeObject.tp_dict"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dict</span></code></a> below) containing a getset descriptor.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited by subtypes (computed attributes are inherited
through a different mechanism).</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_base">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_base</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_base" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a base type from which type properties are inherited.  At
this level, only single inheritance is supported; multiple inheritance require
dynamically creating a type object by calling the metatype.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Slot initialization is subject to the rules of initializing globals.
C99 requires the initializers to be “address constants”.  Function
designators like <a class="reference internal" href="type.html#c.PyType_GenericNew" title="PyType_GenericNew"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GenericNew()</span></code></a>, with implicit conversion
to a pointer, are valid C99 address constants.</p>
<p>However, the unary ‘&amp;’ operator applied to a non-static variable
like <a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a> is not required to produce an address
constant.  Compilers may support this (gcc does), MSVC does not.
Both compilers are strictly standard conforming in this particular
behavior.</p>
<p>Consequently, <a class="reference internal" href="#c.PyTypeObject.tp_base" title="PyTypeObject.tp_base"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_base</span></code></a> should be set in
the extension module’s init function.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited by subtypes (obviously).</p>
<p><strong>Default:</strong></p>
<p>This field defaults to <code class="docutils literal notranslate"><span class="pre">&amp;PyBaseObject_Type</span></code> (which to Python
programmers is known as the type <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>).</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_dict">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_dict</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_dict" title="Link to this definition">¶</a><br /></dt>
<dd><p>The type’s dictionary is stored here by <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a>.</p>
<p>This field should normally be initialized to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> before PyType_Ready is
called; it may also be initialized to a dictionary containing initial attributes
for the type.  Once <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> has initialized the type, extra
attributes for the type may be added to this dictionary only if they don’t
correspond to overloaded operations (like <a class="reference internal" href="../reference/datamodel.html#object.__add__" title="object.__add__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__add__()</span></code></a>).  Once
initialization for the type has finished, this field should be
treated as read-only.</p>
<p>Some types may not store their dictionary in this slot.
Use <a class="reference internal" href="type.html#c.PyType_GetDict" title="PyType_GetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetDict()</span></code></a> to retrieve the dictionary for an arbitrary
type.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Internals detail: For static builtin types, this is always <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
Instead, the dict for such types is stored on <code class="docutils literal notranslate"><span class="pre">PyInterpreterState</span></code>.
Use <a class="reference internal" href="type.html#c.PyType_GetDict" title="PyType_GetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetDict()</span></code></a> to get the dict for an arbitrary type.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited by subtypes (though the attributes defined in here
are inherited through a different mechanism).</p>
<p><strong>Default:</strong></p>
<p>If this field is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> will assign a new
dictionary to it.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>It is not safe to use <a class="reference internal" href="dict.html#c.PyDict_SetItem" title="PyDict_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_SetItem()</span></code></a> on or otherwise modify
<a class="reference internal" href="#c.PyTypeObject.tp_dict" title="PyTypeObject.tp_dict"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dict</span></code></a> with the dictionary C-API.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_descr_get">
<a class="reference internal" href="#c.descrgetfunc" title="descrgetfunc"><span class="n"><span class="pre">descrgetfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_descr_get</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_descr_get" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a “descriptor get” function.</p>
<p>The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nf">tp_descr_get</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">obj</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">type</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_descr_set">
<a class="reference internal" href="#c.descrsetfunc" title="descrsetfunc"><span class="n"><span class="pre">descrsetfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_descr_set</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_descr_set" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a function for setting and deleting
a descriptor’s value.</p>
<p>The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">int</span><span class="w"> </span><span class="nf">tp_descr_set</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">obj</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">value</span><span class="p">);</span>
</pre></div>
</div>
<p>The <em>value</em> argument is set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to delete the value.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_dictoffset">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_dictoffset</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_dictoffset" title="Link to this definition">¶</a><br /></dt>
<dd><p>While this field is still supported, <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_DICT" title="Py_TPFLAGS_MANAGED_DICT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></code></a> should be
used instead, if at all possible.</p>
<p>If the instances of this type have a dictionary containing instance variables,
this field is non-zero and contains the offset in the instances of the type of
the instance variable dictionary; this offset is used by
<a class="reference internal" href="object.html#c.PyObject_GenericGetAttr" title="PyObject_GenericGetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericGetAttr()</span></code></a>.</p>
<p>Do not confuse this field with <a class="reference internal" href="#c.PyTypeObject.tp_dict" title="PyTypeObject.tp_dict"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dict</span></code></a>; that is the dictionary for
attributes of the type object itself.</p>
<p>The value specifies the offset of the dictionary from the start of the instance structure.</p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_dictoffset" title="PyTypeObject.tp_dictoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dictoffset</span></code></a> should be regarded as write-only.
To get the pointer to the dictionary call <a class="reference internal" href="object.html#c.PyObject_GenericGetDict" title="PyObject_GenericGetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericGetDict()</span></code></a>.
Calling <a class="reference internal" href="object.html#c.PyObject_GenericGetDict" title="PyObject_GenericGetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericGetDict()</span></code></a> may need to allocate memory for the
dictionary, so it is may be more efficient to call <a class="reference internal" href="object.html#c.PyObject_GetAttr" title="PyObject_GetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetAttr()</span></code></a>
when accessing an attribute on the object.</p>
<p>It is an error to set both the <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_DICT" title="Py_TPFLAGS_MANAGED_DICT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></code></a> bit and
<a class="reference internal" href="#c.PyTypeObject.tp_dictoffset" title="PyTypeObject.tp_dictoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dictoffset</span></code></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes. A subtype should not override this offset;
doing so could be unsafe, if C code tries to access the dictionary at the
previous offset.
To properly support inheritance, use <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_DICT" title="Py_TPFLAGS_MANAGED_DICT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></code></a>.</p>
<p><strong>Default:</strong></p>
<p>This slot has no default.  For <a class="reference internal" href="#static-types"><span class="std std-ref">static types</span></a>, if the
field is <code class="docutils literal notranslate"><span class="pre">NULL</span></code> then no <a class="reference internal" href="../reference/datamodel.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> gets created for instances.</p>
<p>If the <a class="reference internal" href="#c.Py_TPFLAGS_MANAGED_DICT" title="Py_TPFLAGS_MANAGED_DICT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></code></a> bit is set in the
<a class="reference internal" href="#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a> field, then
<a class="reference internal" href="#c.PyTypeObject.tp_dictoffset" title="PyTypeObject.tp_dictoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dictoffset</span></code></a> will be set to <code class="docutils literal notranslate"><span class="pre">-1</span></code>, to indicate
that it is unsafe to use this field.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_init">
<a class="reference internal" href="#c.initproc" title="initproc"><span class="n"><span class="pre">initproc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_init</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_init" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to an instance initialization function.</p>
<p>This function corresponds to the <a class="reference internal" href="../reference/datamodel.html#object.__init__" title="object.__init__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a> method of classes.  Like
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code>, it is possible to create an instance without calling
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code>, and it is possible to reinitialize an instance by calling its
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> method again.</p>
<p>The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">int</span><span class="w"> </span><span class="nf">tp_init</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">kwds</span><span class="p">);</span>
</pre></div>
</div>
<p>The self argument is the instance to be initialized; the <em>args</em> and <em>kwds</em>
arguments represent positional and keyword arguments of the call to
<a class="reference internal" href="../reference/datamodel.html#object.__init__" title="object.__init__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a>.</p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a> function, if not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, is called when an instance is
created normally by calling its type, after the type’s <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> function
has returned an instance of the type.  If the <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> function returns an
instance of some other type that is not a subtype of the original type, no
<a class="reference internal" href="#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a> function is called; if <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> returns an instance of a
subtype of the original type, the subtype’s <a class="reference internal" href="#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a> is called.</p>
<p>Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, <code class="docutils literal notranslate"><span class="pre">-1</span></code> and sets an exception on error.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
<p><strong>Default:</strong></p>
<p>For <a class="reference internal" href="#static-types"><span class="std std-ref">static types</span></a> this field does not have a default.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_alloc">
<a class="reference internal" href="#c.allocfunc" title="allocfunc"><span class="n"><span class="pre">allocfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_alloc</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_alloc" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to an instance allocation function.</p>
<p>The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_alloc</span><span class="p">(</span><span class="n">PyTypeObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="n">nitems</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by static subtypes, but not by dynamic
subtypes (subtypes created by a class statement).</p>
<p><strong>Default:</strong></p>
<p>For dynamic subtypes, this field is always set to
<a class="reference internal" href="type.html#c.PyType_GenericAlloc" title="PyType_GenericAlloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GenericAlloc()</span></code></a>, to force a standard heap
allocation strategy.</p>
<p>For static subtypes, <a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a> uses
<a class="reference internal" href="type.html#c.PyType_GenericAlloc" title="PyType_GenericAlloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GenericAlloc()</span></code></a>.  That is the recommended value
for all statically defined types.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_new">
<a class="reference internal" href="#c.newfunc" title="newfunc"><span class="n"><span class="pre">newfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_new</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_new" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to an instance creation function.</p>
<p>The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_new</span><span class="p">(</span><span class="n">PyTypeObject</span><span class="w"> </span><span class="o">*</span><span class="n">subtype</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">kwds</span><span class="p">);</span>
</pre></div>
</div>
<p>The <em>subtype</em> argument is the type of the object being created; the <em>args</em> and
<em>kwds</em> arguments represent positional and keyword arguments of the call to the
type.  Note that <em>subtype</em> doesn’t have to equal the type whose <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a>
function is called; it may be a subtype of that type (but not an unrelated
type).</p>
<p>The <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> function should call <code class="docutils literal notranslate"><span class="pre">subtype-&gt;tp_alloc(subtype,</span> <span class="pre">nitems)</span></code>
to allocate space for the object, and then do only as much further
initialization as is absolutely necessary.  Initialization that can safely be
ignored or repeated should be placed in the <a class="reference internal" href="#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a> handler.  A good
rule of thumb is that for immutable types, all initialization should take place
in <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a>, while for mutable types, most initialization should be
deferred to <a class="reference internal" href="#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a>.</p>
<p>Set the <a class="reference internal" href="#c.Py_TPFLAGS_DISALLOW_INSTANTIATION" title="Py_TPFLAGS_DISALLOW_INSTANTIATION"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_DISALLOW_INSTANTIATION</span></code></a> flag to disallow creating
instances of the type in Python.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes, except it is not inherited by
<a class="reference internal" href="#static-types"><span class="std std-ref">static types</span></a> whose <a class="reference internal" href="#c.PyTypeObject.tp_base" title="PyTypeObject.tp_base"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_base</span></code></a>
is <code class="docutils literal notranslate"><span class="pre">NULL</span></code> or <code class="docutils literal notranslate"><span class="pre">&amp;PyBaseObject_Type</span></code>.</p>
<p><strong>Default:</strong></p>
<p>For <a class="reference internal" href="#static-types"><span class="std std-ref">static types</span></a> this field has no default.
This means if the slot is defined as <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the type cannot be called
to create new instances; presumably there is some other way to create
instances, like a factory function.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_free">
<a class="reference internal" href="#c.freefunc" title="freefunc"><span class="n"><span class="pre">freefunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_free</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_free" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to an instance deallocation function.  Its signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">tp_free</span><span class="p">(</span><span class="kt">void</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>An initializer that is compatible with this signature is <a class="reference internal" href="memory.html#c.PyObject_Free" title="PyObject_Free"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Free()</span></code></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by static subtypes, but not by dynamic
subtypes (subtypes created by a class statement)</p>
<p><strong>Default:</strong></p>
<p>In dynamic subtypes, this field is set to a deallocator suitable to
match <a class="reference internal" href="type.html#c.PyType_GenericAlloc" title="PyType_GenericAlloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GenericAlloc()</span></code></a> and the value of the
<a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit.</p>
<p>For static subtypes, <a class="reference internal" href="structures.html#c.PyBaseObject_Type" title="PyBaseObject_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></a> uses <a class="reference internal" href="allocation.html#c.PyObject_Del" title="PyObject_Del"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Del()</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_is_gc">
<a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><span class="n"><span class="pre">inquiry</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_is_gc</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_is_gc" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to a function called by the garbage collector.</p>
<p>The garbage collector needs to know whether a particular object is collectible
or not.  Normally, it is sufficient to look at the object’s type’s
<a class="reference internal" href="#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a> field, and check the <a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> flag bit.  But
some types have a mixture of statically and dynamically allocated instances, and
the statically allocated instances are not collectible.  Such types should
define this function; it should return <code class="docutils literal notranslate"><span class="pre">1</span></code> for a collectible instance, and
<code class="docutils literal notranslate"><span class="pre">0</span></code> for a non-collectible instance. The signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">int</span><span class="w"> </span><span class="nf">tp_is_gc</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>(The only example of this are types themselves.  The metatype,
<a class="reference internal" href="type.html#c.PyType_Type" title="PyType_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyType_Type</span></code></a>, defines this function to distinguish between statically
and <a class="reference internal" href="#heap-types"><span class="std std-ref">dynamically allocated types</span></a>.)</p>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
<p><strong>Default:</strong></p>
<p>This slot has no default.  If this field is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>,
<a class="reference internal" href="#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> is used as the functional equivalent.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_bases">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_bases</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_bases" title="Link to this definition">¶</a><br /></dt>
<dd><p>Tuple of base types.</p>
<p>This field should be set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and treated as read-only.
Python will fill it in when the type is <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">initialized</span></code></a>.</p>
<p>For dynamically created classes, the <code class="docutils literal notranslate"><span class="pre">Py_tp_bases</span></code>
<a class="reference internal" href="type.html#c.PyType_Slot" title="PyType_Slot"><code class="xref c c-type docutils literal notranslate"><span class="pre">slot</span></code></a> can be used instead of the <em>bases</em> argument
of <a class="reference internal" href="type.html#c.PyType_FromSpecWithBases" title="PyType_FromSpecWithBases"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpecWithBases()</span></code></a>.
The argument form is preferred.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Multiple inheritance does not work well for statically defined types.
If you set <code class="docutils literal notranslate"><span class="pre">tp_bases</span></code> to a tuple, Python will not raise an error,
but some slots will only be inherited from the first base.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_mro">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_mro</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_mro" title="Link to this definition">¶</a><br /></dt>
<dd><p>Tuple containing the expanded set of base types, starting with the type itself
and ending with <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>, in Method Resolution Order.</p>
<p>This field should be set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and treated as read-only.
Python will fill it in when the type is <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">initialized</span></code></a>.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited; it is calculated fresh by
<a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_cache">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_cache</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_cache" title="Link to this definition">¶</a><br /></dt>
<dd><p>Unused.  Internal use only.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_subclasses">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_subclasses</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_subclasses" title="Link to this definition">¶</a><br /></dt>
<dd><p>A collection of subclasses.  Internal use only.  May be an invalid pointer.</p>
<p>To get a list of subclasses, call the Python method
<a class="reference internal" href="../reference/datamodel.html#type.__subclasses__" title="type.__subclasses__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__subclasses__()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>For some types, this field does not hold a valid <span class="c-expr sig sig-inline c"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span>.
The type was changed to <span class="c-expr sig sig-inline c"><span class="kt">void</span><span class="p">*</span></span> to indicate this.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_weaklist">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_weaklist</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_weaklist" title="Link to this definition">¶</a><br /></dt>
<dd><p>Weak reference list head, for weak references to this type object.  Not
inherited.  Internal use only.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Internals detail: For the static builtin types this is always <code class="docutils literal notranslate"><span class="pre">NULL</span></code>,
even if weakrefs are added.  Instead, the weakrefs for each are stored
on <code class="docutils literal notranslate"><span class="pre">PyInterpreterState</span></code>.  Use the public C-API or the internal
<code class="docutils literal notranslate"><span class="pre">_PyObject_GET_WEAKREFS_LISTPTR()</span></code> macro to avoid the distinction.</p>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_del">
<a class="reference internal" href="#c.destructor" title="destructor"><span class="n"><span class="pre">destructor</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_del</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_del" title="Link to this definition">¶</a><br /></dt>
<dd><p>This field is deprecated.  Use <a class="reference internal" href="#c.PyTypeObject.tp_finalize" title="PyTypeObject.tp_finalize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_finalize</span></code></a> instead.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_version_tag">
<span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_version_tag</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_version_tag" title="Link to this definition">¶</a><br /></dt>
<dd><p>Used to index into the method cache.  Internal use only.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is not inherited.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_finalize">
<a class="reference internal" href="#c.destructor" title="destructor"><span class="n"><span class="pre">destructor</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_finalize</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_finalize" title="Link to this definition">¶</a><br /></dt>
<dd><p>An optional pointer to an instance finalization function.  Its signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">tp_finalize</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>If <a class="reference internal" href="#c.PyTypeObject.tp_finalize" title="PyTypeObject.tp_finalize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_finalize</span></code></a> is set, the interpreter calls it once when
finalizing an instance.  It is called either from the garbage
collector (if the instance is part of an isolated reference cycle) or
just before the object is deallocated.  Either way, it is guaranteed
to be called before attempting to break reference cycles, ensuring
that it finds the object in a sane state.</p>
<p><a class="reference internal" href="#c.PyTypeObject.tp_finalize" title="PyTypeObject.tp_finalize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_finalize</span></code></a> should not mutate the current exception status;
therefore, a recommended way to write a non-trivial finalizer is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">void</span>
<span class="nf">local_finalize</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="cm">/* Save the current exception, if any. */</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">exc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyErr_GetRaisedException</span><span class="p">();</span>

<span class="w">    </span><span class="cm">/* ... */</span>

<span class="w">    </span><span class="cm">/* Restore the saved exception. */</span>
<span class="w">    </span><span class="n">PyErr_SetRaisedException</span><span class="p">(</span><span class="n">exc</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Inheritance:</strong></p>
<p>This field is inherited by subtypes.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Before version 3.8 it was necessary to set the
<a class="reference internal" href="#c.Py_TPFLAGS_HAVE_FINALIZE" title="Py_TPFLAGS_HAVE_FINALIZE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_FINALIZE</span></code></a> flags bit in order for this field to be
used.  This is no longer required.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>“Safe object finalization” (<span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-0442/"><strong>PEP 442</strong></a>)</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_vectorcall">
<a class="reference internal" href="call.html#c.vectorcallfunc" title="vectorcallfunc"><span class="n"><span class="pre">vectorcallfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_vectorcall</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_vectorcall" title="Link to this definition">¶</a><br /></dt>
<dd><p>Vectorcall function to use for calls of this type object.
In other words, it is used to implement
<a class="reference internal" href="call.html#vectorcall"><span class="std std-ref">vectorcall</span></a> for <code class="docutils literal notranslate"><span class="pre">type.__call__</span></code>.
If <code class="docutils literal notranslate"><span class="pre">tp_vectorcall</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the default call implementation
using <a class="reference internal" href="../reference/datamodel.html#object.__new__" title="object.__new__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__init__" title="object.__init__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a> is used.</p>
<p><strong>Inheritance:</strong></p>
<p>This field is never inherited.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9: </span>(the field exists since 3.8 but it’s only used since 3.9)</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyTypeObject.tp_watched">
<span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">tp_watched</span></span></span><a class="headerlink" href="#c.PyTypeObject.tp_watched" title="Link to this definition">¶</a><br /></dt>
<dd><p>Internal. Do not use.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

</section>
<section id="static-types">
<span id="id4"></span><h2>Static Types<a class="headerlink" href="#static-types" title="Link to this heading">¶</a></h2>
<p>Traditionally, types defined in C code are <em>static</em>, that is,
a static <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> structure is defined directly in code
and initialized using <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a>.</p>
<p>This results in types that are limited relative to types defined in Python:</p>
<ul class="simple">
<li><p>Static types are limited to one base, i.e. they cannot use multiple
inheritance.</p></li>
<li><p>Static type objects (but not necessarily their instances) are immutable.
It is not possible to add or modify the type object’s attributes from Python.</p></li>
<li><p>Static type objects are shared across
<a class="reference internal" href="init.html#sub-interpreter-support"><span class="std std-ref">sub-interpreters</span></a>, so they should not
include any subinterpreter-specific state.</p></li>
</ul>
<p>Also, since <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> is only part of the <a class="reference internal" href="stable.html#limited-c-api"><span class="std std-ref">Limited API</span></a> as an opaque struct, any extension modules using static types must be
compiled for a specific Python minor version.</p>
</section>
<section id="heap-types">
<span id="id5"></span><h2>Heap Types<a class="headerlink" href="#heap-types" title="Link to this heading">¶</a></h2>
<p>An alternative to <a class="reference internal" href="#static-types"><span class="std std-ref">static types</span></a> is <em>heap-allocated types</em>,
or <em>heap types</em> for short, which correspond closely to classes created by
Python’s <code class="docutils literal notranslate"><span class="pre">class</span></code> statement. Heap types have the <a class="reference internal" href="#c.Py_TPFLAGS_HEAPTYPE" title="Py_TPFLAGS_HEAPTYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HEAPTYPE</span></code></a>
flag set.</p>
<p>This is done by filling a <a class="reference internal" href="type.html#c.PyType_Spec" title="PyType_Spec"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyType_Spec</span></code></a> structure and calling
<a class="reference internal" href="type.html#c.PyType_FromSpec" title="PyType_FromSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpec()</span></code></a>, <a class="reference internal" href="type.html#c.PyType_FromSpecWithBases" title="PyType_FromSpecWithBases"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpecWithBases()</span></code></a>,
<a class="reference internal" href="type.html#c.PyType_FromModuleAndSpec" title="PyType_FromModuleAndSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromModuleAndSpec()</span></code></a>, or <a class="reference internal" href="type.html#c.PyType_FromMetaclass" title="PyType_FromMetaclass"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromMetaclass()</span></code></a>.</p>
</section>
<section id="number-object-structures">
<span id="number-structs"></span><h2>Number Object Structures<a class="headerlink" href="#number-object-structures" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyNumberMethods">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyNumberMethods</span></span></span><a class="headerlink" href="#c.PyNumberMethods" title="Link to this definition">¶</a><br /></dt>
<dd><p>This structure holds pointers to the functions which an object uses to
implement the number protocol.  Each function is used by the function of
similar name documented in the <a class="reference internal" href="number.html#number"><span class="std std-ref">Number Protocol</span></a> section.</p>
<p>Here is the structure definition:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="p">{</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_add</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_subtract</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_multiply</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_remainder</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_divmod</span><span class="p">;</span>
<span class="w">     </span><span class="n">ternaryfunc</span><span class="w"> </span><span class="n">nb_power</span><span class="p">;</span>
<span class="w">     </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">nb_negative</span><span class="p">;</span>
<span class="w">     </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">nb_positive</span><span class="p">;</span>
<span class="w">     </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">nb_absolute</span><span class="p">;</span>
<span class="w">     </span><span class="n">inquiry</span><span class="w"> </span><span class="n">nb_bool</span><span class="p">;</span>
<span class="w">     </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">nb_invert</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_lshift</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_rshift</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_and</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_xor</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_or</span><span class="p">;</span>
<span class="w">     </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">nb_int</span><span class="p">;</span>
<span class="w">     </span><span class="kt">void</span><span class="w"> </span><span class="o">*</span><span class="n">nb_reserved</span><span class="p">;</span>
<span class="w">     </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">nb_float</span><span class="p">;</span>

<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_add</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_subtract</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_multiply</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_remainder</span><span class="p">;</span>
<span class="w">     </span><span class="n">ternaryfunc</span><span class="w"> </span><span class="n">nb_inplace_power</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_lshift</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_rshift</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_and</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_xor</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_or</span><span class="p">;</span>

<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_floor_divide</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_true_divide</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_floor_divide</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_true_divide</span><span class="p">;</span>

<span class="w">     </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">nb_index</span><span class="p">;</span>

<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_matrix_multiply</span><span class="p">;</span>
<span class="w">     </span><span class="n">binaryfunc</span><span class="w"> </span><span class="n">nb_inplace_matrix_multiply</span><span class="p">;</span>
<span class="p">}</span><span class="w"> </span><span class="n">PyNumberMethods</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Binary and ternary functions must check the type of all their operands,
and implement the necessary conversions (at least one of the operands is
an instance of the defined type).  If the operation is not defined for the
given operands, binary and ternary functions must return
<code class="docutils literal notranslate"><span class="pre">Py_NotImplemented</span></code>, if another error occurred they must return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>
and set an exception.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#c.PyNumberMethods.nb_reserved" title="PyNumberMethods.nb_reserved"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_reserved</span></code></a> field should always be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  It
was previously called <code class="xref c c-member docutils literal notranslate"><span class="pre">nb_long</span></code>, and was renamed in
Python 3.0.1.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_add">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_add</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_add" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_subtract">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_subtract</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_subtract" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_multiply">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_multiply</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_multiply" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_remainder">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_remainder</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_remainder" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_divmod">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_divmod</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_divmod" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_power">
<a class="reference internal" href="#c.ternaryfunc" title="ternaryfunc"><span class="n"><span class="pre">ternaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_power</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_power" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_negative">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_negative</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_negative" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_positive">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_positive</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_positive" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_absolute">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_absolute</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_absolute" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_bool">
<a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><span class="n"><span class="pre">inquiry</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_bool</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_bool" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_invert">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_invert</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_invert" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_lshift">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_lshift</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_lshift" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_rshift">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_rshift</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_rshift" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_and">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_and</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_and" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_xor">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_xor</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_xor" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_or">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_or</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_or" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_int">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_int</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_int" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_reserved">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_reserved</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_reserved" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_float">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_float</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_float" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_add">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_add</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_add" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_subtract">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_subtract</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_subtract" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_multiply">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_multiply</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_multiply" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_remainder">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_remainder</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_remainder" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_power">
<a class="reference internal" href="#c.ternaryfunc" title="ternaryfunc"><span class="n"><span class="pre">ternaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_power</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_power" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_lshift">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_lshift</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_lshift" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_rshift">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_rshift</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_rshift" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_and">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_and</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_and" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_xor">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_xor</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_xor" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_or">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_or</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_or" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_floor_divide">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_floor_divide</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_floor_divide" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_true_divide">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_true_divide</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_true_divide" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_floor_divide">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_floor_divide</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_floor_divide" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_true_divide">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_true_divide</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_true_divide" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_index">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_index</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_index" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_matrix_multiply">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_matrix_multiply</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_matrix_multiply" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyNumberMethods.nb_inplace_matrix_multiply">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyNumberMethods" title="PyNumberMethods"><span class="n"><span class="pre">PyNumberMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">nb_inplace_matrix_multiply</span></span></span><a class="headerlink" href="#c.PyNumberMethods.nb_inplace_matrix_multiply" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</section>
<section id="mapping-object-structures">
<span id="mapping-structs"></span><h2>Mapping Object Structures<a class="headerlink" href="#mapping-object-structures" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyMappingMethods">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMappingMethods</span></span></span><a class="headerlink" href="#c.PyMappingMethods" title="Link to this definition">¶</a><br /></dt>
<dd><p>This structure holds pointers to the functions which an object uses to
implement the mapping protocol.  It has three members:</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMappingMethods.mp_length">
<a class="reference internal" href="#c.lenfunc" title="lenfunc"><span class="n"><span class="pre">lenfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyMappingMethods" title="PyMappingMethods"><span class="n"><span class="pre">PyMappingMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">mp_length</span></span></span><a class="headerlink" href="#c.PyMappingMethods.mp_length" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="mapping.html#c.PyMapping_Size" title="PyMapping_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_Size()</span></code></a> and
<a class="reference internal" href="object.html#c.PyObject_Size" title="PyObject_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Size()</span></code></a>, and has the same signature.  This slot may be set to
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> if the object has no defined length.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMappingMethods.mp_subscript">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyMappingMethods" title="PyMappingMethods"><span class="n"><span class="pre">PyMappingMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">mp_subscript</span></span></span><a class="headerlink" href="#c.PyMappingMethods.mp_subscript" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="object.html#c.PyObject_GetItem" title="PyObject_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetItem()</span></code></a> and
<a class="reference internal" href="sequence.html#c.PySequence_GetSlice" title="PySequence_GetSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_GetSlice()</span></code></a>, and has the same signature as
<code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetItem()</span></code>.  This slot must be filled for the
<a class="reference internal" href="mapping.html#c.PyMapping_Check" title="PyMapping_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_Check()</span></code></a> function to return <code class="docutils literal notranslate"><span class="pre">1</span></code>, it can be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>
otherwise.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMappingMethods.mp_ass_subscript">
<a class="reference internal" href="#c.objobjargproc" title="objobjargproc"><span class="n"><span class="pre">objobjargproc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyMappingMethods" title="PyMappingMethods"><span class="n"><span class="pre">PyMappingMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">mp_ass_subscript</span></span></span><a class="headerlink" href="#c.PyMappingMethods.mp_ass_subscript" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="object.html#c.PyObject_SetItem" title="PyObject_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SetItem()</span></code></a>,
<a class="reference internal" href="object.html#c.PyObject_DelItem" title="PyObject_DelItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_DelItem()</span></code></a>, <a class="reference internal" href="sequence.html#c.PySequence_SetSlice" title="PySequence_SetSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_SetSlice()</span></code></a> and
<a class="reference internal" href="sequence.html#c.PySequence_DelSlice" title="PySequence_DelSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_DelSlice()</span></code></a>.  It has the same signature as
<code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SetItem()</span></code>, but <em>v</em> can also be set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to delete
an item.  If this slot is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the object does not support item
assignment and deletion.</p>
</dd></dl>

</section>
<section id="sequence-object-structures">
<span id="sequence-structs"></span><h2>Sequence Object Structures<a class="headerlink" href="#sequence-object-structures" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PySequenceMethods">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySequenceMethods</span></span></span><a class="headerlink" href="#c.PySequenceMethods" title="Link to this definition">¶</a><br /></dt>
<dd><p>This structure holds pointers to the functions which an object uses to
implement the sequence protocol.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PySequenceMethods.sq_length">
<a class="reference internal" href="#c.lenfunc" title="lenfunc"><span class="n"><span class="pre">lenfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">sq_length</span></span></span><a class="headerlink" href="#c.PySequenceMethods.sq_length" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="sequence.html#c.PySequence_Size" title="PySequence_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Size()</span></code></a> and
<a class="reference internal" href="object.html#c.PyObject_Size" title="PyObject_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Size()</span></code></a>, and has the same signature.  It is also used for
handling negative indices via the <a class="reference internal" href="#c.PySequenceMethods.sq_item" title="PySequenceMethods.sq_item"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_item</span></code></a>
and the <a class="reference internal" href="#c.PySequenceMethods.sq_ass_item" title="PySequenceMethods.sq_ass_item"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_ass_item</span></code></a> slots.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PySequenceMethods.sq_concat">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">sq_concat</span></span></span><a class="headerlink" href="#c.PySequenceMethods.sq_concat" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="sequence.html#c.PySequence_Concat" title="PySequence_Concat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Concat()</span></code></a> and has the same
signature.  It is also used by the <code class="docutils literal notranslate"><span class="pre">+</span></code> operator, after trying the numeric
addition via the <a class="reference internal" href="#c.PyNumberMethods.nb_add" title="PyNumberMethods.nb_add"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_add</span></code></a> slot.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PySequenceMethods.sq_repeat">
<a class="reference internal" href="#c.ssizeargfunc" title="ssizeargfunc"><span class="n"><span class="pre">ssizeargfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">sq_repeat</span></span></span><a class="headerlink" href="#c.PySequenceMethods.sq_repeat" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="sequence.html#c.PySequence_Repeat" title="PySequence_Repeat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Repeat()</span></code></a> and has the same
signature.  It is also used by the <code class="docutils literal notranslate"><span class="pre">*</span></code> operator, after trying numeric
multiplication via the <a class="reference internal" href="#c.PyNumberMethods.nb_multiply" title="PyNumberMethods.nb_multiply"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_multiply</span></code></a> slot.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PySequenceMethods.sq_item">
<a class="reference internal" href="#c.ssizeargfunc" title="ssizeargfunc"><span class="n"><span class="pre">ssizeargfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">sq_item</span></span></span><a class="headerlink" href="#c.PySequenceMethods.sq_item" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="sequence.html#c.PySequence_GetItem" title="PySequence_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_GetItem()</span></code></a> and has the same
signature.  It is also used by <a class="reference internal" href="object.html#c.PyObject_GetItem" title="PyObject_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetItem()</span></code></a>, after trying
the subscription via the <a class="reference internal" href="#c.PyMappingMethods.mp_subscript" title="PyMappingMethods.mp_subscript"><code class="xref c c-member docutils literal notranslate"><span class="pre">mp_subscript</span></code></a> slot.
This slot must be filled for the <a class="reference internal" href="sequence.html#c.PySequence_Check" title="PySequence_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Check()</span></code></a>
function to return <code class="docutils literal notranslate"><span class="pre">1</span></code>, it can be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> otherwise.</p>
<p>Negative indexes are handled as follows: if the <a class="reference internal" href="#c.PySequenceMethods.sq_length" title="PySequenceMethods.sq_length"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_length</span></code></a> slot is
filled, it is called and the sequence length is used to compute a positive
index which is passed to  <a class="reference internal" href="#c.PySequenceMethods.sq_item" title="PySequenceMethods.sq_item"><code class="xref c c-member docutils literal notranslate"><span class="pre">sq_item</span></code></a>.  If <code class="xref c c-member docutils literal notranslate"><span class="pre">sq_length</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>,
the index is passed as is to the function.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PySequenceMethods.sq_ass_item">
<a class="reference internal" href="#c.ssizeobjargproc" title="ssizeobjargproc"><span class="n"><span class="pre">ssizeobjargproc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">sq_ass_item</span></span></span><a class="headerlink" href="#c.PySequenceMethods.sq_ass_item" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="sequence.html#c.PySequence_SetItem" title="PySequence_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_SetItem()</span></code></a> and has the same
signature.  It is also used by <a class="reference internal" href="object.html#c.PyObject_SetItem" title="PyObject_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SetItem()</span></code></a> and
<a class="reference internal" href="object.html#c.PyObject_DelItem" title="PyObject_DelItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_DelItem()</span></code></a>, after trying the item assignment and deletion
via the <a class="reference internal" href="#c.PyMappingMethods.mp_ass_subscript" title="PyMappingMethods.mp_ass_subscript"><code class="xref c c-member docutils literal notranslate"><span class="pre">mp_ass_subscript</span></code></a> slot.
This slot may be left to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if the object does not support
item assignment and deletion.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PySequenceMethods.sq_contains">
<a class="reference internal" href="#c.objobjproc" title="objobjproc"><span class="n"><span class="pre">objobjproc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">sq_contains</span></span></span><a class="headerlink" href="#c.PySequenceMethods.sq_contains" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function may be used by <a class="reference internal" href="sequence.html#c.PySequence_Contains" title="PySequence_Contains"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Contains()</span></code></a> and has the same
signature.  This slot may be left to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, in this case
<code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Contains()</span></code> simply traverses the sequence until it
finds a match.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PySequenceMethods.sq_inplace_concat">
<a class="reference internal" href="#c.binaryfunc" title="binaryfunc"><span class="n"><span class="pre">binaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">sq_inplace_concat</span></span></span><a class="headerlink" href="#c.PySequenceMethods.sq_inplace_concat" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="sequence.html#c.PySequence_InPlaceConcat" title="PySequence_InPlaceConcat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_InPlaceConcat()</span></code></a> and has the same
signature.  It should modify its first operand, and return it.  This slot
may be left to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, in this case <code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_InPlaceConcat()</span></code>
will fall back to <a class="reference internal" href="sequence.html#c.PySequence_Concat" title="PySequence_Concat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Concat()</span></code></a>.  It is also used by the
augmented assignment <code class="docutils literal notranslate"><span class="pre">+=</span></code>, after trying numeric in-place addition
via the <a class="reference internal" href="#c.PyNumberMethods.nb_inplace_add" title="PyNumberMethods.nb_inplace_add"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_add</span></code></a> slot.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PySequenceMethods.sq_inplace_repeat">
<a class="reference internal" href="#c.ssizeargfunc" title="ssizeargfunc"><span class="n"><span class="pre">ssizeargfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PySequenceMethods" title="PySequenceMethods"><span class="n"><span class="pre">PySequenceMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">sq_inplace_repeat</span></span></span><a class="headerlink" href="#c.PySequenceMethods.sq_inplace_repeat" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function is used by <a class="reference internal" href="sequence.html#c.PySequence_InPlaceRepeat" title="PySequence_InPlaceRepeat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_InPlaceRepeat()</span></code></a> and has the same
signature.  It should modify its first operand, and return it.  This slot
may be left to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, in this case <code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_InPlaceRepeat()</span></code>
will fall back to <a class="reference internal" href="sequence.html#c.PySequence_Repeat" title="PySequence_Repeat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Repeat()</span></code></a>.  It is also used by the
augmented assignment <code class="docutils literal notranslate"><span class="pre">*=</span></code>, after trying numeric in-place multiplication
via the <a class="reference internal" href="#c.PyNumberMethods.nb_inplace_multiply" title="PyNumberMethods.nb_inplace_multiply"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_inplace_multiply</span></code></a> slot.</p>
</dd></dl>

</section>
<section id="buffer-object-structures">
<span id="buffer-structs"></span><h2>Buffer Object Structures<a class="headerlink" href="#buffer-object-structures" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyBufferProcs">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBufferProcs</span></span></span><a class="headerlink" href="#c.PyBufferProcs" title="Link to this definition">¶</a><br /></dt>
<dd><p>This structure holds pointers to the functions required by the
<a class="reference internal" href="buffer.html#bufferobjects"><span class="std std-ref">Buffer protocol</span></a>. The protocol defines how
an exporter object can expose its internal data to consumer objects.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyBufferProcs.bf_getbuffer">
<a class="reference internal" href="#c.getbufferproc" title="getbufferproc"><span class="n"><span class="pre">getbufferproc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyBufferProcs" title="PyBufferProcs"><span class="n"><span class="pre">PyBufferProcs</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">bf_getbuffer</span></span></span><a class="headerlink" href="#c.PyBufferProcs.bf_getbuffer" title="Link to this definition">¶</a><br /></dt>
<dd><p>The signature of this function is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">int</span><span class="w"> </span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">exporter</span><span class="p">,</span><span class="w"> </span><span class="n">Py_buffer</span><span class="w"> </span><span class="o">*</span><span class="n">view</span><span class="p">,</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">flags</span><span class="p">);</span>
</pre></div>
</div>
<p>Handle a request to <em>exporter</em> to fill in <em>view</em> as specified by <em>flags</em>.
Except for point (3), an implementation of this function MUST take these
steps:</p>
<ol class="arabic simple">
<li><p>Check if the request can be met. If not, raise <a class="reference internal" href="../library/exceptions.html#BufferError" title="BufferError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BufferError</span></code></a>,
set <span class="c-expr sig sig-inline c"><span class="n">view</span><span class="o">-&gt;</span><span class="n">obj</span></span> to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and return <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p></li>
<li><p>Fill in the requested fields.</p></li>
<li><p>Increment an internal counter for the number of exports.</p></li>
<li><p>Set <span class="c-expr sig sig-inline c"><span class="n">view</span><span class="o">-&gt;</span><span class="n">obj</span></span> to <em>exporter</em> and increment <span class="c-expr sig sig-inline c"><span class="n">view</span><span class="o">-&gt;</span><span class="n">obj</span></span>.</p></li>
<li><p>Return <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p></li>
</ol>
<p>If <em>exporter</em> is part of a chain or tree of buffer providers, two main
schemes can be used:</p>
<ul class="simple">
<li><p>Re-export: Each member of the tree acts as the exporting object and
sets <span class="c-expr sig sig-inline c"><span class="n">view</span><span class="o">-&gt;</span><span class="n">obj</span></span> to a new reference to itself.</p></li>
<li><p>Redirect: The buffer request is redirected to the root object of the
tree. Here, <span class="c-expr sig sig-inline c"><span class="n">view</span><span class="o">-&gt;</span><span class="n">obj</span></span> will be a new reference to the root
object.</p></li>
</ul>
<p>The individual fields of <em>view</em> are described in section
<a class="reference internal" href="buffer.html#buffer-structure"><span class="std std-ref">Buffer structure</span></a>, the rules how an exporter
must react to specific requests are in section
<a class="reference internal" href="buffer.html#buffer-request-types"><span class="std std-ref">Buffer request types</span></a>.</p>
<p>All memory pointed to in the <a class="reference internal" href="buffer.html#c.Py_buffer" title="Py_buffer"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_buffer</span></code></a> structure belongs to
the exporter and must remain valid until there are no consumers left.
<a class="reference internal" href="buffer.html#c.Py_buffer.format" title="Py_buffer.format"><code class="xref c c-member docutils literal notranslate"><span class="pre">format</span></code></a>, <a class="reference internal" href="buffer.html#c.Py_buffer.shape" title="Py_buffer.shape"><code class="xref c c-member docutils literal notranslate"><span class="pre">shape</span></code></a>,
<a class="reference internal" href="buffer.html#c.Py_buffer.strides" title="Py_buffer.strides"><code class="xref c c-member docutils literal notranslate"><span class="pre">strides</span></code></a>, <a class="reference internal" href="buffer.html#c.Py_buffer.suboffsets" title="Py_buffer.suboffsets"><code class="xref c c-member docutils literal notranslate"><span class="pre">suboffsets</span></code></a>
and <a class="reference internal" href="buffer.html#c.Py_buffer.internal" title="Py_buffer.internal"><code class="xref c c-member docutils literal notranslate"><span class="pre">internal</span></code></a>
are read-only for the consumer.</p>
<p><a class="reference internal" href="buffer.html#c.PyBuffer_FillInfo" title="PyBuffer_FillInfo"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_FillInfo()</span></code></a> provides an easy way of exposing a simple
bytes buffer while dealing correctly with all request types.</p>
<p><a class="reference internal" href="buffer.html#c.PyObject_GetBuffer" title="PyObject_GetBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetBuffer()</span></code></a> is the interface for the consumer that
wraps this function.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyBufferProcs.bf_releasebuffer">
<a class="reference internal" href="#c.releasebufferproc" title="releasebufferproc"><span class="n"><span class="pre">releasebufferproc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyBufferProcs" title="PyBufferProcs"><span class="n"><span class="pre">PyBufferProcs</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">bf_releasebuffer</span></span></span><a class="headerlink" href="#c.PyBufferProcs.bf_releasebuffer" title="Link to this definition">¶</a><br /></dt>
<dd><p>The signature of this function is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">exporter</span><span class="p">,</span><span class="w"> </span><span class="n">Py_buffer</span><span class="w"> </span><span class="o">*</span><span class="n">view</span><span class="p">);</span>
</pre></div>
</div>
<p>Handle a request to release the resources of the buffer. If no resources
need to be released, <a class="reference internal" href="#c.PyBufferProcs.bf_releasebuffer" title="PyBufferProcs.bf_releasebuffer"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyBufferProcs.bf_releasebuffer</span></code></a> may be
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>. Otherwise, a standard implementation of this function will take
these optional steps:</p>
<ol class="arabic simple">
<li><p>Decrement an internal counter for the number of exports.</p></li>
<li><p>If the counter is <code class="docutils literal notranslate"><span class="pre">0</span></code>, free all memory associated with <em>view</em>.</p></li>
</ol>
<p>The exporter MUST use the <a class="reference internal" href="buffer.html#c.Py_buffer.internal" title="Py_buffer.internal"><code class="xref c c-member docutils literal notranslate"><span class="pre">internal</span></code></a> field to keep
track of buffer-specific resources. This field is guaranteed to remain
constant, while a consumer MAY pass a copy of the original buffer as the
<em>view</em> argument.</p>
<p>This function MUST NOT decrement <span class="c-expr sig sig-inline c"><span class="n">view</span><span class="o">-&gt;</span><span class="n">obj</span></span>, since that is
done automatically in <a class="reference internal" href="buffer.html#c.PyBuffer_Release" title="PyBuffer_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_Release()</span></code></a> (this scheme is
useful for breaking reference cycles).</p>
<p><a class="reference internal" href="buffer.html#c.PyBuffer_Release" title="PyBuffer_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_Release()</span></code></a> is the interface for the consumer that
wraps this function.</p>
</dd></dl>

</section>
<section id="async-object-structures">
<span id="async-structs"></span><h2>Async Object Structures<a class="headerlink" href="#async-object-structures" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.5.</span></p>
</div>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyAsyncMethods">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyAsyncMethods</span></span></span><a class="headerlink" href="#c.PyAsyncMethods" title="Link to this definition">¶</a><br /></dt>
<dd><p>This structure holds pointers to the functions required to implement
<a class="reference internal" href="../glossary.html#term-awaitable"><span class="xref std std-term">awaitable</span></a> and <a class="reference internal" href="../glossary.html#term-asynchronous-iterator"><span class="xref std std-term">asynchronous iterator</span></a> objects.</p>
<p>Here is the structure definition:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">am_await</span><span class="p">;</span>
<span class="w">    </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">am_aiter</span><span class="p">;</span>
<span class="w">    </span><span class="n">unaryfunc</span><span class="w"> </span><span class="n">am_anext</span><span class="p">;</span>
<span class="w">    </span><span class="n">sendfunc</span><span class="w"> </span><span class="n">am_send</span><span class="p">;</span>
<span class="p">}</span><span class="w"> </span><span class="n">PyAsyncMethods</span><span class="p">;</span>
</pre></div>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyAsyncMethods.am_await">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyAsyncMethods" title="PyAsyncMethods"><span class="n"><span class="pre">PyAsyncMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">am_await</span></span></span><a class="headerlink" href="#c.PyAsyncMethods.am_await" title="Link to this definition">¶</a><br /></dt>
<dd><p>The signature of this function is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">am_await</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>The returned object must be an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a>, i.e. <a class="reference internal" href="iter.html#c.PyIter_Check" title="PyIter_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyIter_Check()</span></code></a>
must return <code class="docutils literal notranslate"><span class="pre">1</span></code> for it.</p>
<p>This slot may be set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an object is not an <a class="reference internal" href="../glossary.html#term-awaitable"><span class="xref std std-term">awaitable</span></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyAsyncMethods.am_aiter">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyAsyncMethods" title="PyAsyncMethods"><span class="n"><span class="pre">PyAsyncMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">am_aiter</span></span></span><a class="headerlink" href="#c.PyAsyncMethods.am_aiter" title="Link to this definition">¶</a><br /></dt>
<dd><p>The signature of this function is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">am_aiter</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>Must return an <a class="reference internal" href="../glossary.html#term-asynchronous-iterator"><span class="xref std std-term">asynchronous iterator</span></a> object.
See <a class="reference internal" href="../reference/datamodel.html#object.__anext__" title="object.__anext__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__anext__()</span></code></a> for details.</p>
<p>This slot may be set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an object does not implement
asynchronous iteration protocol.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyAsyncMethods.am_anext">
<a class="reference internal" href="#c.unaryfunc" title="unaryfunc"><span class="n"><span class="pre">unaryfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyAsyncMethods" title="PyAsyncMethods"><span class="n"><span class="pre">PyAsyncMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">am_anext</span></span></span><a class="headerlink" href="#c.PyAsyncMethods.am_anext" title="Link to this definition">¶</a><br /></dt>
<dd><p>The signature of this function is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">am_anext</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">);</span>
</pre></div>
</div>
<p>Must return an <a class="reference internal" href="../glossary.html#term-awaitable"><span class="xref std std-term">awaitable</span></a> object.
See <a class="reference internal" href="../reference/datamodel.html#object.__anext__" title="object.__anext__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__anext__()</span></code></a> for details.
This slot may be set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyAsyncMethods.am_send">
<a class="reference internal" href="#c.sendfunc" title="sendfunc"><span class="n"><span class="pre">sendfunc</span></span></a><span class="w"> </span><span class="sig-prename descclassname"><a class="reference internal" href="#c.PyAsyncMethods" title="PyAsyncMethods"><span class="n"><span class="pre">PyAsyncMethods</span></span></a><span class="p"><span class="pre">.</span></span></span><span class="sig-name descname"><span class="n"><span class="pre">am_send</span></span></span><a class="headerlink" href="#c.PyAsyncMethods.am_send" title="Link to this definition">¶</a><br /></dt>
<dd><p>The signature of this function is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PySendResult</span><span class="w"> </span><span class="nf">am_send</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">arg</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">**</span><span class="n">result</span><span class="p">);</span>
</pre></div>
</div>
<p>See <a class="reference internal" href="iter.html#c.PyIter_Send" title="PyIter_Send"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyIter_Send()</span></code></a> for details.
This slot may be set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

</section>
<section id="slot-type-typedefs">
<span id="id6"></span><h2>Slot Type typedefs<a class="headerlink" href="#slot-type-typedefs" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.allocfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">allocfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">cls</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">nitems</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.allocfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>The purpose of this function is to separate memory allocation from memory
initialization.  It should return a pointer to a block of memory of adequate
length for the instance, suitably aligned, and initialized to zeros, but with
<a class="reference internal" href="#c.PyObject.ob_refcnt" title="PyObject.ob_refcnt"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_refcnt</span></code></a> set to <code class="docutils literal notranslate"><span class="pre">1</span></code> and <a class="reference internal" href="#c.PyObject.ob_type" title="PyObject.ob_type"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_type</span></code></a> set to the type argument.  If
the type’s <a class="reference internal" href="#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a> is non-zero, the object’s <a class="reference internal" href="#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field
should be initialized to <em>nitems</em> and the length of the allocated memory block
should be <code class="docutils literal notranslate"><span class="pre">tp_basicsize</span> <span class="pre">+</span> <span class="pre">nitems*tp_itemsize</span></code>, rounded up to a multiple of
<code class="docutils literal notranslate"><span class="pre">sizeof(void*)</span></code>; otherwise, <em>nitems</em> is not used and the length of the block
should be <a class="reference internal" href="#c.PyTypeObject.tp_basicsize" title="PyTypeObject.tp_basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code></a>.</p>
<p>This function should not do any other instance initialization, not even to
allocate additional memory; that should be done by <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.destructor">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">destructor</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.destructor" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.freefunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">freefunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><span class="kt"><span class="pre">void</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.freefunc" title="Link to this definition">¶</a><br /></dt>
<dd><p>See <a class="reference internal" href="#c.PyTypeObject.tp_free" title="PyTypeObject.tp_free"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_free</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.newfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">newfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.newfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.initproc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">initproc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.initproc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.reprfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">reprfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.reprfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_repr" title="PyTypeObject.tp_repr"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_repr</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.getattrfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">getattrfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">self</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">attr</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.getattrfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the value of the named attribute for the object.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.setattrfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">setattrfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">self</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">attr</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.setattrfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Set the value of the named attribute for the object.
The value argument is set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to delete the attribute.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.getattrofunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">getattrofunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">self</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">attr</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.getattrofunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the value of the named attribute for the object.</p>
<p>See <a class="reference internal" href="#c.PyTypeObject.tp_getattro" title="PyTypeObject.tp_getattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_getattro</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.setattrofunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">setattrofunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">self</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">attr</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.setattrofunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Set the value of the named attribute for the object.
The value argument is set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to delete the attribute.</p>
<p>See <a class="reference internal" href="#c.PyTypeObject.tp_setattro" title="PyTypeObject.tp_setattro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_setattro</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.descrgetfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">descrgetfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.descrgetfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_descr_get" title="PyTypeObject.tp_descr_get"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_descr_get</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.descrsetfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">descrsetfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.descrsetfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_descr_set" title="PyTypeObject.tp_descr_set"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_descr_set</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.hashfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="hash.html#c.Py_hash_t" title="Py_hash_t"><span class="n"><span class="pre">Py_hash_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">hashfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.hashfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_hash</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.richcmpfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">richcmpfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.richcmpfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_richcompare" title="PyTypeObject.tp_richcompare"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_richcompare</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.getiterfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">getiterfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.getiterfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_iter" title="PyTypeObject.tp_iter"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_iter</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.iternextfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">iternextfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.iternextfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>See <a class="reference internal" href="#c.PyTypeObject.tp_iternext" title="PyTypeObject.tp_iternext"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_iternext</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.lenfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">lenfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.lenfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.getbufferproc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">getbufferproc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="buffer.html#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.getbufferproc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.releasebufferproc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">releasebufferproc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="buffer.html#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.releasebufferproc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.unaryfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">unaryfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.unaryfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.binaryfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">binaryfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.binaryfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.sendfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="iter.html#c.PySendResult" title="PySendResult"><span class="n"><span class="pre">PySendResult</span></span></a><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">sendfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.sendfunc" title="Link to this definition">¶</a><br /></dt>
<dd><p>See <a class="reference internal" href="#c.PyAsyncMethods.am_send" title="PyAsyncMethods.am_send"><code class="xref c c-member docutils literal notranslate"><span class="pre">am_send</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.ternaryfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">ternaryfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.ternaryfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.ssizeargfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">ssizeargfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.ssizeargfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.ssizeobjargproc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">ssizeobjargproc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.ssizeobjargproc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.objobjproc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">objobjproc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.objobjproc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.objobjargproc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">objobjargproc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.objobjargproc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

</section>
<section id="examples">
<span id="typedef-examples"></span><h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>The following are simple examples of Python type definitions.  They
include common usage you may encounter.  Some demonstrate tricky corner
cases.  For more examples, practical info, and a tutorial, see
<a class="reference internal" href="../extending/newtypes_tutorial.html#defining-new-types"><span class="std std-ref">Defining Extension Types: Tutorial</span></a> and <a class="reference internal" href="../extending/newtypes.html#new-types-topics"><span class="std std-ref">Defining Extension Types: Assorted Topics</span></a>.</p>
<p>A basic <a class="reference internal" href="#static-types"><span class="std std-ref">static type</span></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyObject_HEAD</span>
<span class="w">    </span><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">data</span><span class="p">;</span>
<span class="p">}</span><span class="w"> </span><span class="n">MyObject</span><span class="p">;</span>

<span class="k">static</span><span class="w"> </span><span class="n">PyTypeObject</span><span class="w"> </span><span class="n">MyObject_Type</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyVarObject_HEAD_INIT</span><span class="p">(</span><span class="nb">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;mymod.MyObject&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_basicsize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">MyObject</span><span class="p">),</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_doc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyDoc_STR</span><span class="p">(</span><span class="s">&quot;My objects&quot;</span><span class="p">),</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_new</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">myobj_new</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_dealloc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">destructor</span><span class="p">)</span><span class="n">myobj_dealloc</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_repr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">reprfunc</span><span class="p">)</span><span class="n">myobj_repr</span><span class="p">,</span>
<span class="p">};</span>
</pre></div>
</div>
<p>You may also find older code (especially in the CPython code base)
with a more verbose initializer:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="n">PyTypeObject</span><span class="w"> </span><span class="n">MyObject_Type</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyVarObject_HEAD_INIT</span><span class="p">(</span><span class="nb">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span>
<span class="w">    </span><span class="s">&quot;mymod.MyObject&quot;</span><span class="p">,</span><span class="w">               </span><span class="cm">/* tp_name */</span>
<span class="w">    </span><span class="k">sizeof</span><span class="p">(</span><span class="n">MyObject</span><span class="p">),</span><span class="w">               </span><span class="cm">/* tp_basicsize */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_itemsize */</span>
<span class="w">    </span><span class="p">(</span><span class="n">destructor</span><span class="p">)</span><span class="n">myobj_dealloc</span><span class="p">,</span><span class="w">      </span><span class="cm">/* tp_dealloc */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_vectorcall_offset */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_getattr */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_setattr */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_as_async */</span>
<span class="w">    </span><span class="p">(</span><span class="n">reprfunc</span><span class="p">)</span><span class="n">myobj_repr</span><span class="p">,</span><span class="w">           </span><span class="cm">/* tp_repr */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_as_number */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_as_sequence */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_as_mapping */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_hash */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_call */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_str */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_getattro */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_setattro */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_as_buffer */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_flags */</span>
<span class="w">    </span><span class="n">PyDoc_STR</span><span class="p">(</span><span class="s">&quot;My objects&quot;</span><span class="p">),</span><span class="w">        </span><span class="cm">/* tp_doc */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_traverse */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_clear */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_richcompare */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_weaklistoffset */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_iter */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_iternext */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_methods */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_members */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_getset */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_base */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_dict */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_descr_get */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_descr_set */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_dictoffset */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_init */</span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w">                              </span><span class="cm">/* tp_alloc */</span>
<span class="w">    </span><span class="n">myobj_new</span><span class="p">,</span><span class="w">                      </span><span class="cm">/* tp_new */</span>
<span class="p">};</span>
</pre></div>
</div>
<p>A type that supports weakrefs, instance dicts, and hashing:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyObject_HEAD</span>
<span class="w">    </span><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">data</span><span class="p">;</span>
<span class="p">}</span><span class="w"> </span><span class="n">MyObject</span><span class="p">;</span>

<span class="k">static</span><span class="w"> </span><span class="n">PyTypeObject</span><span class="w"> </span><span class="n">MyObject_Type</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyVarObject_HEAD_INIT</span><span class="p">(</span><span class="nb">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;mymod.MyObject&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_basicsize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">MyObject</span><span class="p">),</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_doc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyDoc_STR</span><span class="p">(</span><span class="s">&quot;My objects&quot;</span><span class="p">),</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_flags</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_TPFLAGS_DEFAULT</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="n">Py_TPFLAGS_BASETYPE</span><span class="w"> </span><span class="o">|</span>
<span class="w">         </span><span class="n">Py_TPFLAGS_HAVE_GC</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="n">Py_TPFLAGS_MANAGED_DICT</span><span class="w"> </span><span class="o">|</span>
<span class="w">         </span><span class="n">Py_TPFLAGS_MANAGED_WEAKREF</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_new</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">myobj_new</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_traverse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">traverseproc</span><span class="p">)</span><span class="n">myobj_traverse</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_clear</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">inquiry</span><span class="p">)</span><span class="n">myobj_clear</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_alloc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyType_GenericNew</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_dealloc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">destructor</span><span class="p">)</span><span class="n">myobj_dealloc</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_repr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">reprfunc</span><span class="p">)</span><span class="n">myobj_repr</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_hash</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">hashfunc</span><span class="p">)</span><span class="n">myobj_hash</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_richcompare</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyBaseObject_Type</span><span class="p">.</span><span class="n">tp_richcompare</span><span class="p">,</span>
<span class="p">};</span>
</pre></div>
</div>
<p>A str subclass that cannot be subclassed and cannot be called
to create instances (e.g. uses a separate factory func) using
<a class="reference internal" href="#c.Py_TPFLAGS_DISALLOW_INSTANTIATION" title="Py_TPFLAGS_DISALLOW_INSTANTIATION"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_DISALLOW_INSTANTIATION</span></code></a> flag:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyUnicodeObject</span><span class="w"> </span><span class="n">raw</span><span class="p">;</span>
<span class="w">    </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">extra</span><span class="p">;</span>
<span class="p">}</span><span class="w"> </span><span class="n">MyStr</span><span class="p">;</span>

<span class="k">static</span><span class="w"> </span><span class="n">PyTypeObject</span><span class="w"> </span><span class="n">MyStr_Type</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyVarObject_HEAD_INIT</span><span class="p">(</span><span class="nb">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;mymod.MyStr&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_basicsize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">MyStr</span><span class="p">),</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_base</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">NULL</span><span class="p">,</span><span class="w">  </span><span class="c1">// set to &amp;PyUnicode_Type in module init</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_doc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyDoc_STR</span><span class="p">(</span><span class="s">&quot;my custom str&quot;</span><span class="p">),</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_flags</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_TPFLAGS_DEFAULT</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="n">Py_TPFLAGS_DISALLOW_INSTANTIATION</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_repr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">reprfunc</span><span class="p">)</span><span class="n">myobj_repr</span><span class="p">,</span>
<span class="p">};</span>
</pre></div>
</div>
<p>The simplest <a class="reference internal" href="#static-types"><span class="std std-ref">static type</span></a> with fixed-length instances:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyObject_HEAD</span>
<span class="p">}</span><span class="w"> </span><span class="n">MyObject</span><span class="p">;</span>

<span class="k">static</span><span class="w"> </span><span class="n">PyTypeObject</span><span class="w"> </span><span class="n">MyObject_Type</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyVarObject_HEAD_INIT</span><span class="p">(</span><span class="nb">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;mymod.MyObject&quot;</span><span class="p">,</span>
<span class="p">};</span>
</pre></div>
</div>
<p>The simplest <a class="reference internal" href="#static-types"><span class="std std-ref">static type</span></a> with variable-length instances:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyObject_VAR_HEAD</span>
<span class="w">    </span><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">data</span><span class="p">[</span><span class="mi">1</span><span class="p">];</span>
<span class="p">}</span><span class="w"> </span><span class="n">MyObject</span><span class="p">;</span>

<span class="k">static</span><span class="w"> </span><span class="n">PyTypeObject</span><span class="w"> </span><span class="n">MyObject_Type</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">PyVarObject_HEAD_INIT</span><span class="p">(</span><span class="nb">NULL</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;mymod.MyObject&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_basicsize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="n">MyObject</span><span class="p">)</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="p">),</span>
<span class="w">    </span><span class="p">.</span><span class="n">tp_itemsize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">sizeof</span><span class="p">(</span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="p">),</span>
<span class="p">};</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Type Object Structures</a><ul>
<li><a class="reference internal" href="#quick-reference">Quick Reference</a><ul>
<li><a class="reference internal" href="#tp-slots">“tp slots”</a></li>
<li><a class="reference internal" href="#sub-slots">sub-slots</a></li>
<li><a class="reference internal" href="#slot-typedefs">slot typedefs</a></li>
</ul>
</li>
<li><a class="reference internal" href="#pytypeobject-definition">PyTypeObject Definition</a></li>
<li><a class="reference internal" href="#pyobject-slots">PyObject Slots</a></li>
<li><a class="reference internal" href="#pyvarobject-slots">PyVarObject Slots</a></li>
<li><a class="reference internal" href="#pytypeobject-slots">PyTypeObject Slots</a></li>
<li><a class="reference internal" href="#static-types">Static Types</a></li>
<li><a class="reference internal" href="#heap-types">Heap Types</a></li>
<li><a class="reference internal" href="#number-object-structures">Number Object Structures</a></li>
<li><a class="reference internal" href="#mapping-object-structures">Mapping Object Structures</a></li>
<li><a class="reference internal" href="#sequence-object-structures">Sequence Object Structures</a></li>
<li><a class="reference internal" href="#buffer-object-structures">Buffer Object Structures</a></li>
<li><a class="reference internal" href="#async-object-structures">Async Object Structures</a></li>
<li><a class="reference internal" href="#slot-type-typedefs">Slot Type typedefs</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="structures.html"
                          title="previous chapter">Common Object Structures</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="gcsupport.html"
                          title="next chapter">Supporting Cyclic Garbage Collection</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/typeobj.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="gcsupport.html" title="Supporting Cyclic Garbage Collection"
             >next</a> |</li>
        <li class="right" >
          <a href="structures.html" title="Common Object Structures"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="objimpl.html" >Object Implementation Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Type Object Structures</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>