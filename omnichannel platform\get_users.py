import sqlite3

def get_all_users(db_path):
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, email, username, role, company_id FROM user")
        users = cursor.fetchall()
        if users:
            print("Existing Users:")
            for user in users:
                print(f"  ID={user[0]}, Email={user[1]}, Username={user[2]}, Role={user[3]}, Company ID={user[4]}")
        else:
            print("No users found in the database.")
    except sqlite3.Error as e:
        print(f"Database error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    db_file = "F:\\great_things\\omnichannel platform\\whatsapp_platform_dev.db"
    get_all_users(db_file)