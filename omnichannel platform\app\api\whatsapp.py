"""API endpoints for WhatsApp functionality."""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from app.database import get_session
from app.models.whatsapp import WhatsAppAccount, WhatsAppAccountStatus
from app.models.schemas import (
    WhatsAppAccountCreate, WhatsAppAccountRead,
    WhatsAppAccountUpdate, ListResponse
)
from app.services.whatsapp_api import WhatsAppAPIService

router = APIRouter(prefix="/whatsapp", tags=["whatsapp"])

# Pydantic models for message sending
class MessageSendRequest(BaseModel):
    phone_number: str
    message_data: Dict[str, Any]

class MessageSendResponse(BaseModel):
    success: bool
    message_id: Optional[str] = None
    error: Optional[str] = None
    whatsapp_response: Optional[Dict[str, Any]] = None

# WhatsApp Accounts
@router.post("/accounts/", response_model=WhatsAppAccountRead)
def create_whatsapp_account(
    account: WhatsAppAccountCreate,
    session: Session = Depends(get_session)
):
    """Create a new WhatsApp account."""
    db_account = WhatsAppAccount.from_orm(account)
    session.add(db_account)
    session.commit()
    session.refresh(db_account)
    return db_account

@router.get("/accounts/", response_model=ListResponse)
def list_whatsapp_accounts(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    session: Session = Depends(get_session)
):
    """List all WhatsApp accounts with optional filtering."""
    query = select(WhatsAppAccount)
    
    if status:
        query = query.where(WhatsAppAccount.status == status)
    
    total = len(session.exec(query).all())
    accounts = session.exec(query.offset(skip).limit(limit)).all()
    
    return ListResponse(
        items=accounts,
        total=total,
        page=skip // limit + 1,
        size=len(accounts),
        pages=(total + limit - 1) // limit if limit > 0 else 1
    )

@router.get("/accounts/{account_id}", response_model=WhatsAppAccountRead)
def get_whatsapp_account(
    account_id: int,
    session: Session = Depends(get_session)
):
    """Get a specific WhatsApp account by ID."""
    account = session.get(WhatsAppAccount, account_id)
    if not account:
        raise HTTPException(status_code=404, detail="Account not found")
    return account

@router.patch("/accounts/{account_id}", response_model=WhatsAppAccountRead)
def update_whatsapp_account(
    account_id: int,
    account_update: WhatsAppAccountUpdate,
    session: Session = Depends(get_session)
):
    """Update a WhatsApp account."""
    db_account = session.get(WhatsAppAccount, account_id)
    if not db_account:
        raise HTTPException(status_code=404, detail="Account not found")
    
    account_data = account_update.dict(exclude_unset=True)
    
    # Handle metadata update
    if 'metadata_' in account_data:
        account_data['metadata'] = account_data.pop('metadata_')
    
    for key, value in account_data.items():
        setattr(db_account, key, value)
    
    session.add(db_account)
    session.commit()
    session.refresh(db_account)
    return db_account

@router.delete("/accounts/{account_id}")
def delete_whatsapp_account(
    account_id: int,
    session: Session = Depends(get_session)
):
    """Delete a WhatsApp account (soft delete)."""
    db_account = session.get(WhatsAppAccount, account_id)
    if not db_account:
        raise HTTPException(status_code=404, detail="Account not found")
    
    db_account.is_active = False
    session.add(db_account)
    session.commit()
    return {"ok": True}

# Message Sending Endpoints
@router.post("/send-message", response_model=MessageSendResponse)
async def send_whatsapp_message(
    request: MessageSendRequest,
    session: Session = Depends(get_session)
):
    """Send a WhatsApp message using the configured account."""
    try:
        # Get the first active WhatsApp account
        query = select(WhatsAppAccount).where(
            WhatsAppAccount.is_active == True,
            WhatsAppAccount.status == WhatsAppAccountStatus.ACTIVE
        )
        account = session.exec(query).first()

        if not account:
            raise HTTPException(
                status_code=400,
                detail="No active WhatsApp account configured. Please configure WhatsApp integration first."
            )

        # Initialize the WhatsApp API service
        api_service = WhatsAppAPIService(
            access_token=account.access_token,
            phone_number_id=account.phone_number_id
        )

        # Determine message type and send
        message_data = request.message_data
        message_type = message_data.get("type", "text")

        if message_type == "text":
            result = api_service.send_text_message(
                to=request.phone_number,
                text=message_data["text"]["body"]
            )
        elif message_type == "template":
            template = message_data["template"]
            parameters = []

            # Extract parameters from components
            if "components" in template:
                for component in template["components"]:
                    if component["type"] == "body" and "parameters" in component:
                        parameters = [p["text"] for p in component["parameters"]]

            result = api_service.send_template_message(
                to=request.phone_number,
                template_name=template["name"],
                language_code=template["language"]["code"],
                parameters=parameters
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported message type: {message_type}"
            )

        # Extract message ID from WhatsApp response
        message_id = None
        if "messages" in result and len(result["messages"]) > 0:
            message_id = result["messages"][0].get("id")

        return MessageSendResponse(
            success=True,
            message_id=message_id,
            whatsapp_response=result
        )

    except HTTPException:
        raise
    except Exception as e:
        return MessageSendResponse(
            success=False,
            error=str(e)
        )

# Template Management Endpoints
@router.get("/templates/")
async def list_whatsapp_templates(
    session: Session = Depends(get_session)
):
    """List available WhatsApp message templates."""
    try:
        # Get the first active WhatsApp account
        query = select(WhatsAppAccount).where(
            WhatsAppAccount.is_active == True,
            WhatsAppAccount.status == WhatsAppAccountStatus.ACTIVE
        )
        account = session.exec(query).first()

        if not account:
            # Return mock templates if no account configured
            return [
                {
                    "id": "welcome",
                    "name": "welcome",
                    "language": "en",
                    "body": "Hello {{1}}, welcome to our service!",
                    "status": "APPROVED"
                },
                {
                    "id": "order_confirmation",
                    "name": "order_confirmation",
                    "language": "en",
                    "body": "Your order {{1}} has been confirmed. Total: {{2}}",
                    "status": "APPROVED"
                },
                {
                    "id": "appointment_reminder",
                    "name": "appointment_reminder",
                    "language": "en",
                    "body": "Reminder: You have an appointment on {{1}} at {{2}}",
                    "status": "APPROVED"
                }
            ]

        # Initialize the WhatsApp API service
        api_service = WhatsAppAPIService(
            access_token=account.access_token,
            phone_number_id=account.phone_number_id
        )

        # Get templates from WhatsApp API
        # Note: This would require implementing template fetching in the API service
        # For now, return mock templates
        return [
            {
                "id": "welcome",
                "name": "welcome",
                "language": "en",
                "body": "Hello {{1}}, welcome to our service!",
                "status": "APPROVED"
            },
            {
                "id": "order_confirmation",
                "name": "order_confirmation",
                "language": "en",
                "body": "Your order {{1}} has been confirmed. Total: {{2}}",
                "status": "APPROVED"
            }
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching templates: {str(e)}")

# WhatsApp Calling Endpoints

class CallInitiateRequest(BaseModel):
    phone_number: str
    call_type: str = "voice"  # "voice" or "video"
    account_id: Optional[int] = None

class CallResponse(BaseModel):
    success: bool
    call_id: Optional[str] = None
    status: Optional[str] = None
    error: Optional[str] = None
    timestamp: Optional[str] = None

@router.post("/calls/initiate", response_model=CallResponse)
async def initiate_call(
    request: CallInitiateRequest,
    session: Session = Depends(get_session)
):
    """Initiate a WhatsApp call."""
    try:
        # Get the first active WhatsApp account if no account_id specified
        if request.account_id:
            account = session.get(WhatsAppAccount, request.account_id)
        else:
            account = session.exec(
                select(WhatsAppAccount)
                .where(WhatsAppAccount.status == WhatsAppAccountStatus.ACTIVE)
            ).first()

        if not account:
            raise HTTPException(status_code=404, detail="No active WhatsApp account found")

        # Validate call type
        if request.call_type not in ["voice", "video"]:
            raise HTTPException(status_code=400, detail="Call type must be 'voice' or 'video'")

        # Initialize API service
        api_service = WhatsAppAPIService(account)

        # Initiate the call
        result = await api_service.initiate_call(request.phone_number, request.call_type)

        return CallResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error initiating call: {str(e)}")

@router.get("/calls/{call_id}/status", response_model=CallResponse)
async def get_call_status(
    call_id: str,
    account_id: Optional[int] = None,
    session: Session = Depends(get_session)
):
    """Get the status of a WhatsApp call."""
    try:
        # Get the first active WhatsApp account if no account_id specified
        if account_id:
            account = session.get(WhatsAppAccount, account_id)
        else:
            account = session.exec(
                select(WhatsAppAccount)
                .where(WhatsAppAccount.status == WhatsAppAccountStatus.ACTIVE)
            ).first()

        if not account:
            raise HTTPException(status_code=404, detail="No active WhatsApp account found")

        # Initialize API service
        api_service = WhatsAppAPIService(account)

        # Get call status
        result = await api_service.get_call_status(call_id)

        return CallResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting call status: {str(e)}")

@router.post("/calls/{call_id}/end", response_model=CallResponse)
async def end_call(
    call_id: str,
    account_id: Optional[int] = None,
    session: Session = Depends(get_session)
):
    """End a WhatsApp call."""
    try:
        # Get the first active WhatsApp account if no account_id specified
        if account_id:
            account = session.get(WhatsAppAccount, account_id)
        else:
            account = session.exec(
                select(WhatsAppAccount)
                .where(WhatsAppAccount.status == WhatsAppAccountStatus.ACTIVE)
            ).first()

        if not account:
            raise HTTPException(status_code=404, detail="No active WhatsApp account found")

        # Initialize API service
        api_service = WhatsAppAPIService(account)

        # End the call
        result = await api_service.end_call(call_id)

        return CallResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ending call: {str(e)}")

# Add more endpoints for other models as needed
