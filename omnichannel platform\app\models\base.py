"""Base database model with common fields and functionality."""

from datetime import datetime
from typing import Optional, Dict, Any, List, ClassVar
from sqlmodel import SQLModel, Field, DateTime, func, Boolean, Integer, ForeignKey
from sqlalchemy import Column, JSON, text, event, String
from sqlalchemy.ext.declarative import declared_attr
from pydantic import BaseModel as PydanticBaseModel
import uuid


class BaseModel(SQLModel):
    """Base model with common fields and functionality.
    
    This model serves as the base for all database models in the application.
    It includes common fields like timestamps, soft delete, and audit fields.
    """
    __abstract__ = True  # This makes SQLAlchemy treat this as an abstract base class
    
    # Primary key
    id: Optional[int] = Field(default=None, primary_key=True, index=True)
    
    # Timestamps - these are just type hints for SQLModel/Pydantic
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # Timestamps - SQLAlchemy columns with declared_attr
    @declared_attr
    def created_at(cls):
        return Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    @declared_attr
    def updated_at(cls):
        return Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    

    # Soft delete - type hints for SQLModel/Pydantic
    

    # Audit fields
    created_by: Optional[int] = Field(default=None)
    updated_by: Optional[int] = Field(default=None)
    deleted_by: Optional[int] = Field(default=None)

    @declared_attr
    def __sa_column_created_by__(cls):
        return Column("created_by", Integer, ForeignKey("users.id"), nullable=True)

    @declared_attr
    def __sa_column_updated_by__(cls):
        return Column("updated_by", Integer, ForeignKey("users.id"), nullable=True)

    @declared_attr
    def __sa_column_deleted_by__(cls):
        return Column("deleted_by", Integer, ForeignKey("users.id"), nullable=True)
    
    # JSON metadata field with SQLAlchemy column definition
    metadata_: Dict[str, Any] = Field(default_factory=dict)

    @declared_attr
    def __sa_column_metadata__(cls):
        return Column("metadata", JSON, default=dict, server_default=text("'{}'"))
    
    # Class configuration
    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
        }
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"


class TimestampMixin:
    """Mixin for models that need timestamp tracking.
    
    Note: This class is now a compatibility layer. Timestamps are now handled by BaseModel.
    """
    # Timestamps are now defined in BaseModel to avoid duplication
    pass





class AuditMixin:
    """Mixin for models that need audit trail."""
    
    created_by: Optional[str] = Column(String(255), nullable=True)
    updated_by: Optional[str] = Column(String(255), nullable=True)
    
    def set_created_by(self, user_id: str) -> None:
        """Set the user who created this record."""
        self.created_by = user_id
    
    def set_updated_by(self, user_id: str) -> None:
        """Set the user who last updated this record."""
        self.updated_by = user_id
