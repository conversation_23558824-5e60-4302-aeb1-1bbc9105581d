"""Main entry point for WhatsApp Business Platform."""

import sys
import codecs

sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())

from app.main import app
from app.database import init_database, create_sample_data

if __name__ == "__main__":
    # Initialize database on startup
    print("Initializing database...")
    init_database()
    
    print("Creating sample data...")
    create_sample_data()
    
    print("Starting WhatsApp Business Platform Backend...")
    print("🚀 Access the API at: http://localhost:8001/api")
    
    # Run the app
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)