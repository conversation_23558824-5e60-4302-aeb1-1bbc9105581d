import {
  Box,
  Heading,
  VStack,
  HStack,
  Text,
  FormControl,
  FormLabel,
  Input,
  Button,
  useToast,
  Card,
  CardHeader,
  CardBody,
  Divider,
  Switch,
  Select,
  Tabs, TabList, TabPanels, Tab, TabPanel
} from '@chakra-ui/react';
import { useState } from 'react';
import TelegramSettings from './settings/TelegramSettings.tsx'; // Corrected import path

const Settings = () => {
  const toast = useToast();
  const [isSaving, setIsSaving] = useState(false);
  
  // Mock settings data
  const [settings, setSettings] = useState({
    apiKey: 'sk_test_***********************',
    webhookUrl: 'https://example.com/webhook',
    notificationsEnabled: true,
    language: 'en',
    timezone: 'UTC'
  });

  const handleSave = () => {
    setIsSaving(true);
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      toast({
        title: 'Settings saved',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }, 1000);
  };

  return (
    <Box>
      <Heading mb={6}>Settings</Heading>
      
      <Tabs variant="enclosed">
        <TabList>
          <Tab>General</Tab>
          <Tab>Telegram</Tab>
          {/* Add other tabs here as needed */}
        </TabList>

        <TabPanels>
          <TabPanel>
            <VStack spacing={6} align="stretch">
              <Card>
                <CardHeader>
                  <Heading size="md">API Configuration</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <FormControl>
                      <FormLabel>API Key</FormLabel>
                      <Input 
                        type="password" 
                        value={settings.apiKey}
                        onChange={(e) => setSettings({...settings, apiKey: e.target.value})}
                      />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Webhook URL</FormLabel>
                      <Input 
                        value={settings.webhookUrl}
                        onChange={(e) => setSettings({...settings, webhookUrl: e.target.value})}
                      />
                    </FormControl>
                  </VStack>
                </CardBody>
              </Card>
              
              <Card>
                <CardHeader>
                  <Heading size="md">Preferences</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <FormControl display="flex" alignItems="center">
                      <FormLabel htmlFor="notifications" mb="0">
                        Enable Notifications
                      </FormLabel>
                      <Switch 
                        id="notifications" 
                        isChecked={settings.notificationsEnabled}
                        onChange={(e) => setSettings({...settings, notificationsEnabled: e.target.checked})}
                      />
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Language</FormLabel>
                      <Select 
                        value={settings.language}
                        onChange={(e) => setSettings({...settings, language: e.target.value})}
                      >
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                      </Select>
                    </FormControl>
                    
                    <FormControl>
                      <FormLabel>Timezone</FormLabel>
                      <Select 
                        value={settings.timezone}
                        onChange={(e) => setSettings({...settings, timezone: e.target.value})}
                      >
                        <option value="UTC">UTC</option>
                        <option value="EST">Eastern Standard Time</option>
                        <option value="CST">Central Standard Time</option>
                        <option value="PST">Pacific Standard Time</option>
                      </Select>
                    </FormControl>
                  </VStack>
                </CardBody>
              </Card>
              
              <HStack justifyContent="flex-end">
                <Button variant="outline" mr={3}>
                  Cancel
                </Button>
                <Button 
                  colorScheme="blue" 
                  onClick={handleSave}
                  isLoading={isSaving}
                  loadingText="Saving"
                >
                  Save Changes
                </Button>
              </HStack>
            </VStack>
          </TabPanel>

          <TabPanel>
            <TelegramSettings />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default Settings;