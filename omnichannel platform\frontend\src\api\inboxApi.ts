import { apiRequest } from './api';
import { ChannelType } from '../types/omnichannel';

export interface Channel {
  id: number;
  company_id: number;
  name: string;
  channel_type: ChannelType;
  description: string | null;
  status: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const listChannels = async (): Promise<Channel[]> => {
  const response = await apiRequest<Channel[]>({
    url: '/conversations/',
    method: 'GET',
  });
  return response.data;
};

export const getChannel = async (id: number): Promise<Channel> => {
  const response = await apiRequest<Channel>({
    url: `/conversations/channels/${id}`,
    method: 'GET',
  });
  return response.data;
};

export interface UpdateChannelData {
  name: string;
  description?: string | null;
  is_active: boolean;
  // Add other fields that can be updated
}

export const updateChannel = async (id: number, data: UpdateChannelData): Promise<Channel> => {
  const response = await apiRequest<Channel>({
    url: `/conversations/channels/${id}`,
    method: 'PUT',
    data,
  });
  return response.data;
};

export const deleteChannel = async (id: number): Promise<void> => {
  await apiRequest({
    url: `/conversations/channels/${id}`,
    method: 'DELETE',
  });
};
