<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Instance Method Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/method.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="An instance method is a wrapper for a PyCFunction and the new way to bind a PyCFunction to a class object. It replaces the former call PyMethod_New(func, NULL, class). Method Objects: Methods are b..." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="An instance method is a wrapper for a PyCFunction and the new way to bind a PyCFunction to a class object. It replaces the former call PyMethod_New(func, NULL, class). Method Objects: Methods are b..." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Instance Method Objects &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Cell Objects" href="cell.html" />
    <link rel="prev" title="Function Objects" href="function.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/method.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Instance Method Objects</a></li>
<li><a class="reference internal" href="#method-objects">Method Objects</a></li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="function.html"
                          title="previous chapter">Function Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cell.html"
                          title="next chapter">Cell Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/method.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="cell.html" title="Cell Objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="function.html" title="Function Objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" accesskey="U">Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Instance Method Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="instance-method-objects">
<span id="instancemethod-objects"></span><h1>Instance Method Objects<a class="headerlink" href="#instance-method-objects" title="Link to this heading">¶</a></h1>
<p id="index-0">An instance method is a wrapper for a <a class="reference internal" href="structures.html#c.PyCFunction" title="PyCFunction"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunction</span></code></a> and the new way
to bind a <a class="reference internal" href="structures.html#c.PyCFunction" title="PyCFunction"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunction</span></code></a> to a class object. It replaces the former call
<code class="docutils literal notranslate"><span class="pre">PyMethod_New(func,</span> <span class="pre">NULL,</span> <span class="pre">class)</span></code>.</p>
<dl class="c var">
<dt class="sig sig-object c" id="c.PyInstanceMethod_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyInstanceMethod_Type</span></span></span><a class="headerlink" href="#c.PyInstanceMethod_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p>This instance of <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> represents the Python instance
method type. It is not exposed to Python programs.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInstanceMethod_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyInstanceMethod_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInstanceMethod_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>o</em> is an instance method object (has type
<a class="reference internal" href="#c.PyInstanceMethod_Type" title="PyInstanceMethod_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyInstanceMethod_Type</span></code></a>).  The parameter must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInstanceMethod_New">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInstanceMethod_New</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">func</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInstanceMethod_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Return a new instance method object, with <em>func</em> being any callable object.
<em>func</em> is the function that will be called when the instance method is
called.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInstanceMethod_Function">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInstanceMethod_Function</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">im</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInstanceMethod_Function" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return the function object associated with the instance method <em>im</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInstanceMethod_GET_FUNCTION">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInstanceMethod_GET_FUNCTION</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">im</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInstanceMethod_GET_FUNCTION" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Macro version of <a class="reference internal" href="#c.PyInstanceMethod_Function" title="PyInstanceMethod_Function"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInstanceMethod_Function()</span></code></a> which avoids error checking.</p>
</dd></dl>

</section>
<section id="method-objects">
<span id="id1"></span><h1>Method Objects<a class="headerlink" href="#method-objects" title="Link to this heading">¶</a></h1>
<p id="index-1">Methods are bound function objects. Methods are always bound to an instance of
a user-defined class. Unbound methods (methods bound to a class object) are
no longer available.</p>
<dl class="c var">
<dt class="sig sig-object c" id="c.PyMethod_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMethod_Type</span></span></span><a class="headerlink" href="#c.PyMethod_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-2">This instance of <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> represents the Python method type.  This
is exposed to Python programs as <code class="docutils literal notranslate"><span class="pre">types.MethodType</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMethod_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMethod_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMethod_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>o</em> is a method object (has type <a class="reference internal" href="#c.PyMethod_Type" title="PyMethod_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyMethod_Type</span></code></a>).  The
parameter must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMethod_New">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyMethod_New</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">func</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">self</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMethod_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Return a new method object, with <em>func</em> being any callable object and <em>self</em>
the instance the method should be bound. <em>func</em> is the function that will
be called when the method is called. <em>self</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMethod_Function">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyMethod_Function</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">meth</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMethod_Function" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return the function object associated with the method <em>meth</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMethod_GET_FUNCTION">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyMethod_GET_FUNCTION</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">meth</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMethod_GET_FUNCTION" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Macro version of <a class="reference internal" href="#c.PyMethod_Function" title="PyMethod_Function"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMethod_Function()</span></code></a> which avoids error checking.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMethod_Self">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyMethod_Self</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">meth</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMethod_Self" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return the instance associated with the method <em>meth</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMethod_GET_SELF">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyMethod_GET_SELF</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">meth</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMethod_GET_SELF" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Macro version of <a class="reference internal" href="#c.PyMethod_Self" title="PyMethod_Self"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMethod_Self()</span></code></a> which avoids error checking.</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Instance Method Objects</a></li>
<li><a class="reference internal" href="#method-objects">Method Objects</a></li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="function.html"
                          title="previous chapter">Function Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cell.html"
                          title="next chapter">Cell Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/method.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="cell.html" title="Cell Objects"
             >next</a> |</li>
        <li class="right" >
          <a href="function.html" title="Function Objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" >Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Instance Method Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>