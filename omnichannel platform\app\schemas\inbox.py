from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from app.models.omnichannel import ChannelType, ChannelStatus

class ChannelRead(BaseModel):
    """Schema for reading channel data."""
    id: int
    company_id: int
    name: str
    channel_type: ChannelType
    description: Optional[str] = None
    config: Dict[str, Any] = Field(default_factory=dict)
    status: ChannelStatus = ChannelStatus.ACTIVE
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class MessageRead(BaseModel):
    id: int
    conversation_id: int
    channel_id: int
    customer_id: int
    message_type: Optional[str] = None
    content: str
    direction: str
    status: str
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    class Config:
        from_attributes = True

class ConversationRead(BaseModel):
    id: int
    company_id: int
    customer_id: int
    channel_id: int
    status: str
    subject: Optional[str] = None
    priority: Optional[int] = 0
    last_message_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    assigned_user_id: Optional[int] = None
    metadata_: Optional[Dict[str, Any]] = None
    messages: Optional[List[MessageRead]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    class Config:
        from_attributes = True
