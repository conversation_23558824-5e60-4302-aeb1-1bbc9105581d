<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Support for Perf Maps" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/perfmaps.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="On supported platforms (as of this writing, only Linux), the runtime can take advantage of perf map files to make Python functions visible to an external profiling tool (such as perf). A running pr..." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="On supported platforms (as of this writing, only Linux), the runtime can take advantage of perf map files to make Python functions visible to an external profiling tool (such as perf). A running pr..." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Support for Perf Maps &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Abstract Objects Layer" href="abstract.html" />
    <link rel="prev" title="PyTime C API" href="time.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/perfmaps.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="time.html"
                          title="previous chapter">PyTime C API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="abstract.html"
                          title="next chapter">Abstract Objects Layer</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/perfmaps.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="abstract.html" title="Abstract Objects Layer"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="time.html" title="PyTime C API"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="utilities.html" accesskey="U">Utilities</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Support for Perf Maps</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="support-for-perf-maps">
<span id="perfmaps"></span><h1>Support for Perf Maps<a class="headerlink" href="#support-for-perf-maps" title="Link to this heading">¶</a></h1>
<p>On supported platforms (as of this writing, only Linux), the runtime can take
advantage of <em>perf map files</em> to make Python functions visible to an external
profiling tool (such as <a class="reference external" href="https://perf.wiki.kernel.org/index.php/Main_Page">perf</a>).
A running process may create a file in the <code class="docutils literal notranslate"><span class="pre">/tmp</span></code> directory, which contains entries
that can map a section of executable code to a name. This interface is described in the
<a class="reference external" href="https://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux.git/tree/tools/perf/Documentation/jit-interface.txt">documentation of the Linux Perf tool</a>.</p>
<p>In Python, these helper APIs can be used by libraries and features that rely
on generating machine code on the fly.</p>
<p>Note that holding the Global Interpreter Lock (GIL) is not required for these APIs.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyUnstable_PerfMapState_Init">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyUnstable_PerfMapState_Init</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyUnstable_PerfMapState_Init" title="Link to this definition">¶</a><br /></dt>
<dd><div class="unstable-c-api warning admonition">
<em>This is <a class="reference internal" href="stable.html#unstable-c-api"><span class="std std-ref">Unstable API</span></a>. It may change without warning in minor releases.</em></div>
<p>Open the <code class="docutils literal notranslate"><span class="pre">/tmp/perf-$pid.map</span></code> file, unless it’s already opened, and create
a lock to ensure thread-safe writes to the file (provided the writes are
done through <a class="reference internal" href="#c.PyUnstable_WritePerfMapEntry" title="PyUnstable_WritePerfMapEntry"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnstable_WritePerfMapEntry()</span></code></a>). Normally, there’s no need
to call this explicitly; just use <a class="reference internal" href="#c.PyUnstable_WritePerfMapEntry" title="PyUnstable_WritePerfMapEntry"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnstable_WritePerfMapEntry()</span></code></a>
and it will initialize the state on first call.</p>
<p>Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, <code class="docutils literal notranslate"><span class="pre">-1</span></code> on failure to create/open the perf map file,
or <code class="docutils literal notranslate"><span class="pre">-2</span></code> on failure to create a lock. Check <code class="docutils literal notranslate"><span class="pre">errno</span></code> for more information
about the cause of a failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyUnstable_WritePerfMapEntry">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyUnstable_WritePerfMapEntry</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">code_addr</span></span>, <span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">code_size</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">entry_name</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyUnstable_WritePerfMapEntry" title="Link to this definition">¶</a><br /></dt>
<dd><div class="unstable-c-api warning admonition">
<em>This is <a class="reference internal" href="stable.html#unstable-c-api"><span class="std std-ref">Unstable API</span></a>. It may change without warning in minor releases.</em></div>
<p>Write one single entry to the <code class="docutils literal notranslate"><span class="pre">/tmp/perf-$pid.map</span></code> file. This function is
thread safe. Here is what an example entry looks like:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="cp"># address      size  name</span>
<span class="mf">7f3529f</span><span class="n">cf759</span><span class="w"> </span><span class="n">b</span><span class="w">     </span><span class="n">py</span><span class="o">::</span><span class="n">bar</span><span class="o">:/</span><span class="n">run</span><span class="o">/</span><span class="n">t</span><span class="p">.</span><span class="n">py</span>
</pre></div>
</div>
<p>Will call <a class="reference internal" href="#c.PyUnstable_PerfMapState_Init" title="PyUnstable_PerfMapState_Init"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnstable_PerfMapState_Init()</span></code></a> before writing the entry, if
the perf map file is not already opened. Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, or the
same error codes as <a class="reference internal" href="#c.PyUnstable_PerfMapState_Init" title="PyUnstable_PerfMapState_Init"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnstable_PerfMapState_Init()</span></code></a> on failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyUnstable_PerfMapState_Fini">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyUnstable_PerfMapState_Fini</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyUnstable_PerfMapState_Fini" title="Link to this definition">¶</a><br /></dt>
<dd><div class="unstable-c-api warning admonition">
<em>This is <a class="reference internal" href="stable.html#unstable-c-api"><span class="std std-ref">Unstable API</span></a>. It may change without warning in minor releases.</em></div>
<p>Close the perf map file opened by <a class="reference internal" href="#c.PyUnstable_PerfMapState_Init" title="PyUnstable_PerfMapState_Init"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnstable_PerfMapState_Init()</span></code></a>.
This is called by the runtime itself during interpreter shut-down. In
general, there shouldn’t be a reason to explicitly call this, except to
handle specific scenarios such as forking.</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="time.html"
                          title="previous chapter">PyTime C API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="abstract.html"
                          title="next chapter">Abstract Objects Layer</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/perfmaps.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="abstract.html" title="Abstract Objects Layer"
             >next</a> |</li>
        <li class="right" >
          <a href="time.html" title="PyTime C API"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="utilities.html" >Utilities</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Support for Perf Maps</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>