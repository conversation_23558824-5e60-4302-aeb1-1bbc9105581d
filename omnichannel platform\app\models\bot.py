"""Bot testing related models."""

import reflex as rx
from sqlmodel import Field, SQLModel, Relationship
from sqlalchemy import Column, String, Text, Integer, Boolean, ForeignKey, DateTime, JSON, Float
from typing import Optional, Dict, Any, List, TYPE_CHECKING
from datetime import datetime

if TYPE_CHECKING:
    from .user import User


class BotConfiguration(SQLModel, table=True):
    """Bot test configuration model."""
    __tablename__ = "bot_configuration"
    
    # Primary key
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Relationships
    user_id: int = Field(sa_column=Column(Integer, ForeignKey("users.id"), nullable=False))
    
    # Bot details
    bot_name: str = Field(sa_column=Column(String(255), nullable=False))
    bot_type: str = Field(sa_column=Column(String(100), nullable=False))
    description: Optional[str] = Field(default=None, sa_column=Column(Text, nullable=True))
    
    # Configuration data
    test_scenarios: Dict[str, Any] = Field(sa_column=Column(JSON, nullable=False))
    expected_responses: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    test_settings: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Status
    is_active: bool = Field(default=True, sa_column=Column(Boolean, default=True, nullable=False))
    
    # Timestamps
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), onupdate=datetime.utcnow)
    )


class BotTest(SQLModel, table=True):
    """Bot test execution model."""
    __tablename__ = "bot_test"
    
    # Primary key
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Relationships
    configuration_id: int = Field(sa_column=Column(Integer, ForeignKey("bot_configuration.id"), nullable=False))
    user_id: int = Field(sa_column=Column(Integer, ForeignKey("users.id"), nullable=False))
    
    # Test details
    test_name: str = Field(sa_column=Column(String(255), nullable=False))
    target_phone: str = Field(sa_column=Column(String(20), nullable=False))
    
    # Timestamps
    started_at: datetime = Field(sa_column=Column(DateTime(timezone=True), nullable=False))
    completed_at: Optional[datetime] = Field(default=None, sa_column=Column(DateTime(timezone=True), nullable=True))
    
    # Status
    status: str = Field(default="RUNNING", sa_column=Column(String(50), default="RUNNING", nullable=False))
    
    # Statistics
    total_scenarios: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    passed_scenarios: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    failed_scenarios: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    total_messages_sent: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    average_response_time: Optional[float] = Field(default=None, sa_column=Column(Float, nullable=True))
    
    # Error handling
    error_message: Optional[str] = Field(default=None, sa_column=Column(Text, nullable=True))
    
    # Relationships
    results: List["BotTestResult"] = Relationship(back_populates="test")


class BotTestResult(SQLModel, table=True):
    """Individual bot test scenario result."""
    __tablename__ = "bot_test_result"
    
    # Primary key
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Relationships
    test_id: int = Field(sa_column=Column(Integer, ForeignKey("bot_test.id"), nullable=False))
    test: Optional[BotTest] = Relationship(back_populates="results")
    
    # Test details
    scenario_name: str = Field(sa_column=Column(String(255), nullable=False))
    scenario_order: int = Field(sa_column=Column(Integer, nullable=False))
    
    # Timestamps
    started_at: datetime = Field(sa_column=Column(DateTime(timezone=True), nullable=False))
    completed_at: Optional[datetime] = Field(default=None, sa_column=Column(DateTime(timezone=True), nullable=True))
    
    # Test data
    test_message: str = Field(sa_column=Column(Text, nullable=False))
    expected_response: Optional[str] = Field(default=None, sa_column=Column(Text, nullable=True))
    actual_response: Optional[str] = Field(default=None, sa_column=Column(Text, nullable=True))
    status: str = Field(sa_column=Column(String(50), nullable=False))
    response_time: Optional[float] = Field(default=None, sa_column=Column(Float, nullable=True))
    
    # Additional data
    test_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    error_details: Optional[str] = Field(default=None, sa_column=Column(Text, nullable=True))
