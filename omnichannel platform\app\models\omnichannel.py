"""Omnichannel customer service models.

This module contains the core models for the omnichannel customer service platform,
including channels, customers, conversations, and messages across multiple channels.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union, TYPE_CHECKING
from enum import Enum
from sqlalchemy import Column, String, ForeignKey, Text, DateTime, Boolean, Integer, Index, text
from sqlalchemy.dialects.sqlite import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship
from sqlmodel import SQLModel, Field, Relationship
from .base import BaseModel
if TYPE_CHECKING:
    from .company import Company
    from .whatsapp import WhatsAppFlow, Campaign
    from .analytics import MessageAnalytics, Campaign

class ChannelType(str, Enum):
    """Supported communication channel types."""
    WHATSAPP = "whatsapp"
    EMAIL = "email"
    WEB_CHAT = "web_chat"
    MESSENGER = "messenger"
    INSTAGRAM = "instagram"
    TWITTER = "twitter"
    SMS = "sms"
    VOICE = "voice"
    TELEGRAM = "telegram"


class ChannelStatus(str, Enum):
    """Status of a communication channel."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"
    SUSPENDED = "suspended"





class ChannelAccount(BaseModel, table=True):
    """Accounts for different messaging channels (multi-tenant)."""
    __tablename__ = "channel_accounts"

    # Multi-tenancy
    company_id: int = Field(sa_column=Column(Integer, ForeignKey("companies.id", ondelete="CASCADE"), nullable=False, index=True))
    company: Optional["Company"] = Relationship(back_populates="channel_accounts")

    # Account identification
    name: str = Field(sa_column=Column(String(100), nullable=False))
    channel_type: ChannelType = Field(sa_column=Column(String(20), nullable=False, index=True))
    
    # Account configuration
    config: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON, nullable=False, default=dict),
        description="Channel-specific account configuration"
    )
    
    # Status and metadata
    
    metadata_: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column("metadata", JSON, nullable=False, default=dict)
    )
    
    # Relationships
    channels: List["Channel"] = Relationship(back_populates="account")


class Channel(BaseModel, table=True):
    """Base model for communication channels (multi-tenant)."""
    __tablename__ = "channels"

    # Multi-tenancy
    company_id: int = Field(sa_column=Column(Integer, ForeignKey("companies.id", ondelete="CASCADE"), nullable=False, index=True))
    company: Optional["Company"] = Relationship(back_populates="channels")

    # Channel identification
    name: str = Field(sa_column=Column(String(100), nullable=False, index=True))
    channel_type: ChannelType = Field(sa_column=Column(String(20), nullable=False, index=True))
    description: Optional[str] = Field(sa_column=Column(Text, nullable=True))
    
    # Channel configuration
    config: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON, nullable=False, default=dict),
        description="Channel-specific configuration"
    )
    
    # Status and metadata
    status: ChannelStatus = Field(
        default=ChannelStatus.ACTIVE,
        sa_column=Column(String(20), nullable=False, index=True)
    )
    
    # Metadata for additional channel data
    metadata_: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column("metadata", JSON, nullable=False, default=dict, server_default=text("'{}'"))
    )
    
    # Account relationship
    account_id: int = Field(
        sa_column=Column(
            Integer, 
            ForeignKey("channel_accounts.id", ondelete="CASCADE"),
            nullable=True,
            index=True
        )
    )
    account: "ChannelAccount" = Relationship(back_populates="channels")
    
    # Relationships
    conversations: List["Conversation"] = Relationship(back_populates="channel")
    messages: List["OmnichannelMessage"] = Relationship(back_populates="channel")


class Customer(BaseModel, table=True):
    """Unified customer model with cross-channel identity resolution (multi-tenant)."""
    __tablename__ = "customers"

    # Multi-tenancy
    company_id: int = Field(sa_column=Column(Integer, ForeignKey("companies.id", ondelete="CASCADE"), nullable=False, index=True))
    company: Optional["Company"] = Relationship(back_populates="customers")

    # Core customer info
    first_name: Optional[str] = Field(sa_column=Column(String(100), nullable=True))
    last_name: Optional[str] = Field(sa_column=Column(String(100), nullable=True))
    email: Optional[str] = Field(sa_column=Column(String(255), nullable=True, index=True))
    phone: Optional[str] = Field(sa_column=Column(String(50), nullable=True, index=True))
    
    # External identifiers (for identity resolution)
    external_ids: Dict[str, str] = Field(
        default_factory=dict,
        sa_column=Column(JSON, nullable=False, default=dict),
        description="External system IDs (e.g., { 'shopify': '12345' })"
    )
    
    # Customer metadata
    metadata_: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column("metadata", JSON, nullable=False, default=dict),
        description="Additional customer metadata"
    )
    
    # Relationships
    conversations: List["Conversation"] = Relationship(back_populates="customer")
    messages: List["OmnichannelMessage"] = Relationship(back_populates="customer")
    
    # Indexes for faster lookups
    __table_args__ = (
        Index('ix_customers_email_phone', 'email', 'phone'),
    )





class MessageDirection(str, Enum):
    """Direction of a message."""
    INBOUND = "inbound"
    OUTBOUND = "outbound"
    SYSTEM = "system"


class MessageStatus(str, Enum):
    """Status of a message."""
    DRAFT = "draft"
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"
    CANCELED = "canceled"


class MessageType(str, Enum):
    """Type of message content."""
    TEXT = "text"
    TEMPLATE = "template"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"
    LOCATION = "location"
    CONTACTS = "contacts"
    INTERACTIVE = "interactive"
    BUTTON = "button"
    LIST = "list"
    FLOW = "flow"


class OmnichannelMessage(BaseModel, table=True):
    __tablename__ = "omnichannel_messages"
    """A message in a conversation, which can be from any channel (multi-tenant)."""

    # Multi-tenancy
    company_id: int = Field(sa_column=Column(Integer, ForeignKey("companies.id", ondelete="CASCADE"), nullable=False, index=True))
    company: Optional["Company"] = Relationship(back_populates="messages")

    conversation_id: int = Field(
            sa_column=Column(
                Integer, 
                ForeignKey("conversations.id", ondelete="CASCADE"),
                nullable=False,
                index=True
            )
        )
    channel_id: int = Field(
            sa_column=Column(
                Integer, 
                ForeignKey("channels.id", ondelete="CASCADE"),
                nullable=False,
                index=True
            )
        )
    customer_id: int = Field(
            sa_column=Column(
                Integer, 
                ForeignKey("customers.id", ondelete="CASCADE"),
                nullable=False,
                index=True
            )
        )
    flow_id: Optional[int] = Field(
            sa_column=Column(
                Integer, 
                ForeignKey("whatsappflow.id", ondelete="SET NULL"),
                nullable=True,
                index=True
            )
        )
    campaign_id: Optional[int] = Field(
            sa_column=Column(
                Integer, 
                ForeignKey("campaigns.id", ondelete="SET NULL"),
                nullable=True,
                index=True
            )
        )
    



    # Message content and type
    message_type: Optional[MessageType] = Field(
            default=None,
            sa_column=Column(String(20), nullable=True, index=True),
            description="Type of the message content"
        )
    content: str = Field(sa_column=Column(Text, nullable=False))
    content_json: Optional[Dict[str, Any]] = Field(
            default_factory=dict,
            sa_column=Column(JSON, nullable=False, default=dict),
            description="Structured message content"
        )
    
    # Message status and direction
    direction: MessageDirection = Field(
            sa_column=Column(String(20), nullable=False, index=True)
        )
    status: MessageStatus = Field(
            default=MessageStatus.PENDING,
            sa_column=Column(String(20), nullable=False, index=True)
        )
    status_updates: List[Dict[str, Any]] = Field(
            default_factory=list,
            sa_column=Column(JSON, nullable=False, default=list),
            description="History of status updates"
        )
    
    # External references
    external_id: Optional[str] = Field(
            sa_column=Column(String(255), nullable=True, index=True),
            description="External message ID from the channel"
        )
    external_conversation_id: Optional[str] = Field(
            default=None,
            sa_column=Column(String(255), nullable=True, index=True),
            description="External conversation ID from the channel"
        )
    
    # Error information
    error_code: Optional[str] = Field(
            default=None,
            sa_column=Column(String(100), nullable=True),
            description="Error code if message failed"
        )
    error_message: Optional[str] = Field(
            default=None,
            sa_column=Column(Text, nullable=True),
            description="Error message if message failed"
        )
    
    # Timestamps
    sent_at: Optional[datetime] = Field(
            default=None,
            sa_column=Column(DateTime(timezone=True), nullable=True, index=True)
        )
    delivered_at: Optional[datetime] = Field(
            default=None,
            sa_column=Column(DateTime(timezone=True), nullable=True)
        )
    read_at: Optional[datetime] = Field(
            default=None,
            sa_column=Column(DateTime(timezone=True), nullable=True)
        )
    
    # Relationships
    conversation: "Conversation" = Relationship(back_populates="omnichannel_messages")
    channel: "Channel" = Relationship(back_populates="messages")
    customer: "Customer" = Relationship(back_populates="messages")
    flow: Optional["WhatsAppFlow"] = Relationship(back_populates="messages")
    campaign: Optional["Campaign"] = Relationship(back_populates="messages")
    analytics: List["MessageAnalytics"] = Relationship(back_populates="message", sa_relationship_kwargs={"foreign_keys": "[MessageAnalytics.message_id]"})

    
    __table_args__ = (
        Index('ix_messages_conversation_created', 'conversation_id', 'created_at'),
        Index('ix_messages_customer_created', 'customer_id', 'created_at'),
        Index('ix_messages_external_id', 'channel_id', 'external_id', unique=True)
    )
