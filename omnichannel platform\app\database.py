"""Database configuration and session management."""

from sqlalchemy import create_engine
from sqlmodel import SQLModel, Session, create_engine as create_sqlmodel_engine
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from typing import Generator, Optional
import os

from rxconfig import DATABASE_CONFIG

# Get database URL from environment or config
DATABASE_URL = os.getenv("DATABASE_URL", DATABASE_CONFIG["development"]["url"])

# Create database engine
engine = create_sqlmodel_engine(
    DATABASE_URL,
    echo=DATABASE_CONFIG["development"]["echo"],
    pool_pre_ping=True,
    pool_recycle=300,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine, class_=Session)


def get_session() -> Session:
    """Get database session with automatic cleanup."""
    return SessionLocal()


@contextmanager
def get_session_ctx() -> Generator[Session, None, None]:
    """Context manager for database sessions with automatic cleanup."""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def init_database():
    """Initialize database tables."""
    # Import all models to ensure they are registered with SQLModel
    from .models import user, whatsapp, bot, analytics, flow_models, conversation  # noqa: F401
    
    # Create all tables
    SQLModel.metadata.create_all(engine)
    print("Database tables created successfully!")


def create_sample_data():
    """Create sample data for development."""
    from .models.user import User, UserRole, UserStatus
    from .models.company import Company
    from .models.whatsapp import WhatsAppAccount, WhatsAppAccountStatus
    from .utils.security import hash_password
    from datetime import datetime, timedelta
    
    with get_session_ctx() as session:
        # Check if admin user already exists
        # Check if default company exists, create if not
        default_company = session.query(Company).filter(Company.name == "Default Company").first()
        if not default_company:
            default_company = Company(
                name="Default Company",
                settings={},
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(default_company)
            session.commit()
            session.refresh(default_company)

        admin_user = session.query(User).filter(User.email == "<EMAIL>").first()
        
        if not admin_user:
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=hash_password("admin123"),
                first_name="Admin",
                last_name="User",
                role=UserRole.SUPER_ADMIN.value,
                status=UserStatus.ACTIVE.value,
                is_email_verified=True,
                company_id=default_company.id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(admin_user)
            
            # Create demo user
            demo_user = User(
                email="<EMAIL>",
                username="demo",
                hashed_password=hash_password("demo123"),
                first_name="Demo",
                last_name="User",
                role=UserRole.USER.value,
                status=UserStatus.ACTIVE.value,
                is_email_verified=True,
                company_id=default_company.id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(demo_user)
            
            # Commit to get user IDs
            session.commit()
            session.refresh(admin_user)
            session.refresh(demo_user)
            
            # Create a sample WhatsApp account
            whatsapp_account = WhatsAppAccount(
                user_id=admin_user.id,
                account_name="Test Business Account",
                business_account_id="**********",
                phone_number_id="**********",
                phone_number="+**********",
                access_token="test_access_token",
                is_verified=True,
                status=WhatsAppAccountStatus.ACTIVE.value,
                verified_at=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(whatsapp_account)
            session.commit()
            
            print("Sample data created successfully!")
            print("Admin: <EMAIL> / admin123")
            print("Demo: <EMAIL> / demo123")


if __name__ == "__main__":
    # Initialize database when run directly
    init_database()
    create_sample_data()
