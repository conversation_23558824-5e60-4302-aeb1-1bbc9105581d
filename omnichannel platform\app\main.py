"""Main application entry point for WhatsApp Business Platform."""

from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles

# Import API router
from .api.main import app as fastapi_app

# Create the main FastAPI app
app = FastAPI()

# Mount the API router
app.mount("/api", fastapi_app)

# Mount the static files directory
app.mount("/", StaticFiles(directory="static"), name="static")

# Health check endpoint
@app.get("/health")
def health_check():
    return {"status": "ok"}