<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Type Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/type.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Creating Heap-Allocated Types: The following functions and structs are used to create heap types." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Creating Heap-Allocated Types: The following functions and structs are used to create heap types." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Type Objects &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="The None Object" href="none.html" />
    <link rel="prev" title="Concrete Objects Layer" href="concrete.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/type.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Type Objects</a><ul>
<li><a class="reference internal" href="#creating-heap-allocated-types">Creating Heap-Allocated Types</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="concrete.html"
                          title="previous chapter">Concrete Objects Layer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="none.html"
                          title="next chapter">The <code class="docutils literal notranslate"><span class="pre">None</span></code> Object</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/type.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="none.html" title="The None Object"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="concrete.html" title="Concrete Objects Layer"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" accesskey="U">Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Type Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="type-objects">
<span id="typeobjects"></span><h1>Type Objects<a class="headerlink" href="#type-objects" title="Link to this heading">¶</a></h1>
<dl class="c type" id="index-0">
<dt class="sig sig-object c" id="c.PyTypeObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTypeObject</span></span></span><a class="headerlink" href="#c.PyTypeObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Limited API</span></a> (as an opaque struct).</em><p>The C structure of the objects used to describe built-in types.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyType_Type">
<a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_Type</span></span></span><a class="headerlink" href="#c.PyType_Type" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This is the type object for type objects; it is the same object as
<a class="reference internal" href="../library/functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> in the Python layer.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return non-zero if the object <em>o</em> is a type object, including instances of
types derived from the standard type object.  Return 0 in all other cases.
This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_CheckExact">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_CheckExact</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_CheckExact" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return non-zero if the object <em>o</em> is a type object, but not a subtype of
the standard type object.  Return 0 in all other cases.  This function
always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_ClearCache">
<span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_ClearCache</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_ClearCache" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Clear the internal lookup cache. Return the current version tag.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetFlags">
<span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">long</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetFlags</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetFlags" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a> member of <em>type</em>. This function is primarily
meant for use with <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code>; the individual flag bits are
guaranteed to be stable across Python releases, but access to
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_flags</span></code></a> itself is not part of the <a class="reference internal" href="stable.html#limited-c-api"><span class="std std-ref">limited API</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The return type is now <code class="docutils literal notranslate"><span class="pre">unsigned</span> <span class="pre">long</span></code> rather than <code class="docutils literal notranslate"><span class="pre">long</span></code>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetDict">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetDict</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetDict" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the type object’s internal namespace, which is otherwise only
exposed via a read-only proxy (<a class="reference internal" href="../reference/datamodel.html#type.__dict__" title="type.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">cls.__dict__</span></code></a>).
This is a
replacement for accessing <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_dict" title="PyTypeObject.tp_dict"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dict</span></code></a> directly.
The returned dictionary must be treated as read-only.</p>
<p>This function is meant for specific embedding and language-binding cases,
where direct access to the dict is necessary and indirect access
(e.g. via the proxy or <a class="reference internal" href="object.html#c.PyObject_GetAttr" title="PyObject_GetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetAttr()</span></code></a>) isn’t adequate.</p>
<p>Extension modules should continue to use <code class="docutils literal notranslate"><span class="pre">tp_dict</span></code>,
directly or indirectly, when setting up their own types.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_Modified">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_Modified</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_Modified" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Invalidate the internal lookup cache for the type and all of its
subtypes.  This function must be called after any manual
modification of the attributes or base classes of the type.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_AddWatcher">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_AddWatcher</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyType_WatchCallback" title="PyType_WatchCallback"><span class="n"><span class="pre">PyType_WatchCallback</span></span></a><span class="w"> </span><span class="n"><span class="pre">callback</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_AddWatcher" title="Link to this definition">¶</a><br /></dt>
<dd><p>Register <em>callback</em> as a type watcher. Return a non-negative integer ID
which must be passed to future calls to <a class="reference internal" href="#c.PyType_Watch" title="PyType_Watch"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Watch()</span></code></a>. In case of
error (e.g. no more watcher IDs available), return <code class="docutils literal notranslate"><span class="pre">-1</span></code> and set an
exception.</p>
<p>In free-threaded builds, <a class="reference internal" href="#c.PyType_AddWatcher" title="PyType_AddWatcher"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_AddWatcher()</span></code></a> is not thread-safe,
so it must be called at start up (before spawning the first thread).</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_ClearWatcher">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_ClearWatcher</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">watcher_id</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_ClearWatcher" title="Link to this definition">¶</a><br /></dt>
<dd><p>Clear watcher identified by <em>watcher_id</em> (previously returned from
<a class="reference internal" href="#c.PyType_AddWatcher" title="PyType_AddWatcher"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_AddWatcher()</span></code></a>). Return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error (e.g.
if <em>watcher_id</em> was never registered.)</p>
<p>An extension should never call <code class="docutils literal notranslate"><span class="pre">PyType_ClearWatcher</span></code> with a <em>watcher_id</em>
that was not returned to it by a previous call to
<a class="reference internal" href="#c.PyType_AddWatcher" title="PyType_AddWatcher"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_AddWatcher()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_Watch">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_Watch</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">watcher_id</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_Watch" title="Link to this definition">¶</a><br /></dt>
<dd><p>Mark <em>type</em> as watched. The callback granted <em>watcher_id</em> by
<a class="reference internal" href="#c.PyType_AddWatcher" title="PyType_AddWatcher"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_AddWatcher()</span></code></a> will be called whenever
<a class="reference internal" href="#c.PyType_Modified" title="PyType_Modified"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Modified()</span></code></a> reports a change to <em>type</em>. (The callback may be
called only once for a series of consecutive modifications to <em>type</em>, if
<code class="xref c c-func docutils literal notranslate"><span class="pre">_PyType_Lookup()</span></code> is not called on <em>type</em> between the modifications;
this is an implementation detail and subject to change.)</p>
<p>An extension should never call <code class="docutils literal notranslate"><span class="pre">PyType_Watch</span></code> with a <em>watcher_id</em> that was
not returned to it by a previous call to <a class="reference internal" href="#c.PyType_AddWatcher" title="PyType_AddWatcher"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_AddWatcher()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyType_WatchCallback">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_WatchCallback</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.PyType_WatchCallback" title="Link to this definition">¶</a><br /></dt>
<dd><p>Type of a type-watcher callback function.</p>
<p>The callback must not modify <em>type</em> or cause <a class="reference internal" href="#c.PyType_Modified" title="PyType_Modified"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Modified()</span></code></a> to be
called on <em>type</em> or any type in its MRO; violating this rule could cause
infinite recursion.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_HasFeature">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_HasFeature</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">feature</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_HasFeature" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return non-zero if the type object <em>o</em> sets the feature <em>feature</em>.
Type features are denoted by single bit flags.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_IS_GC">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_IS_GC</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_IS_GC" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if the type object includes support for the cycle detector; this
tests the type flag <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_IsSubtype">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_IsSubtype</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">a</span></span>, <a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">b</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_IsSubtype" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return true if <em>a</em> is a subtype of <em>b</em>.</p>
<p>This function only checks for actual subtypes, which means that
<a class="reference internal" href="../reference/datamodel.html#type.__subclasscheck__" title="type.__subclasscheck__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__subclasscheck__()</span></code></a> is not called on <em>b</em>.  Call
<a class="reference internal" href="object.html#c.PyObject_IsSubclass" title="PyObject_IsSubclass"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_IsSubclass()</span></code></a> to do the same check that <a class="reference internal" href="../library/functions.html#issubclass" title="issubclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">issubclass()</span></code></a>
would do.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GenericAlloc">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GenericAlloc</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">nitems</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GenericAlloc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Generic handler for the <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_alloc" title="PyTypeObject.tp_alloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_alloc</span></code></a> slot of a type object.  Use
Python’s default memory allocation mechanism to allocate a new instance and
initialize all its contents to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GenericNew">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GenericNew</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">kwds</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GenericNew" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Generic handler for the <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> slot of a type object.  Create a
new instance using the type’s <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_alloc" title="PyTypeObject.tp_alloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_alloc</span></code></a> slot.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_Ready">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_Ready</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_Ready" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Finalize a type object.  This should be called on all type objects to finish
their initialization.  This function is responsible for adding inherited slots
from a type’s base class.  Return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, or return <code class="docutils literal notranslate"><span class="pre">-1</span></code> and sets an
exception on error.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If some of the base classes implements the GC protocol and the provided
type does not include the <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> in its flags, then
the GC protocol will be automatically implemented from its parents. On
the contrary, if the type being created does include
<a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_HAVE_GC" title="Py_TPFLAGS_HAVE_GC"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_GC</span></code></a> in its flags then it <strong>must</strong> implement the
GC protocol itself by at least implementing the
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_traverse" title="PyTypeObject.tp_traverse"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_traverse</span></code></a> handle.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetName">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetName</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetName" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Return the type’s name. Equivalent to getting the type’s
<a class="reference internal" href="../reference/datamodel.html#type.__name__" title="type.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> attribute.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetQualName">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetQualName</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetQualName" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Return the type’s qualified name. Equivalent to getting the
type’s <a class="reference internal" href="../reference/datamodel.html#type.__qualname__" title="type.__qualname__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__qualname__</span></code></a> attribute.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetFullyQualifiedName">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetFullyQualifiedName</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetFullyQualifiedName" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.13.</em><p>Return the type’s fully qualified name. Equivalent to
<code class="docutils literal notranslate"><span class="pre">f&quot;{type.__module__}.{type.__qualname__}&quot;</span></code>, or <a class="reference internal" href="../reference/datamodel.html#type.__qualname__" title="type.__qualname__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type.__qualname__</span></code></a>
if <a class="reference internal" href="../reference/datamodel.html#type.__module__" title="type.__module__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type.__module__</span></code></a> is not a string or is equal to <code class="docutils literal notranslate"><span class="pre">&quot;builtins&quot;</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetModuleName">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetModuleName</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetModuleName" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.13.</em><p>Return the type’s module name. Equivalent to getting the
<a class="reference internal" href="../reference/datamodel.html#type.__module__" title="type.__module__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type.__module__</span></code></a> attribute.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetSlot">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetSlot</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">slot</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetSlot" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.4.</em><p>Return the function pointer stored in the given slot. If the
result is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, this indicates that either the slot is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>,
or that the function was called with invalid parameters.
Callers will typically cast the result pointer into the appropriate
function type.</p>
<p>See <a class="reference internal" href="#c.PyType_Slot.slot" title="PyType_Slot.slot"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyType_Slot.slot</span></code></a> for possible values of the <em>slot</em> argument.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><a class="reference internal" href="#c.PyType_GetSlot" title="PyType_GetSlot"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetSlot()</span></code></a> can now accept all types.
Previously, it was limited to <a class="reference internal" href="typeobj.html#heap-types"><span class="std std-ref">heap types</span></a>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetModule">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetModule</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetModule" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Return the module object associated with the given type when the type was
created using <a class="reference internal" href="#c.PyType_FromModuleAndSpec" title="PyType_FromModuleAndSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromModuleAndSpec()</span></code></a>.</p>
<p>If no module is associated with the given type, sets <a class="reference internal" href="../library/exceptions.html#TypeError" title="TypeError"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeError</span></code></a>
and returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>This function is usually used to get the module in which a method is defined.
Note that in such a method, <code class="docutils literal notranslate"><span class="pre">PyType_GetModule(Py_TYPE(self))</span></code>
may not return the intended result.
<code class="docutils literal notranslate"><span class="pre">Py_TYPE(self)</span></code> may be a <em>subclass</em> of the intended class, and subclasses
are not necessarily defined in the same module as their superclass.
See <a class="reference internal" href="structures.html#c.PyCMethod" title="PyCMethod"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCMethod</span></code></a> to get the class that defines the method.
See <a class="reference internal" href="#c.PyType_GetModuleByDef" title="PyType_GetModuleByDef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetModuleByDef()</span></code></a> for cases when <code class="xref c c-type docutils literal notranslate"><span class="pre">PyCMethod</span></code> cannot
be used.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetModuleState">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetModuleState</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetModuleState" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Return the state of the module object associated with the given type.
This is a shortcut for calling <a class="reference internal" href="module.html#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a> on the result
of <a class="reference internal" href="#c.PyType_GetModule" title="PyType_GetModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetModule()</span></code></a>.</p>
<p>If no module is associated with the given type, sets <a class="reference internal" href="../library/exceptions.html#TypeError" title="TypeError"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeError</span></code></a>
and returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>If the <em>type</em> has an associated module but its state is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>,
returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> without setting an exception.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_GetModuleByDef">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_GetModuleByDef</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span>, <span class="k"><span class="pre">struct</span></span><span class="w"> </span><a class="reference internal" href="module.html#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_GetModuleByDef" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.13.</em><p>Find the first superclass whose module was created from
the given <a class="reference internal" href="module.html#c.PyModuleDef" title="PyModuleDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyModuleDef</span></code></a> <em>def</em>, and return that module.</p>
<p>If no module is found, raises a <a class="reference internal" href="../library/exceptions.html#TypeError" title="TypeError"><code class="xref py py-class docutils literal notranslate"><span class="pre">TypeError</span></code></a> and returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>This function is intended to be used together with
<a class="reference internal" href="module.html#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a> to get module state from slot methods (such as
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a> or <a class="reference internal" href="typeobj.html#c.PyNumberMethods.nb_add" title="PyNumberMethods.nb_add"><code class="xref c c-member docutils literal notranslate"><span class="pre">nb_add</span></code></a>)
and other places where a method’s defining class cannot be passed using the
<a class="reference internal" href="structures.html#c.PyCMethod" title="PyCMethod"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCMethod</span></code></a> calling convention.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyUnstable_Type_AssignVersionTag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyUnstable_Type_AssignVersionTag</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyUnstable_Type_AssignVersionTag" title="Link to this definition">¶</a><br /></dt>
<dd><div class="unstable-c-api warning admonition">
<em>This is <a class="reference internal" href="stable.html#unstable-c-api"><span class="std std-ref">Unstable API</span></a>. It may change without warning in minor releases.</em></div>
<p>Attempt to assign a version tag to the given type.</p>
<p>Returns 1 if the type already had a valid version tag or a new one was
assigned, or 0 if a new tag could not be assigned.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<section id="creating-heap-allocated-types">
<h2>Creating Heap-Allocated Types<a class="headerlink" href="#creating-heap-allocated-types" title="Link to this heading">¶</a></h2>
<p>The following functions and structs are used to create
<a class="reference internal" href="typeobj.html#heap-types"><span class="std std-ref">heap types</span></a>.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_FromMetaclass">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_FromMetaclass</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">metaclass</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <a class="reference internal" href="#c.PyType_Spec" title="PyType_Spec"><span class="n"><span class="pre">PyType_Spec</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">spec</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">bases</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_FromMetaclass" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em><p>Create and return a <a class="reference internal" href="typeobj.html#heap-types"><span class="std std-ref">heap type</span></a> from the <em>spec</em>
(see <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_HEAPTYPE" title="Py_TPFLAGS_HEAPTYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HEAPTYPE</span></code></a>).</p>
<p>The metaclass <em>metaclass</em> is used to construct the resulting type object.
When <em>metaclass</em> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the metaclass is derived from <em>bases</em>
(or <em>Py_tp_base[s]</em> slots if <em>bases</em> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, see below).</p>
<p>Metaclasses that override <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> are not
supported, except if <code class="docutils literal notranslate"><span class="pre">tp_new</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
(For backwards compatibility, other <code class="docutils literal notranslate"><span class="pre">PyType_From*</span></code> functions allow
such metaclasses. They ignore <code class="docutils literal notranslate"><span class="pre">tp_new</span></code>, which may result in incomplete
initialization. This is deprecated and in Python 3.14+ such metaclasses will
not be supported.)</p>
<p>The <em>bases</em> argument can be used to specify base classes; it can either
be only one class or a tuple of classes.
If <em>bases</em> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the <em>Py_tp_bases</em> slot is used instead.
If that also is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the <em>Py_tp_base</em> slot is used instead.
If that also is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the new type derives from <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>.</p>
<p>The <em>module</em> argument can be used to record the module in which the new
class is defined. It must be a module object or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
If not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the module is associated with the new type and can later be
retrieved with <a class="reference internal" href="#c.PyType_GetModule" title="PyType_GetModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetModule()</span></code></a>.
The associated module is not inherited by subclasses; it must be specified
for each class individually.</p>
<p>This function calls <a class="reference internal" href="#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a> on the new type.</p>
<p>Note that this function does <em>not</em> fully match the behavior of
calling <a class="reference internal" href="../library/functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type()</span></code></a> or using the <a class="reference internal" href="../reference/compound_stmts.html#class"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">class</span></code></a> statement.
With user-provided base types or metaclasses, prefer
<a class="reference internal" href="call.html#capi-call"><span class="std std-ref">calling</span></a> <a class="reference internal" href="../library/functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> (or the metaclass)
over <code class="docutils literal notranslate"><span class="pre">PyType_From*</span></code> functions.
Specifically:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../reference/datamodel.html#object.__new__" title="object.__new__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a> is not called on the new class
(and it must be set to <code class="docutils literal notranslate"><span class="pre">type.__new__</span></code>).</p></li>
<li><p><a class="reference internal" href="../reference/datamodel.html#object.__init__" title="object.__init__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a> is not called on the new class.</p></li>
<li><p><a class="reference internal" href="../reference/datamodel.html#object.__init_subclass__" title="object.__init_subclass__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__init_subclass__()</span></code></a> is not called on any bases.</p></li>
<li><p><a class="reference internal" href="../reference/datamodel.html#object.__set_name__" title="object.__set_name__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__set_name__()</span></code></a> is not called on new descriptors.</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_FromModuleAndSpec">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_FromModuleAndSpec</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <a class="reference internal" href="#c.PyType_Spec" title="PyType_Spec"><span class="n"><span class="pre">PyType_Spec</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">spec</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">bases</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_FromModuleAndSpec" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Equivalent to <code class="docutils literal notranslate"><span class="pre">PyType_FromMetaclass(NULL,</span> <span class="pre">module,</span> <span class="pre">spec,</span> <span class="pre">bases)</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The function now accepts a single class as the <em>bases</em> argument and
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> as the <code class="docutils literal notranslate"><span class="pre">tp_doc</span></code> slot.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The function now finds and uses a metaclass corresponding to the provided
base classes.  Previously, only <a class="reference internal" href="../library/functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> instances were returned.</p>
<p>The <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> of the metaclass is <em>ignored</em>.
which may result in incomplete initialization.
Creating classes whose metaclass overrides
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> is deprecated and in Python 3.14+ it
will be no longer allowed.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_FromSpecWithBases">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_FromSpecWithBases</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyType_Spec" title="PyType_Spec"><span class="n"><span class="pre">PyType_Spec</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">spec</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">bases</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_FromSpecWithBases" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.3.</em><p>Equivalent to <code class="docutils literal notranslate"><span class="pre">PyType_FromMetaclass(NULL,</span> <span class="pre">NULL,</span> <span class="pre">spec,</span> <span class="pre">bases)</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The function now finds and uses a metaclass corresponding to the provided
base classes.  Previously, only <a class="reference internal" href="../library/functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> instances were returned.</p>
<p>The <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> of the metaclass is <em>ignored</em>.
which may result in incomplete initialization.
Creating classes whose metaclass overrides
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> is deprecated and in Python 3.14+ it
will be no longer allowed.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyType_FromSpec">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyType_FromSpec</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyType_Spec" title="PyType_Spec"><span class="n"><span class="pre">PyType_Spec</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">spec</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyType_FromSpec" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Equivalent to <code class="docutils literal notranslate"><span class="pre">PyType_FromMetaclass(NULL,</span> <span class="pre">NULL,</span> <span class="pre">spec,</span> <span class="pre">NULL)</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The function now finds and uses a metaclass corresponding to the
base classes provided in <em>Py_tp_base[s]</em> slots.
Previously, only <a class="reference internal" href="../library/functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> instances were returned.</p>
<p>The <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> of the metaclass is <em>ignored</em>.
which may result in incomplete initialization.
Creating classes whose metaclass overrides
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> is deprecated and in Python 3.14+ it
will be no longer allowed.</p>
</div>
</dd></dl>

<!-- Keep old URL fragments working (see gh-97908) -->
<span id='c.PyType_Spec.PyType_Spec.name'></span>
<span id='c.PyType_Spec.PyType_Spec.basicsize'></span>
<span id='c.PyType_Spec.PyType_Spec.itemsize'></span>
<span id='c.PyType_Spec.PyType_Spec.flags'></span>
<span id='c.PyType_Spec.PyType_Spec.slots'></span><dl class="c type">
<dt class="sig sig-object c" id="c.PyType_Spec">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_Spec</span></span></span><a class="headerlink" href="#c.PyType_Spec" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> (including all members).</em><p>Structure defining a type’s behavior.</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyType_Spec.name">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">name</span></span></span><a class="headerlink" href="#c.PyType_Spec.name" title="Link to this definition">¶</a><br /></dt>
<dd><p>Name of the type, used to set <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_name" title="PyTypeObject.tp_name"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyTypeObject.tp_name</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyType_Spec.basicsize">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">basicsize</span></span></span><a class="headerlink" href="#c.PyType_Spec.basicsize" title="Link to this definition">¶</a><br /></dt>
<dd><p>If positive, specifies the size of the instance in bytes.
It is used to set <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_basicsize" title="PyTypeObject.tp_basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyTypeObject.tp_basicsize</span></code></a>.</p>
<p>If zero, specifies that <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_basicsize" title="PyTypeObject.tp_basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code></a>
should be inherited.</p>
<p>If negative, the absolute value specifies how much space instances of the
class need <em>in addition</em> to the superclass.
Use <a class="reference internal" href="object.html#c.PyObject_GetTypeData" title="PyObject_GetTypeData"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetTypeData()</span></code></a> to get a pointer to subclass-specific
memory reserved this way.
For negative <code class="xref c c-member docutils literal notranslate"><span class="pre">basicsize</span></code>, Python will insert padding when
needed to meet <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_basicsize" title="PyTypeObject.tp_basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_basicsize</span></code></a>’s alignment
requirements.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Previously, this field could not be negative.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyType_Spec.itemsize">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">itemsize</span></span></span><a class="headerlink" href="#c.PyType_Spec.itemsize" title="Link to this definition">¶</a><br /></dt>
<dd><p>Size of one element of a variable-size type, in bytes.
Used to set <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyTypeObject.tp_itemsize</span></code></a>.
See <code class="docutils literal notranslate"><span class="pre">tp_itemsize</span></code> documentation for caveats.</p>
<p>If zero, <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a> is inherited.
Extending arbitrary variable-sized classes is dangerous,
since some types use a fixed offset for variable-sized memory,
which can then overlap fixed-sized memory used by a subclass.
To help prevent mistakes, inheriting <code class="docutils literal notranslate"><span class="pre">itemsize</span></code> is only possible
in the following situations:</p>
<ul class="simple">
<li><p>The base is not variable-sized (its
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_itemsize" title="PyTypeObject.tp_itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_itemsize</span></code></a>).</p></li>
<li><p>The requested <a class="reference internal" href="#c.PyType_Spec.basicsize" title="PyType_Spec.basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyType_Spec.basicsize</span></code></a> is positive,
suggesting that the memory layout of the base class is known.</p></li>
<li><p>The requested <a class="reference internal" href="#c.PyType_Spec.basicsize" title="PyType_Spec.basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyType_Spec.basicsize</span></code></a> is zero,
suggesting that the subclass does not access the instance’s memory
directly.</p></li>
<li><p>With the <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_ITEMS_AT_END" title="Py_TPFLAGS_ITEMS_AT_END"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_ITEMS_AT_END</span></code></a> flag.</p></li>
</ul>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyType_Spec.flags">
<span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">flags</span></span></span><a class="headerlink" href="#c.PyType_Spec.flags" title="Link to this definition">¶</a><br /></dt>
<dd><p>Type flags, used to set <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_flags" title="PyTypeObject.tp_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyTypeObject.tp_flags</span></code></a>.</p>
<p>If the <code class="docutils literal notranslate"><span class="pre">Py_TPFLAGS_HEAPTYPE</span></code> flag is not set,
<a class="reference internal" href="#c.PyType_FromSpecWithBases" title="PyType_FromSpecWithBases"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpecWithBases()</span></code></a> sets it automatically.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyType_Spec.slots">
<a class="reference internal" href="#c.PyType_Slot" title="PyType_Slot"><span class="n"><span class="pre">PyType_Slot</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">slots</span></span></span><a class="headerlink" href="#c.PyType_Spec.slots" title="Link to this definition">¶</a><br /></dt>
<dd><p>Array of <a class="reference internal" href="#c.PyType_Slot" title="PyType_Slot"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyType_Slot</span></code></a> structures.
Terminated by the special slot value <code class="docutils literal notranslate"><span class="pre">{0,</span> <span class="pre">NULL}</span></code>.</p>
<p>Each slot ID should be specified at most once.</p>
</dd></dl>

</dd></dl>

<!-- Keep old URL fragments working (see gh-97908) -->
<span id='c.PyType_Slot.PyType_Slot.slot'></span>
<span id='c.PyType_Slot.PyType_Slot.pfunc'></span><dl class="c type">
<dt class="sig sig-object c" id="c.PyType_Slot">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyType_Slot</span></span></span><a class="headerlink" href="#c.PyType_Slot" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> (including all members).</em><p>Structure defining optional functionality of a type, containing a slot ID
and a value pointer.</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyType_Slot.slot">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">slot</span></span></span><a class="headerlink" href="#c.PyType_Slot.slot" title="Link to this definition">¶</a><br /></dt>
<dd><p>A slot ID.</p>
<p>Slot IDs are named like the field names of the structures
<a class="reference internal" href="#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a>, <a class="reference internal" href="typeobj.html#c.PyNumberMethods" title="PyNumberMethods"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyNumberMethods</span></code></a>,
<a class="reference internal" href="typeobj.html#c.PySequenceMethods" title="PySequenceMethods"><code class="xref c c-type docutils literal notranslate"><span class="pre">PySequenceMethods</span></code></a>, <a class="reference internal" href="typeobj.html#c.PyMappingMethods" title="PyMappingMethods"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMappingMethods</span></code></a> and
<a class="reference internal" href="typeobj.html#c.PyAsyncMethods" title="PyAsyncMethods"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyAsyncMethods</span></code></a> with an added <code class="docutils literal notranslate"><span class="pre">Py_</span></code> prefix.
For example, use:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">Py_tp_dealloc</span></code> to set <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_dealloc" title="PyTypeObject.tp_dealloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyTypeObject.tp_dealloc</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Py_nb_add</span></code> to set <a class="reference internal" href="typeobj.html#c.PyNumberMethods.nb_add" title="PyNumberMethods.nb_add"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyNumberMethods.nb_add</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Py_sq_length</span></code> to set <a class="reference internal" href="typeobj.html#c.PySequenceMethods.sq_length" title="PySequenceMethods.sq_length"><code class="xref c c-member docutils literal notranslate"><span class="pre">PySequenceMethods.sq_length</span></code></a></p></li>
</ul>
<p>The following “offset” fields cannot be set using <a class="reference internal" href="#c.PyType_Slot" title="PyType_Slot"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyType_Slot</span></code></a>:</p>
<ul class="simple">
<li><p><a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_weaklistoffset" title="PyTypeObject.tp_weaklistoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklistoffset</span></code></a>
(use <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_MANAGED_WEAKREF" title="Py_TPFLAGS_MANAGED_WEAKREF"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_WEAKREF</span></code></a> instead if possible)</p></li>
<li><p><a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_dictoffset" title="PyTypeObject.tp_dictoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dictoffset</span></code></a>
(use <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_MANAGED_DICT" title="Py_TPFLAGS_MANAGED_DICT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></code></a> instead if possible)</p></li>
<li><p><a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_vectorcall_offset" title="PyTypeObject.tp_vectorcall_offset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_vectorcall_offset</span></code></a>
(use <code class="docutils literal notranslate"><span class="pre">&quot;__vectorcalloffset__&quot;</span></code> in
<a class="reference internal" href="structures.html#pymemberdef-offsets"><span class="std std-ref">PyMemberDef</span></a>)</p></li>
</ul>
<p>If it is not possible to switch to a <code class="docutils literal notranslate"><span class="pre">MANAGED</span></code> flag (for example,
for vectorcall or to support Python older than 3.12), specify the
offset in <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_members" title="PyTypeObject.tp_members"><code class="xref c c-member docutils literal notranslate"><span class="pre">Py_tp_members</span></code></a>.
See <a class="reference internal" href="structures.html#pymemberdef-offsets"><span class="std std-ref">PyMemberDef documentation</span></a>
for details.</p>
<p>The following fields cannot be set at all when creating a heap type:</p>
<ul class="simple">
<li><p><a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_vectorcall" title="PyTypeObject.tp_vectorcall"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_vectorcall</span></code></a>
(use <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> and/or
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a>)</p></li>
<li><p>Internal fields:
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_dict" title="PyTypeObject.tp_dict"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dict</span></code></a>,
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_mro" title="PyTypeObject.tp_mro"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_mro</span></code></a>,
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_cache" title="PyTypeObject.tp_cache"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_cache</span></code></a>,
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_subclasses" title="PyTypeObject.tp_subclasses"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_subclasses</span></code></a>, and
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_weaklist" title="PyTypeObject.tp_weaklist"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklist</span></code></a>.</p></li>
</ul>
<p>Setting <code class="xref c c-data docutils literal notranslate"><span class="pre">Py_tp_bases</span></code> or <code class="xref c c-data docutils literal notranslate"><span class="pre">Py_tp_base</span></code> may be
problematic on some platforms.
To avoid issues, use the <em>bases</em> argument of
<a class="reference internal" href="#c.PyType_FromSpecWithBases" title="PyType_FromSpecWithBases"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpecWithBases()</span></code></a> instead.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Slots in <a class="reference internal" href="typeobj.html#c.PyBufferProcs" title="PyBufferProcs"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyBufferProcs</span></code></a> may be set in the unlimited API.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="typeobj.html#c.PyBufferProcs.bf_getbuffer" title="PyBufferProcs.bf_getbuffer"><code class="xref c c-member docutils literal notranslate"><span class="pre">bf_getbuffer</span></code></a> and
<a class="reference internal" href="typeobj.html#c.PyBufferProcs.bf_releasebuffer" title="PyBufferProcs.bf_releasebuffer"><code class="xref c c-member docutils literal notranslate"><span class="pre">bf_releasebuffer</span></code></a> are now available
under the <a class="reference internal" href="stable.html#limited-c-api"><span class="std std-ref">limited API</span></a>.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyType_Slot.pfunc">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pfunc</span></span></span><a class="headerlink" href="#c.PyType_Slot.pfunc" title="Link to this definition">¶</a><br /></dt>
<dd><p>The desired value of the slot. In most cases, this is a pointer
to a function.</p>
<p>Slots other than <code class="docutils literal notranslate"><span class="pre">Py_tp_doc</span></code> may not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Type Objects</a><ul>
<li><a class="reference internal" href="#creating-heap-allocated-types">Creating Heap-Allocated Types</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="concrete.html"
                          title="previous chapter">Concrete Objects Layer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="none.html"
                          title="next chapter">The <code class="docutils literal notranslate"><span class="pre">None</span></code> Object</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/type.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="none.html" title="The None Object"
             >next</a> |</li>
        <li class="right" >
          <a href="concrete.html" title="Concrete Objects Layer"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" >Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Type Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>