<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="PyHash API" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/hash.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="See also the PyTypeObject.tp_hash member and Hashing of numeric types." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="See also the PyTypeObject.tp_hash member and Hashing of numeric types." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>PyHash API &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Reflection" href="reflection.html" />
    <link rel="prev" title="String conversion and formatting" href="conversion.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/hash.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="conversion.html"
                          title="previous chapter">String conversion and formatting</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="reflection.html"
                          title="next chapter">Reflection</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/hash.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="reflection.html" title="Reflection"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="conversion.html" title="String conversion and formatting"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="utilities.html" accesskey="U">Utilities</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">PyHash API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="pyhash-api">
<h1>PyHash API<a class="headerlink" href="#pyhash-api" title="Link to this heading">¶</a></h1>
<p>See also the <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_hash" title="PyTypeObject.tp_hash"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyTypeObject.tp_hash</span></code></a> member and <a class="reference internal" href="../library/stdtypes.html#numeric-hash"><span class="std std-ref">Hashing of numeric types</span></a>.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.Py_hash_t">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_hash_t</span></span></span><a class="headerlink" href="#c.Py_hash_t" title="Link to this definition">¶</a><br /></dt>
<dd><p>Hash value type: signed integer.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.Py_uhash_t">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_uhash_t</span></span></span><a class="headerlink" href="#c.Py_uhash_t" title="Link to this definition">¶</a><br /></dt>
<dd><p>Hash value type: unsigned integer.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyHASH_MODULUS">
<span class="sig-name descname"><span class="n"><span class="pre">PyHASH_MODULUS</span></span></span><a class="headerlink" href="#c.PyHASH_MODULUS" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <a class="reference external" href="https://en.wikipedia.org/wiki/Mersenne_prime">Mersenne prime</a> <code class="docutils literal notranslate"><span class="pre">P</span> <span class="pre">=</span> <span class="pre">2**n</span> <span class="pre">-1</span></code>, used for numeric hash scheme.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyHASH_BITS">
<span class="sig-name descname"><span class="n"><span class="pre">PyHASH_BITS</span></span></span><a class="headerlink" href="#c.PyHASH_BITS" title="Link to this definition">¶</a><br /></dt>
<dd><p>The exponent <code class="docutils literal notranslate"><span class="pre">n</span></code> of <code class="docutils literal notranslate"><span class="pre">P</span></code> in <a class="reference internal" href="#c.PyHASH_MODULUS" title="PyHASH_MODULUS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyHASH_MODULUS</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyHASH_MULTIPLIER">
<span class="sig-name descname"><span class="n"><span class="pre">PyHASH_MULTIPLIER</span></span></span><a class="headerlink" href="#c.PyHASH_MULTIPLIER" title="Link to this definition">¶</a><br /></dt>
<dd><p>Prime multiplier used in string and various other hashes.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyHASH_INF">
<span class="sig-name descname"><span class="n"><span class="pre">PyHASH_INF</span></span></span><a class="headerlink" href="#c.PyHASH_INF" title="Link to this definition">¶</a><br /></dt>
<dd><p>The hash value returned for a positive infinity.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyHASH_IMAG">
<span class="sig-name descname"><span class="n"><span class="pre">PyHASH_IMAG</span></span></span><a class="headerlink" href="#c.PyHASH_IMAG" title="Link to this definition">¶</a><br /></dt>
<dd><p>The multiplier used for the imaginary part of a complex number.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyHash_FuncDef">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyHash_FuncDef</span></span></span><a class="headerlink" href="#c.PyHash_FuncDef" title="Link to this definition">¶</a><br /></dt>
<dd><p>Hash function definition used by <a class="reference internal" href="#c.PyHash_GetFuncDef" title="PyHash_GetFuncDef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyHash_GetFuncDef()</span></code></a>.</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyHash_FuncDef.name">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">name</span></span></span><a class="headerlink" href="#c.PyHash_FuncDef.name" title="Link to this definition">¶</a><br /></dt>
<dd><p>Hash function name (UTF-8 encoded string).</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyHash_FuncDef.hash_bits">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">hash_bits</span></span></span><a class="headerlink" href="#c.PyHash_FuncDef.hash_bits" title="Link to this definition">¶</a><br /></dt>
<dd><p>Internal size of the hash value in bits.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyHash_FuncDef.seed_bits">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">seed_bits</span></span></span><a class="headerlink" href="#c.PyHash_FuncDef.seed_bits" title="Link to this definition">¶</a><br /></dt>
<dd><p>Size of seed input in bits.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyHash_GetFuncDef">
<a class="reference internal" href="#c.PyHash_FuncDef" title="PyHash_FuncDef"><span class="n"><span class="pre">PyHash_FuncDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyHash_GetFuncDef</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyHash_GetFuncDef" title="Link to this definition">¶</a><br /></dt>
<dd><p>Get the hash function definition.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0456/"><strong>PEP 456</strong></a> “Secure and interchangeable hash algorithm”.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_HashPointer">
<a class="reference internal" href="#c.Py_hash_t" title="Py_hash_t"><span class="n"><span class="pre">Py_hash_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_HashPointer</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ptr</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_HashPointer" title="Link to this definition">¶</a><br /></dt>
<dd><p>Hash a pointer value: process the pointer value as an integer (cast it to
<code class="docutils literal notranslate"><span class="pre">uintptr_t</span></code> internally). The pointer is not dereferenced.</p>
<p>The function cannot fail: it cannot return <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_GenericHash">
<a class="reference internal" href="#c.Py_hash_t" title="Py_hash_t"><span class="n"><span class="pre">Py_hash_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_GenericHash</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_GenericHash" title="Link to this definition">¶</a><br /></dt>
<dd><p>Generic hashing function that is meant to be put into a type
object’s <code class="docutils literal notranslate"><span class="pre">tp_hash</span></code> slot.
Its result only depends on the object’s identity.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> In CPython, it is equivalent to <a class="reference internal" href="#c.Py_HashPointer" title="Py_HashPointer"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_HashPointer()</span></code></a>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="conversion.html"
                          title="previous chapter">String conversion and formatting</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="reflection.html"
                          title="next chapter">Reflection</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/hash.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="reflection.html" title="Reflection"
             >next</a> |</li>
        <li class="right" >
          <a href="conversion.html" title="String conversion and formatting"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="utilities.html" >Utilities</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">PyHash API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>