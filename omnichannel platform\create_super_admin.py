from sqlmodel import Session, SQLModel, create_engine
from app.models.user import User, UserRole, UserStatus
from app.models.company import Company
from passlib.context import CryptContext

# Use the same database as the app
engine = create_engine('sqlite:///whatsapp_platform_new.db')
SQLModel.metadata.create_all(engine)  # Create tables if they don't exist
session = Session(engine)

from sqlmodel import select
company = session.exec(select(Company)).first()
if not company:
    company = Company(name='Echo Voip')
    session.add(company)
    session.commit()
    print(f"Created company: {company.name} (id={company.id})")
else:
    print(f"Using existing company: {company.name} (id={company.id})")

pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')

# Super admin user
super_admin_email = '<EMAIL>'
hashed_password = pwd_context.hash('SuperAdmin5604')
existing = session.exec(select(User).where(User.email == super_admin_email)).first()
if existing:
    print(f"Super admin already exists: {super_admin_email}")
else:
    user = User(
        email=super_admin_email,
        username='superadmin',
        hashed_password=hashed_password,
        first_name='Super',
        last_name='Admin',
        company_id=company.id,
        role=UserRole.SUPER_ADMIN,
        status=UserStatus.ACTIVE,
        is_email_verified=True
    )
    session.add(user)
    session.commit()
    print(f"Created super admin: {user.email}")

# Test login user
login_test_email = '<EMAIL>'
login_test_password = 'password1234'  # Change as needed
hashed_test_password = pwd_context.hash(login_test_password)
existing_test = session.exec(select(User).where(User.email == login_test_email)).first()
if existing_test:
    existing_test.hashed_password = hashed_test_password
    existing_test.status = UserStatus.ACTIVE
    existing_test.company_id = company.id
    existing_test.role = UserRole.USER
    session.add(existing_test)
    session.commit()
    print(f"Test login user updated: {login_test_email}")
else:
    test_user = User(
        email=login_test_email,
        username='testuser',
        hashed_password=hashed_test_password,
        first_name='Test',
        last_name='User',
        company_id=company.id,
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_email_verified=True
    )
    session.add(test_user)
    session.commit()
    print(f"Test login user created: {login_test_email}")
