from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select, func
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from app.models.user import User, UserRole, UserStatus
from app.models.omnichannel import Omnichannel<PERSON><PERSON><PERSON>, Customer, Channel
from app.models.conversation import Conversation
from app.models.whatsapp import Campaign
from app.models.analytics import MessageAnalytics, CampaignAnalytics
from app.db.session import get_db
from app.api.dependencies import get_current_user

router = APIRouter(prefix="/analytics", tags=["analytics"])

@router.get("/dashboard-metrics")
def get_dashboard_metrics(
    company_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive dashboard metrics for the current user's company."""
    # Determine which company to query
    target_company_id = company_id if current_user.role == "super_admin" else current_user.company_id

    if current_user.role != "super_admin" and company_id and company_id != current_user.company_id:
        raise HTTPException(status_code=403, detail="Forbidden")

    # Get date ranges for comparison
    now = datetime.utcnow()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    week_start = today_start - timedelta(days=7)
    month_start = today_start - timedelta(days=30)

    try:
        # Total conversations
        total_conversations = db.exec(
            select(func.count(Conversation.id))
            .where(Conversation.company_id == target_company_id)
        ).first() or 0

        # Active conversations (last 24 hours)
        active_conversations = db.exec(
            select(func.count(Conversation.id))
            .where(
                Conversation.company_id == target_company_id,
                Conversation.status == "active",
                Conversation.last_message_at >= today_start
            )
        ).first() or 0

        # Total customers
        total_customers = db.exec(
            select(func.count(Customer.id))
            .where(Customer.company_id == target_company_id)
        ).first() or 0

        # Messages sent (last 7 days)
        messages_sent = db.exec(
            select(func.count(OmnichannelMessage.id))
            .where(
                OmnichannelMessage.company_id == target_company_id,
                OmnichannelMessage.direction == "outbound",
                OmnichannelMessage.created_at >= week_start
            )
        ).first() or 0

        # Calculate average response time (simplified - time between customer message and agent response)
        avg_response_time = db.exec(
            select(func.avg(
                func.extract('epoch', OmnichannelMessage.created_at) -
                func.lag(func.extract('epoch', OmnichannelMessage.created_at))
                .over(partition_by=OmnichannelMessage.conversation_id, order_by=OmnichannelMessage.created_at)
            ))
            .where(
                OmnichannelMessage.company_id == target_company_id,
                OmnichannelMessage.direction == "outbound",
                OmnichannelMessage.created_at >= week_start
            )
        ).first() or 0

        # Convert seconds to minutes for display
        avg_response_minutes = round(avg_response_time / 60, 1) if avg_response_time else 0

        # Get previous week's data for comparison
        prev_week_start = week_start - timedelta(days=7)
        prev_messages_sent = db.exec(
            select(func.count(OmnichannelMessage.id))
            .where(
                OmnichannelMessage.company_id == target_company_id,
                OmnichannelMessage.direction == "outbound",
                OmnichannelMessage.created_at >= prev_week_start,
                OmnichannelMessage.created_at < week_start
            )
        ).first() or 0

        # Calculate percentage changes
        messages_change = ((messages_sent - prev_messages_sent) / prev_messages_sent * 100) if prev_messages_sent > 0 else 0

        return {
            "total_conversations": total_conversations,
            "active_conversations": active_conversations,
            "total_customers": total_customers,
            "messages_sent": messages_sent,
            "avg_response_time_minutes": avg_response_minutes,
            "messages_change_percent": round(messages_change, 1),
            "last_updated": now.isoformat()
        }

    except Exception as e:
        # Return default values if there's an error
        return {
            "total_conversations": 0,
            "active_conversations": 0,
            "total_customers": 0,
            "messages_sent": 0,
            "avg_response_time_minutes": 0,
            "messages_change_percent": 0,
            "last_updated": now.isoformat(),
            "error": str(e)
        }

@router.get("/message-volume")
def message_volume(company_id: Optional[int] = None, channel_id: Optional[int] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    query = select(func.count(OmnichannelMessage.id))
    if current_user.role == "super_admin":
        if company_id:
            query = query.where(OmnichannelMessage.company_id == company_id)
    else:
        query = query.where(OmnichannelMessage.company_id == current_user.company_id)
    if channel_id:
        query = query.where(OmnichannelMessage.channel_id == channel_id)
    count = db.exec(query).one()
    return {"message_volume": count}

@router.get("/response-times")
def response_times(company_id: Optional[int] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if current_user.role != "super_admin" and (company_id is not None and company_id != current_user.company_id):
        raise HTTPException(status_code=403, detail="Forbidden")
    # Placeholder: Calculate average response time
    return {"average_response_time_seconds": 42}

@router.get("/delivery-rates")
def delivery_rates(company_id: Optional[int] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if current_user.role != "super_admin" and (company_id is not None and company_id != current_user.company_id):
        raise HTTPException(status_code=403, detail="Forbidden")
    # Placeholder: Calculate delivery/failure rates
    return {"delivery_rate": 0.98, "failure_rate": 0.02}

@router.get("/agent-performance")
def agent_performance(company_id: Optional[int] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if current_user.role != "super_admin" and (company_id is not None and company_id != current_user.company_id):
        raise HTTPException(status_code=403, detail="Forbidden")
    # Placeholder: Calculate agent/bot performance stats
    return {"top_agents": ["Alice", "Bob"], "resolved_conversations": 123}


@router.get("/dashboard-metrics-test")
def get_dashboard_metrics_test(db: Session = Depends(get_db)):
    """Test endpoint for dashboard metrics without authentication - returns mock data when no database data exists."""
    try:
        # Try to get real data first
        from datetime import datetime, timedelta
        from sqlmodel import select, func
        from app.models.conversation import Conversation
        from app.models.omnichannel import Customer, OmnichannelMessage

        # Get current week start (Monday)
        today = datetime.utcnow().date()
        week_start = today - timedelta(days=today.weekday())
        week_start_datetime = datetime.combine(week_start, datetime.min.time())

        # Try to query real data
        total_conversations = db.exec(select(func.count(Conversation.id))).first() or 0
        active_conversations = db.exec(
            select(func.count(Conversation.id))
            .where(Conversation.status == "active")
        ).first() or 0
        total_customers = db.exec(select(func.count(Customer.id))).first() or 0
        messages_sent = db.exec(
            select(func.count(OmnichannelMessage.id))
            .where(
                OmnichannelMessage.direction == "outbound",
                OmnichannelMessage.created_at >= week_start_datetime
            )
        ).first() or 0

        # If we have real data, return it
        if total_conversations > 0 or total_customers > 0 or messages_sent > 0:
            return {
                "total_conversations": total_conversations,
                "active_conversations": active_conversations,
                "total_customers": total_customers,
                "messages_sent": messages_sent,
                "avg_response_time_minutes": 2.5,
                "messages_change_percent": 15.2,
                "data_source": "database"
            }
        else:
            # Return mock data if no real data exists
            return {
                "total_conversations": 1247,
                "active_conversations": 89,
                "total_customers": 3456,
                "messages_sent": 892,
                "avg_response_time_minutes": 2.5,
                "messages_change_percent": 15.2,
                "data_source": "mock"
            }
    except Exception as e:
        # Return mock data if there's any error
        return {
            "total_conversations": 1247,
            "active_conversations": 89,
            "total_customers": 3456,
            "messages_sent": 892,
            "avg_response_time_minutes": 2.5,
            "messages_change_percent": 15.2,
            "data_source": "mock_error",
            "error": str(e)
        }


@router.post("/create-test-users")
def create_test_users(db: Session = Depends(get_db)):
    """Create test users for development - REMOVE IN PRODUCTION!"""
    try:
        from app.models.company import Company
        from passlib.context import CryptContext

        pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')

        # Create or get company
        company = db.exec(select(Company)).first()
        if not company:
            company = Company(
                name='Test Company',
                description='Test company for development'
            )
            db.add(company)
            db.commit()
            db.refresh(company)

        # Create super admin user
        super_admin_email = '<EMAIL>'
        super_admin_password = 'admin123'

        existing_admin = db.exec(select(User).where(User.email == super_admin_email)).first()
        if not existing_admin:
            hashed_password = pwd_context.hash(super_admin_password)
            admin_user = User(
                email=super_admin_email,
                username='admin',
                hashed_password=hashed_password,
                first_name='Admin',
                last_name='User',
                company_id=company.id,
                role=UserRole.SUPER_ADMIN,
                status=UserStatus.ACTIVE,
                is_email_verified=True
            )
            db.add(admin_user)
            db.commit()

        # Create regular test user
        test_email = '<EMAIL>'
        test_password = 'test123'

        existing_test = db.exec(select(User).where(User.email == test_email)).first()
        if not existing_test:
            hashed_password = pwd_context.hash(test_password)
            test_user = User(
                email=test_email,
                username='testuser',
                hashed_password=hashed_password,
                first_name='Test',
                last_name='User',
                company_id=company.id,
                role=UserRole.USER,
                status=UserStatus.ACTIVE,
                is_email_verified=True
            )
            db.add(test_user)
            db.commit()

        return {
            "success": True,
            "message": "Test users created successfully",
            "credentials": {
                "admin": {"email": super_admin_email, "password": super_admin_password},
                "user": {"email": test_email, "password": test_password}
            }
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
