from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select, func
from typing import Optional
from app.models.user import User
from app.models.omnichannel import OmnichannelMessage
from app.models.conversation import Conversation
from app.db.session import get_db
from app.api.dependencies import get_current_user

router = APIRouter(prefix="/analytics", tags=["analytics"])

@router.get("/message-volume")
def message_volume(company_id: Optional[int] = None, channel_id: Optional[int] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    query = select(func.count(Message.id))
    if current_user.role == "super_admin":
        if company_id:
            query = query.where(Message.company_id == company_id)
    else:
        query = query.where(Message.company_id == current_user.company_id)
    if channel_id:
        query = query.where(Message.channel_id == channel_id)
    count = db.exec(query).one()
    return {"message_volume": count}

@router.get("/response-times")
def response_times(company_id: Optional[int] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if current_user.role != "super_admin" and (company_id is not None and company_id != current_user.company_id):
        raise HTTPException(status_code=403, detail="Forbidden")
    # Placeholder: Calculate average response time
    return {"average_response_time_seconds": 42}

@router.get("/delivery-rates")
def delivery_rates(company_id: Optional[int] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if current_user.role != "super_admin" and (company_id is not None and company_id != current_user.company_id):
        raise HTTPException(status_code=403, detail="Forbidden")
    # Placeholder: Calculate delivery/failure rates
    return {"delivery_rate": 0.98, "failure_rate": 0.02}

@router.get("/agent-performance")
def agent_performance(company_id: Optional[int] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if current_user.role != "super_admin" and (company_id is not None and company_id != current_user.company_id):
        raise HTTPException(status_code=403, detail="Forbidden")
    # Placeholder: Calculate agent/bot performance stats
    return {"top_agents": ["Alice", "Bob"], "resolved_conversations": 123}
