"""API endpoints for Telegram functionality."""

from typing import List, Optional, Union

from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select

from app.db.session import get_db
from app.api.dependencies import get_current_user
from app.models.user import User
from app.models.telegram import TelegramAccount, TelegramAccountStatus
from app.schemas.telegram import TelegramAccountCreate, TelegramAccountRead, TelegramAccountUpdate, ListResponse
from app.services.telegram_api import TelegramBotAPI

router = APIRouter(prefix="/telegram", tags=["telegram"])


# Telegram Accounts
@router.post("/accounts/", response_model=TelegramAccountRead)
async def create_telegram_account(
    account: TelegramAccountCreate,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new Telegram account."""
    db_account = TelegramAccount.from_orm(account)
    db_account.user_id = current_user.id

    # Verify bot token with Telegram API
    telegram_api = TelegramBotAPI(db_account.bot_token)
    try:
        bot_info = await telegram_api.get_me()
        db_account.is_verified = True
        db_account.verified_at = datetime.utcnow()
        db_account.username = bot_info.get("username")
        db_account.status = TelegramAccountStatus.ACTIVE
    except Exception as e:
        db_account.is_verified = False
        db_account.status = TelegramAccountStatus.PENDING # Or FAILED
        # Optionally store error message in metadata_
        db_account.metadata_["verification_error"] = str(e)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Telegram bot token verification failed: {e}")
    finally:
        await telegram_api.close()

    session.add(db_account)
    session.commit()
    session.refresh(db_account)
    return db_account


@router.post("/accounts/{account_id}/set-webhook", response_model=dict)
async def set_telegram_webhook(
    account_id: int,
    webhook_url: str,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Set the webhook URL for a Telegram account."""
    db_account = session.get(TelegramAccount, account_id)
    if not db_account or db_account.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Telegram account not found")

    telegram_api = TelegramBotAPI(db_account.bot_token)
    try:
        response = await telegram_api.set_webhook(url=webhook_url)
        if response.get("ok"):
            db_account.metadata_["webhook_url"] = webhook_url
            session.add(db_account)
            session.commit()
            session.refresh(db_account)
            return {"status": "success", "message": "Webhook set successfully"}
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.get("description", "Failed to set webhook"))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error setting webhook: {e}")
    finally:
        await telegram_api.close()


@router.post("/accounts/{account_id}/send-message", response_model=dict)
async def send_telegram_test_message(
    account_id: int,
    chat_id: Union[int, str],
    text: str,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Send a test message from a Telegram account."""
    db_account = session.get(TelegramAccount, account_id)
    if not db_account or db_account.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Telegram account not found")

    telegram_api = TelegramBotAPI(db_account.bot_token)
    try:
        response = await telegram_api.send_message(chat_id=chat_id, text=text)
        if response.get("ok"):
            return {"status": "success", "message": "Test message sent successfully"}
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.get("description", "Failed to send message"))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error sending test message: {e}")
    finally:
        await telegram_api.close()


@router.get("/accounts/", response_model=ListResponse[TelegramAccountRead])
def list_telegram_accounts(
    status: Optional[TelegramAccountStatus] = None,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """List all Telegram accounts with optional filtering."""
    query = select(TelegramAccount).where(TelegramAccount.user_id == current_user.id)
    if status:
        query = query.where(TelegramAccount.status == status)
    accounts = session.exec(query).all()
    return ListResponse(data=accounts, total=len(accounts))


@router.get("/accounts/{account_id}", response_model=TelegramAccountRead)
def get_telegram_account(
    account_id: int,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific Telegram account by ID."""
    account = session.get(TelegramAccount, account_id)
    if not account or account.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Telegram account not found")
    return account


@router.patch("/accounts/{account_id}", response_model=TelegramAccountRead)
def update_telegram_account(
    account_id: int,
    account_update: TelegramAccountUpdate,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a Telegram account."""
    db_account = session.get(TelegramAccount, account_id)
    if not db_account or db_account.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Telegram account not found")
    
    account_data = account_update.dict(exclude_unset=True)
    for key, value in account_data.items():
        setattr(db_account, key, value)
    
    session.add(db_account)
    session.commit()
    session.refresh(db_account)
    return db_account


@router.delete("/accounts/{account_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_telegram_account(
    account_id: int,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a Telegram account (soft delete)."""
    db_account = session.get(TelegramAccount, account_id)
    if not db_account or db_account.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Telegram account not found")
    
    # Soft delete: set status to DELETED instead of actual deletion
    db_account.status = TelegramAccountStatus.DELETED
    session.add(db_account)
    session.commit()
    return
