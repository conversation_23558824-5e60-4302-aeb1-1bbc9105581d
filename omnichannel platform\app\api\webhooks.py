"""WhatsApp webhook API endpoints."""

from fastapi import APIRouter, Request, HTTPException, Query
from fastapi.responses import PlainTextResponse, JSONResponse
import json
from typing import Dict, Any
import logging

from ..services.webhook_service import WebhookService

router = APIRouter(prefix="/webhooks", tags=["webhooks"])
from ..database import get_session

logger = logging.getLogger(__name__)

# Initialize webhook service
# In production, these should come from environment variables
WEBHOOK_VERIFY_TOKEN = "your_webhook_verify_token_here"
WEBHOOK_APP_SECRET = "your_app_secret_here"

webhook_service = WebhookService(WEBHOOK_VERIFY_TOKEN, WEBHOOK_APP_SECRET)


class WebhookAPI:
    """WhatsApp webhook API endpoints."""
    
    @staticmethod
    def verify_webhook(hub_mode: str, hub_verify_token: str, hub_challenge: str) -> str:
        """Verify webhook subscription (GET request)."""
        try:
            logger.info(f"Webhook verification request: mode={hub_mode}, token={hub_verify_token}")
            
            challenge = webhook_service.verify_webhook(hub_mode, hub_verify_token, hub_challenge)
            
            if challenge:
                return challenge
            else:
                raise ValueError("Webhook verification failed")
                
        except Exception as e:
            logger.error(f"Error in webhook verification: {str(e)}")
            raise Exception("Internal Server Error")
    
    @staticmethod
    def handle_webhook(request_body: str, signature: str = "") -> Dict[str, Any]:
        """
        Handle incoming webhook (POST request).
        
        Args:
            request_body: The raw request body as a string
            signature: The signature header for verification
            
        Returns:
            Dict with 'content' and 'status_code' for the response
        """
        try:
            logger.info("Received webhook payload")
            
            # Verify signature if provided
            if signature and not webhook_service.verify_signature(request_body, signature):
                logger.warning("Webhook signature verification failed")
                return {
                    "content": {"error": "Invalid signature"},
                    "status_code": 403
                }
            
            # Parse JSON payload
            try:
                payload = json.loads(request_body)
                logger.info(f"Webhook payload: {json.dumps(payload, indent=2)}")
                
                # Process the webhook event
                with get_session() as session:
                    webhook_service.process_webhook(payload, session)
                
                return {
                    "content": {"status": "success"},
                    "status_code": 200
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON payload: {str(e)}")
                return {
                    "content": {"error": "Invalid JSON payload"},
                    "status_code": 400
                }
                
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}", exc_info=True)
            return {
                "content": {"error": "Internal server error"},
                "status_code": 500
            }


# FastAPI endpoints for webhook management

@router.get("/whatsapp")
async def verify_whatsapp_webhook(
    hub_mode: str = Query(..., alias="hub.mode"),
    hub_challenge: str = Query(..., alias="hub.challenge"),
    hub_verify_token: str = Query(..., alias="hub.verify_token")
):
    """Verify WhatsApp webhook subscription."""
    try:
        challenge = WebhookAPI.verify_webhook(hub_mode, hub_verify_token, hub_challenge)
        return PlainTextResponse(content=challenge)
    except Exception as e:
        logger.error(f"Webhook verification failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")


@router.post("/whatsapp")
async def handle_whatsapp_webhook(request: Request):
    """Handle incoming WhatsApp webhook events."""
    try:
        body = await request.body()
        signature = request.headers.get("X-Hub-Signature-256", "")

        result = WebhookAPI.handle_webhook(body.decode(), signature)

        if result["status_code"] == 200:
            return JSONResponse(content=result["content"])
        else:
            raise HTTPException(status_code=result["status_code"], detail=result["content"])

    except Exception as e:
        logger.error(f"Error handling webhook: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/status")
async def get_webhook_status():
    """Get webhook status and health information."""
    return {
        "status": "active",
        "last_event": "2 minutes ago",
        "events_today": 1247,
        "avg_response_time": "45ms"
    }


@router.get("/events/recent")
async def get_recent_webhook_events():
    """Get recent webhook events for monitoring."""
    return [
        {
            "type": "message_status",
            "description": "Message delivered to +**********",
            "timestamp": "2 min ago"
        },
        {
            "type": "incoming_message",
            "description": "User replied: 'Thank you!'",
            "timestamp": "5 min ago"
        },
        {
            "type": "template_status",
            "description": "Template 'welcome_message' approved",
            "timestamp": "1 hour ago"
        }
    ]


# Webhook endpoint handlers for backward compatibility
def create_webhook_handlers():
    """Create webhook endpoint handlers."""
    webhook_handlers = {
        "verify": WebhookAPI.verify_webhook,
        "handle": WebhookAPI.handle_webhook
    }
    return webhook_handlers
