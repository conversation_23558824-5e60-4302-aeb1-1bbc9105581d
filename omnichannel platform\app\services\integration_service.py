from typing import Optional, List
from sqlalchemy.orm import Session # Assuming Session is from sqlalchemy.orm for FastAPI/SQLModel
from app.repositories import integration_repository
from app.schemas.integrations import WhatsAppCredentialsCreate, IntegrationConfig, IntegrationConfigUpdate
from app.models.integrations import IntegrationSetting
from app.core.security import encrypt_data, decrypt_data
from app.services.exceptions import ServiceException

def create_or_update_whatsapp_integration(db: Session, *, creds_in: WhatsAppCredentialsCreate, company_id: int) -> IntegrationSetting:
    """
    Creates a new WhatsApp integration setting or updates it if it already exists.
    Ensures that if WhatsApp is activated, any other primary messaging integration (e.g., test_sender) is deactivated.
    """
    encrypted_token = encrypt_data(creds_in.access_token)
    
    existing_whatsapp = integration_repository.get_integration_setting_by_type(db, integration_type="whatsapp", company_id=company_id)

    if existing_whatsapp:
        # Update existing WhatsApp integration
        update_data = IntegrationConfigUpdate(
            is_active=True, # Activating WhatsApp
            phone_number_id=creds_in.phone_number_id,
            business_account_id=creds_in.business_account_id,
            company_id=company_id,
            # encrypted_access_token would be part of the model, not schema for update usually
        )
        # Manually set encrypted token on the model if not directly in schema
        existing_whatsapp.encrypted_access_token = encrypted_token
        updated_setting = integration_repository.update_integration_setting(
            db=db, db_setting=existing_whatsapp, setting_in=update_data
        )
    else:
        # Create new WhatsApp integration
        # We need to map WhatsAppCredentialsCreate + encrypted_token to IntegrationSetting fields
        # This might be better handled by a specific schema or direct model creation
        new_setting_data = {
            "integration_type": "whatsapp",
            "is_active": True,
            "display_name": "WhatsApp Business", # Default display name
            "phone_number_id": creds_in.phone_number_id,
            "encrypted_access_token": encrypted_token,
            "business_account_id": creds_in.business_account_id,
            "company_id": company_id,
        }
        # IntegrationConfigCreate might need to be adjusted or we directly create IntegrationSetting
        # For now, let's assume IntegrationSetting can be created directly or via a suitable schema
        updated_setting = IntegrationSetting(**new_setting_data)
        db.add(updated_setting)
        # db.commit() # Will be committed after deactivating others
        # db.refresh(updated_setting) # And refreshed

    # Deactivate 'test_sender' if WhatsApp is being activated
    test_sender = integration_repository.get_integration_setting_by_type(db, integration_type="test_sender", company_id=company_id)
    if test_sender and test_sender.is_active:
        integration_repository.update_integration_setting(
            db=db, db_setting=test_sender, setting_in=IntegrationConfigUpdate(is_active=False)
        )
    
    db.commit() # Commit all changes (new/updated WhatsApp, deactivated test_sender)
    if existing_whatsapp:
         db.refresh(existing_whatsapp)
         return existing_whatsapp
    else:
        db.refresh(updated_setting)
        return updated_setting


def get_integration(db: Session, setting_id: int, company_id: int) -> Optional[IntegrationSetting]:
    return integration_repository.get_integration_setting(db, setting_id=setting_id, company_id=company_id)

def get_integrations(db: Session, skip: int = 0, limit: int = 100, company_id: int) -> List[IntegrationSetting]:
    return integration_repository.get_all_integration_settings(db, skip=skip, limit=limit, company_id=company_id)

def get_integration_by_type(db: Session, integration_type: str, company_id: int) -> Optional[IntegrationSetting]:
    return integration_repository.get_integration_setting_by_type(db, integration_type=integration_type, company_id=company_id)

def toggle_integration_status(db: Session, integration_id: int, activate: bool, company_id: int) -> Optional[IntegrationSetting]:
    """
    Activates or deactivates an integration.
    If activating 'whatsapp' or 'test_sender', deactivates the other.
    """
    target_integration = integration_repository.get_integration_setting(db, setting_id=integration_id, company_id=company_id)
    if not target_integration:
        raise ServiceException(f"Integration with id {integration_id} not found.")

    if activate:
        # If activating WhatsApp, deactivate Test Sender
        if target_integration.integration_type == "whatsapp":
            other_integration = integration_repository.get_integration_setting_by_type(db, integration_type="test_sender", company_id=company_id)
            if other_integration and other_integration.is_active:
                integration_repository.update_integration_setting(
                    db, db_setting=other_integration, setting_in=IntegrationConfigUpdate(is_active=False)
                )
        # If activating Test Sender, deactivate WhatsApp
        elif target_integration.integration_type == "test_sender":
            other_integration = integration_repository.get_integration_setting_by_type(db, integration_type="whatsapp", company_id=company_id)
            if other_integration and other_integration.is_active:
                integration_repository.update_integration_setting(
                    db, db_setting=other_integration, setting_in=IntegrationConfigUpdate(is_active=False)
                )
    
    updated_target = integration_repository.update_integration_setting(
        db, db_setting=target_integration, setting_in=IntegrationConfigUpdate(is_active=activate)
    )
    db.commit() # Commit after all potential changes
    db.refresh(updated_target)
    if 'other_integration' in locals() and other_integration: # Check if other_integration was defined and modified
        db.refresh(other_integration)
    return updated_target

def get_decrypted_access_token(db: Session, integration_type: str, company_id: int) -> Optional[str]:
    integration = integration_repository.get_integration_setting_by_type(db, integration_type=integration_type, company_id=company_id)
