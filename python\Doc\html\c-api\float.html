<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Floating-Point Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/float.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Pack and Unpack functions: The pack and unpack functions provide an efficient platform-independent way to store floating-point values as byte strings. The Pack routines produce a bytes string from ..." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Pack and Unpack functions: The pack and unpack functions provide an efficient platform-independent way to store floating-point values as byte strings. The Pack routines produce a bytes string from ..." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Floating-Point Objects &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Complex Number Objects" href="complex.html" />
    <link rel="prev" title="Boolean Objects" href="bool.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/float.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Floating-Point Objects</a><ul>
<li><a class="reference internal" href="#pack-and-unpack-functions">Pack and Unpack functions</a><ul>
<li><a class="reference internal" href="#pack-functions">Pack functions</a></li>
<li><a class="reference internal" href="#unpack-functions">Unpack functions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="bool.html"
                          title="previous chapter">Boolean Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="complex.html"
                          title="next chapter">Complex Number Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/float.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="complex.html" title="Complex Number Objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="bool.html" title="Boolean Objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" accesskey="U">Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Floating-Point Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="floating-point-objects">
<span id="floatobjects"></span><h1>Floating-Point Objects<a class="headerlink" href="#floating-point-objects" title="Link to this heading">¶</a></h1>
<dl class="c type" id="index-0">
<dt class="sig sig-object c" id="c.PyFloatObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloatObject</span></span></span><a class="headerlink" href="#c.PyFloatObject" title="Link to this definition">¶</a><br /></dt>
<dd><p>This subtype of <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> represents a Python floating-point object.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyFloat_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_Type</span></span></span><a class="headerlink" href="#c.PyFloat_Type" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This instance of <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> represents the Python floating-point
type.  This is the same object as <a class="reference internal" href="../library/functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> in the Python layer.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if its argument is a <a class="reference internal" href="#c.PyFloatObject" title="PyFloatObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyFloatObject</span></code></a> or a subtype of
<a class="reference internal" href="#c.PyFloatObject" title="PyFloatObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyFloatObject</span></code></a>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_CheckExact">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_CheckExact</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_CheckExact" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if its argument is a <a class="reference internal" href="#c.PyFloatObject" title="PyFloatObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyFloatObject</span></code></a>, but not a subtype of
<a class="reference internal" href="#c.PyFloatObject" title="PyFloatObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyFloatObject</span></code></a>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_FromString">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_FromString</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_FromString" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Create a <a class="reference internal" href="#c.PyFloatObject" title="PyFloatObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyFloatObject</span></code></a> object based on the string value in <em>str</em>, or
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> on failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_FromDouble">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_FromDouble</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="n"><span class="pre">v</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_FromDouble" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Create a <a class="reference internal" href="#c.PyFloatObject" title="PyFloatObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyFloatObject</span></code></a> object from <em>v</em>, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> on failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_AsDouble">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_AsDouble</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">pyfloat</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_AsDouble" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return a C <span class="c-expr sig sig-inline c"><span class="kt">double</span></span> representation of the contents of <em>pyfloat</em>.  If
<em>pyfloat</em> is not a Python floating-point object but has a <a class="reference internal" href="../reference/datamodel.html#object.__float__" title="object.__float__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code></a>
method, this method will first be called to convert <em>pyfloat</em> into a float.
If <code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code> is not defined then it falls back to <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a>.
This method returns <code class="docutils literal notranslate"><span class="pre">-1.0</span></code> upon failure, so one should call
<a class="reference internal" href="exceptions.html#c.PyErr_Occurred" title="PyErr_Occurred"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Occurred()</span></code></a> to check for errors.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Use <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> if available.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_AS_DOUBLE">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_AS_DOUBLE</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">pyfloat</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_AS_DOUBLE" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return a C <span class="c-expr sig sig-inline c"><span class="kt">double</span></span> representation of the contents of <em>pyfloat</em>, but
without error checking.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_GetInfo">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_GetInfo</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_GetInfo" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return a structseq instance which contains information about the
precision, minimum and maximum values of a float. It’s a thin wrapper
around the header file <code class="file docutils literal notranslate"><span class="pre">float.h</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_GetMax">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_GetMax</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_GetMax" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the maximum representable finite float <em>DBL_MAX</em> as C <span class="c-expr sig sig-inline c"><span class="kt">double</span></span>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_GetMin">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_GetMin</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_GetMin" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the minimum normalized positive float <em>DBL_MIN</em> as C <span class="c-expr sig sig-inline c"><span class="kt">double</span></span>.</p>
</dd></dl>

<section id="pack-and-unpack-functions">
<h2>Pack and Unpack functions<a class="headerlink" href="#pack-and-unpack-functions" title="Link to this heading">¶</a></h2>
<p>The pack and unpack functions provide an efficient platform-independent way to
store floating-point values as byte strings. The Pack routines produce a bytes
string from a C <span class="c-expr sig sig-inline c"><span class="kt">double</span></span>, and the Unpack routines produce a C
<span class="c-expr sig sig-inline c"><span class="kt">double</span></span> from such a bytes string. The suffix (2, 4 or 8) specifies the
number of bytes in the bytes string.</p>
<p>On platforms that appear to use IEEE 754 formats these functions work by
copying bits. On other platforms, the 2-byte format is identical to the IEEE
754 binary16 half-precision format, the 4-byte format (32-bit) is identical to
the IEEE 754 binary32 single precision format, and the 8-byte format to the
IEEE 754 binary64 double precision format, although the packing of INFs and
NaNs (if such things exist on the platform) isn’t handled correctly, and
attempting to unpack a bytes string containing an IEEE INF or NaN will raise an
exception.</p>
<p>On non-IEEE platforms with more precision, or larger dynamic range, than IEEE
754 supports, not all values can be packed; on non-IEEE platforms with less
precision, or smaller dynamic range, not all values can be unpacked. What
happens in such cases is partly accidental (alas).</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.11.</span></p>
</div>
<section id="pack-functions">
<h3>Pack functions<a class="headerlink" href="#pack-functions" title="Link to this heading">¶</a></h3>
<p>The pack routines write 2, 4 or 8 bytes, starting at <em>p</em>. <em>le</em> is an
<span class="c-expr sig sig-inline c"><span class="kt">int</span></span> argument, non-zero if you want the bytes string in little-endian
format (exponent last, at <code class="docutils literal notranslate"><span class="pre">p+1</span></code>, <code class="docutils literal notranslate"><span class="pre">p+3</span></code>, or <code class="docutils literal notranslate"><span class="pre">p+6</span></code> <code class="docutils literal notranslate"><span class="pre">p+7</span></code>), zero if you
want big-endian format (exponent first, at <em>p</em>). The <code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_BIG_ENDIAN</span></code>
constant can be used to use the native endian: it is equal to <code class="docutils literal notranslate"><span class="pre">1</span></code> on big
endian processor, or <code class="docutils literal notranslate"><span class="pre">0</span></code> on little endian processor.</p>
<p>Return value: <code class="docutils literal notranslate"><span class="pre">0</span></code> if all is OK, <code class="docutils literal notranslate"><span class="pre">-1</span></code> if error (and an exception is set,
most likely <a class="reference internal" href="../library/exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a>).</p>
<p>There are two problems on non-IEEE platforms:</p>
<ul class="simple">
<li><p>What this does is undefined if <em>x</em> is a NaN or infinity.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">-0.0</span></code> and <code class="docutils literal notranslate"><span class="pre">+0.0</span></code> produce the same bytes string.</p></li>
</ul>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_Pack2">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_Pack2</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="n"><span class="pre">x</span></span>, <span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">le</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_Pack2" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pack a C double as the IEEE 754 binary16 half-precision format.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_Pack4">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_Pack4</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="n"><span class="pre">x</span></span>, <span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">le</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_Pack4" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pack a C double as the IEEE 754 binary32 single precision format.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_Pack8">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_Pack8</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="n"><span class="pre">x</span></span>, <span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">le</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_Pack8" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pack a C double as the IEEE 754 binary64 double precision format.</p>
</dd></dl>

</section>
<section id="unpack-functions">
<h3>Unpack functions<a class="headerlink" href="#unpack-functions" title="Link to this heading">¶</a></h3>
<p>The unpack routines read 2, 4 or 8 bytes, starting at <em>p</em>.  <em>le</em> is an
<span class="c-expr sig sig-inline c"><span class="kt">int</span></span> argument, non-zero if the bytes string is in little-endian format
(exponent last, at <code class="docutils literal notranslate"><span class="pre">p+1</span></code>, <code class="docutils literal notranslate"><span class="pre">p+3</span></code> or <code class="docutils literal notranslate"><span class="pre">p+6</span></code> and <code class="docutils literal notranslate"><span class="pre">p+7</span></code>), zero if big-endian
(exponent first, at <em>p</em>). The <code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_BIG_ENDIAN</span></code> constant can be used to
use the native endian: it is equal to <code class="docutils literal notranslate"><span class="pre">1</span></code> on big endian processor, or <code class="docutils literal notranslate"><span class="pre">0</span></code>
on little endian processor.</p>
<p>Return value: The unpacked double.  On error, this is <code class="docutils literal notranslate"><span class="pre">-1.0</span></code> and
<a class="reference internal" href="exceptions.html#c.PyErr_Occurred" title="PyErr_Occurred"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Occurred()</span></code></a> is true (and an exception is set, most likely
<a class="reference internal" href="../library/exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a>).</p>
<p>Note that on a non-IEEE platform this will refuse to unpack a bytes string that
represents a NaN or infinity.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_Unpack2">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_Unpack2</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">le</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_Unpack2" title="Link to this definition">¶</a><br /></dt>
<dd><p>Unpack the IEEE 754 binary16 half-precision format as a C double.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_Unpack4">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_Unpack4</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">le</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_Unpack4" title="Link to this definition">¶</a><br /></dt>
<dd><p>Unpack the IEEE 754 binary32 single precision format as a C double.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFloat_Unpack8">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFloat_Unpack8</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">le</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFloat_Unpack8" title="Link to this definition">¶</a><br /></dt>
<dd><p>Unpack the IEEE 754 binary64 double precision format as a C double.</p>
</dd></dl>

</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Floating-Point Objects</a><ul>
<li><a class="reference internal" href="#pack-and-unpack-functions">Pack and Unpack functions</a><ul>
<li><a class="reference internal" href="#pack-functions">Pack functions</a></li>
<li><a class="reference internal" href="#unpack-functions">Unpack functions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="bool.html"
                          title="previous chapter">Boolean Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="complex.html"
                          title="next chapter">Complex Number Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/float.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="complex.html" title="Complex Number Objects"
             >next</a> |</li>
        <li class="right" >
          <a href="bool.html" title="Boolean Objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" >Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Floating-Point Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>