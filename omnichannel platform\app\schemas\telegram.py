from typing import Optional, List, Dict, Any, TypeVar, Generic
from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field

from app.models.telegram import TelegramAccountStatus

T = TypeVar('T')


class TelegramAccountBase(BaseModel):
    """Base schema for Telegram account data."""
    account_name: str
    bot_token: str
    username: Optional[str] = None
    status: Optional[TelegramAccountStatus] = TelegramAccountStatus.PENDING
    metadata_: Dict[str, Any] = Field(default_factory=dict)


class TelegramAccountCreate(TelegramAccountBase):
    """Schema for creating a new Telegram account."""
    pass


class TelegramAccountRead(TelegramAccountBase):
    """Schema for reading Telegram account data."""
    id: int
    user_id: int
    is_verified: bool
    verified_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TelegramAccountUpdate(BaseModel):
    """Schema for updating an existing Telegram account."""
    account_name: Optional[str] = None
    bot_token: Optional[str] = None
    username: Optional[str] = None
    status: Optional[TelegramAccountStatus] = None
    metadata_: Optional[Dict[str, Any]] = None


class ListResponse(BaseModel, Generic[T]):
    data: List[T]
    total: int
