import React, { useState } from 'react';
import { Box, Heading, VStack, FormControl, FormLabel, Input, Button, useToast, SimpleGrid, Card, Text, IconButton } from '@chakra-ui/react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FaTrash, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';
import axios from 'axios';

interface TelegramAccount {
  id: number;
  account_name: string;
  bot_token: string;
  username?: string;
  status: string;
  is_verified: boolean;
}

const TelegramSettings: React.FC = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const [accountName, setAccountName] = useState('');
  const [botToken, setBotToken] = useState('');

  const { data: accounts, isLoading, error } = useQuery<TelegramAccount[]>({ queryKey: ['telegramAccounts'], queryFn: async () => {
    const response = await axios.get('/api/telegram/accounts');
    return response.data.data; // Assuming the API returns { data: [...], total: ... }
  }});

  const createAccountMutation = useMutation({
    mutationFn: async (newAccount: { account_name: string; bot_token: string }) => {
      const response = await axios.post('/api/telegram/accounts/', newAccount);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['telegramAccounts'] });
      setAccountName('');
      setBotToken('');
      toast({
        title: 'Account created.',
        description: "Telegram account added successfully.",
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    },
    onError: (err: any) => {
      toast({
        title: 'Error creating account.',
        description: err.response?.data?.detail || "An error occurred.",
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const deleteAccountMutation = useMutation({
    mutationFn: async (accountId: number) => {
      await axios.delete(`/api/telegram/accounts/${accountId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['telegramAccounts'] });
      toast({
        title: 'Account deleted.',
        description: "Telegram account deleted successfully.",
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    },
    onError: (err: any) => {
      toast({
        title: 'Error deleting account.',
        description: err.response?.data?.detail || "An error occurred.",
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const handleCreateAccount = () => {
    if (accountName && botToken) {
      createAccountMutation.mutate({ account_name: accountName, bot_token: botToken });
    }
  };

  const handleDeleteAccount = (accountId: number) => {
    deleteAccountMutation.mutate(accountId);
  };

  return (
    <Box p={4}>
      <Heading as="h2" size="xl" mb={6}>Telegram Settings</Heading>

      <VStack spacing={8} align="stretch">
        <Box>
          <Heading as="h3" size="lg" mb={4}>Add New Telegram Account</Heading>
          <FormControl id="accountName" mb={4}>
            <FormLabel>Account Name</FormLabel>
            <Input
              value={accountName}
              onChange={(e) => setAccountName(e.target.value)}
              placeholder="e.g., My Telegram Bot"
            />
          </FormControl>
          <FormControl id="botToken" mb={4}>
            <FormLabel>Bot Token</FormLabel>
            <Input
              value={botToken}
              onChange={(e) => setBotToken(e.target.value)}
              placeholder="Enter your Telegram Bot Token"
            />
          </FormControl>
          <Button
            colorScheme="blue"
            onClick={handleCreateAccount}
            isLoading={createAccountMutation.isPending}
          >
            Add Account
          </Button>
        </Box>

        <Box>
          <Heading as="h3" size="lg" mb={4}>Existing Telegram Accounts</Heading>
          {isLoading ? (
            <Text>Loading accounts...</Text>
          ) : error ? (
            <Text color="red.500">Error loading accounts: {error.message}</Text>
          ) : accounts && accounts.length > 0 ? (
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              {accounts.map((account) => (
                <Card key={account.id} p={4} borderWidth="1px" borderRadius="lg">
                  <VStack align="start">
                    <Heading size="md">{account.account_name}</Heading>
                    <Text>Username: {account.username || 'N/A'}</Text>
                    <Text>Status: {account.status}</Text>
                    <Text>
                      Verified: {' '}
                      {account.is_verified ? (
                        <FaCheckCircle color="green.500" />
                      ) : (
                        <FaExclamationTriangle color="orange.500" />
                      )}
                    </Text>
                    <IconButton
                      aria-label="Delete account"
                      icon={<FaTrash />}
                      colorScheme="red"
                      onClick={() => handleDeleteAccount(account.id)}
                      isLoading={deleteAccountMutation.isPending}
                    />
                  </VStack>
                </Card>
              ))}
            </SimpleGrid>
          ) : (
            <Text>No Telegram accounts added yet.</Text>
          )}
        </Box>
      </VStack>
    </Box>
  );
};

export default TelegramSettings;
