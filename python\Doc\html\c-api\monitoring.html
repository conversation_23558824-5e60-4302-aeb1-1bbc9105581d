<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Monitoring C API" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/monitoring.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Added in version 3.13. An extension may need to interact with the event monitoring system. Subscribing to events and registering callbacks can be done via the Python API exposed in sys.monitoring. ..." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Added in version 3.13. An extension may need to interact with the event monitoring system. Subscribing to events and registering callbacks can be done via the Python API exposed in sys.monitoring. ..." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Monitoring C API &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Installing Python Modules" href="../installing/index.html" />
    <link rel="prev" title="API and ABI Versioning" href="apiabiversion.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/monitoring.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Monitoring C API</a></li>
<li><a class="reference internal" href="#generating-execution-events">Generating Execution Events</a><ul>
<li><a class="reference internal" href="#managing-the-monitoring-state">Managing the Monitoring State</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="apiabiversion.html"
                          title="previous chapter">API and ABI Versioning</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../installing/index.html"
                          title="next chapter">Installing Python Modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/monitoring.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="../installing/index.html" title="Installing Python Modules"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="apiabiversion.html" title="API and ABI Versioning"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Monitoring C API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="monitoring-c-api">
<span id="c-api-monitoring"></span><h1>Monitoring C API<a class="headerlink" href="#monitoring-c-api" title="Link to this heading">¶</a></h1>
<p>Added in version 3.13.</p>
<p>An extension may need to interact with the event monitoring system. Subscribing
to events and registering callbacks can be done via the Python API exposed in
<a class="reference internal" href="../library/sys.monitoring.html#module-sys.monitoring" title="sys.monitoring: Access and control event monitoring"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code></a>.</p>
</section>
<section id="generating-execution-events">
<h1>Generating Execution Events<a class="headerlink" href="#generating-execution-events" title="Link to this heading">¶</a></h1>
<p>The functions below make it possible for an extension to fire monitoring
events as it emulates the execution of Python code. Each of these functions
accepts a <code class="docutils literal notranslate"><span class="pre">PyMonitoringState</span></code> struct which contains concise information
about the activation state of events, as well as the event arguments, which
include a <code class="docutils literal notranslate"><span class="pre">PyObject*</span></code> representing the code object, the instruction offset
and sometimes additional, event-specific arguments (see <a class="reference internal" href="../library/sys.monitoring.html#module-sys.monitoring" title="sys.monitoring: Access and control event monitoring"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code></a>
for details about the signatures of the different event callbacks).
The <code class="docutils literal notranslate"><span class="pre">codelike</span></code> argument should be an instance of <a class="reference internal" href="../library/types.html#types.CodeType" title="types.CodeType"><code class="xref py py-class docutils literal notranslate"><span class="pre">types.CodeType</span></code></a>
or of a type that emulates it.</p>
<p>The VM disables tracing when firing an event, so there is no need for user
code to do that.</p>
<p>Monitoring functions should not be called with an exception set,
except those listed below as working with the current exception.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyMonitoringState">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoringState</span></span></span><a class="headerlink" href="#c.PyMonitoringState" title="Link to this definition">¶</a><br /></dt>
<dd><p>Representation of the state of an event type. It is allocated by the user
while its contents are maintained by the monitoring API functions described below.</p>
</dd></dl>

<p>All of the functions below return 0 on success and -1 (with an exception set) on error.</p>
<p>See <a class="reference internal" href="../library/sys.monitoring.html#module-sys.monitoring" title="sys.monitoring: Access and control event monitoring"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code></a> for descriptions of the events.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FirePyStartEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FirePyStartEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FirePyStartEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">PY_START</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FirePyResumeEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FirePyResumeEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FirePyResumeEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">PY_RESUME</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FirePyReturnEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FirePyReturnEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">retval</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FirePyReturnEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">PY_RETURN</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FirePyYieldEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FirePyYieldEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">retval</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FirePyYieldEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">PY_YIELD</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireCallEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireCallEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">arg0</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireCallEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">CALL</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireLineEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireLineEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">lineno</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireLineEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">LINE</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireJumpEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireJumpEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">target_offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireJumpEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">JUMP</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireBranchEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireBranchEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">target_offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireBranchEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">BRANCH</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireCReturnEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireCReturnEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">retval</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireCReturnEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">C_RETURN</span></code> event.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FirePyThrowEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FirePyThrowEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FirePyThrowEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">PY_THROW</span></code> event with the current exception (as returned by
<a class="reference internal" href="exceptions.html#c.PyErr_GetRaisedException" title="PyErr_GetRaisedException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetRaisedException()</span></code></a>).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireRaiseEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireRaiseEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireRaiseEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">RAISE</span></code> event with the current exception (as returned by
<a class="reference internal" href="exceptions.html#c.PyErr_GetRaisedException" title="PyErr_GetRaisedException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetRaisedException()</span></code></a>).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireCRaiseEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireCRaiseEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireCRaiseEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">C_RAISE</span></code> event with the current exception (as returned by
<a class="reference internal" href="exceptions.html#c.PyErr_GetRaisedException" title="PyErr_GetRaisedException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetRaisedException()</span></code></a>).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireReraiseEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireReraiseEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireReraiseEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">RERAISE</span></code> event with the current exception (as returned by
<a class="reference internal" href="exceptions.html#c.PyErr_GetRaisedException" title="PyErr_GetRaisedException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetRaisedException()</span></code></a>).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireExceptionHandledEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireExceptionHandledEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireExceptionHandledEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire an <code class="docutils literal notranslate"><span class="pre">EXCEPTION_HANDLED</span></code> event with the current exception (as returned by
<a class="reference internal" href="exceptions.html#c.PyErr_GetRaisedException" title="PyErr_GetRaisedException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetRaisedException()</span></code></a>).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FirePyUnwindEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FirePyUnwindEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FirePyUnwindEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">PY_UNWIND</span></code> event with the current exception (as returned by
<a class="reference internal" href="exceptions.html#c.PyErr_GetRaisedException" title="PyErr_GetRaisedException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetRaisedException()</span></code></a>).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_FireStopIterationEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_FireStopIterationEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">codelike</span></span>, <span class="n"><span class="pre">int32_t</span></span><span class="w"> </span><span class="n"><span class="pre">offset</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_FireStopIterationEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fire a <code class="docutils literal notranslate"><span class="pre">STOP_ITERATION</span></code> event. If <code class="docutils literal notranslate"><span class="pre">value</span></code> is an instance of <a class="reference internal" href="../library/exceptions.html#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a>, it is used. Otherwise,
a new <a class="reference internal" href="../library/exceptions.html#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a> instance is created with <code class="docutils literal notranslate"><span class="pre">value</span></code> as its argument.</p>
</dd></dl>

<section id="managing-the-monitoring-state">
<h2>Managing the Monitoring State<a class="headerlink" href="#managing-the-monitoring-state" title="Link to this heading">¶</a></h2>
<p>Monitoring states can be managed with the help of monitoring scopes. A scope
would typically correspond to a python function.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_EnterScope">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_EnterScope</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMonitoringState" title="PyMonitoringState"><span class="n"><span class="pre">PyMonitoringState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">state_array</span></span>, <span class="n"><span class="pre">uint64_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">version</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">uint8_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">event_types</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">length</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_EnterScope" title="Link to this definition">¶</a><br /></dt>
<dd><p>Enter a monitored scope. <code class="docutils literal notranslate"><span class="pre">event_types</span></code> is an array of the event IDs for
events that may be fired from the scope. For example, the ID of a <code class="docutils literal notranslate"><span class="pre">PY_START</span></code>
event is the value <code class="docutils literal notranslate"><span class="pre">PY_MONITORING_EVENT_PY_START</span></code>, which is numerically equal
to the base-2 logarithm of <code class="docutils literal notranslate"><span class="pre">sys.monitoring.events.PY_START</span></code>.
<code class="docutils literal notranslate"><span class="pre">state_array</span></code> is an array with a monitoring state entry for each event in
<code class="docutils literal notranslate"><span class="pre">event_types</span></code>, it is allocated by the user but populated by
<code class="xref c c-func docutils literal notranslate"><span class="pre">PyMonitoring_EnterScope()</span></code> with information about the activation state of
the event. The size of <code class="docutils literal notranslate"><span class="pre">event_types</span></code> (and hence also of <code class="docutils literal notranslate"><span class="pre">state_array</span></code>)
is given in <code class="docutils literal notranslate"><span class="pre">length</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">version</span></code> argument is a pointer to a value which should be allocated
by the user together with <code class="docutils literal notranslate"><span class="pre">state_array</span></code> and initialized to 0,
and then set only by <code class="xref c c-func docutils literal notranslate"><span class="pre">PyMonitoring_EnterScope()</span></code> itself. It allows this
function to determine whether event states have changed since the previous call,
and to return quickly if they have not.</p>
<p>The scopes referred to here are lexical scopes: a function, class or method.
<code class="xref c c-func docutils literal notranslate"><span class="pre">PyMonitoring_EnterScope()</span></code> should be called whenever the lexical scope is
entered. Scopes can be reentered, reusing the same <em>state_array</em> and <em>version</em>,
in situations like when emulating a recursive Python function. When a code-like’s
execution is paused, such as when emulating a generator, the scope needs to
be exited and re-entered.</p>
<p>The macros for <em>event_types</em> are:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Macro</p></th>
<th class="head"><p>Event</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_BRANCH">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_BRANCH</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_BRANCH" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-BRANCH"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">BRANCH</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_CALL">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_CALL</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_CALL" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-CALL"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">CALL</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_C_RAISE">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_C_RAISE</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_C_RAISE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-C_RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RAISE</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_C_RETURN">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_C_RETURN</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_C_RETURN" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-C_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">C_RETURN</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_EXCEPTION_HANDLED">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_EXCEPTION_HANDLED</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_EXCEPTION_HANDLED" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-EXCEPTION_HANDLED"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">EXCEPTION_HANDLED</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_INSTRUCTION">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_INSTRUCTION</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_INSTRUCTION" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-INSTRUCTION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">INSTRUCTION</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_JUMP">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_JUMP</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_JUMP" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-JUMP"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">JUMP</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_LINE">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_LINE</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_LINE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-LINE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">LINE</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_PY_RESUME">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_PY_RESUME</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_PY_RESUME" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-PY_RESUME"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_RESUME</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_PY_RETURN">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_PY_RETURN</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_PY_RETURN" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-PY_RETURN"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_RETURN</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_PY_START">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_PY_START</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_PY_START" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-PY_START"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_START</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_PY_THROW">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_PY_THROW</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_PY_THROW" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-PY_THROW"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_THROW</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_PY_UNWIND">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_PY_UNWIND</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_PY_UNWIND" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-PY_UNWIND"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_UNWIND</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_PY_YIELD">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_PY_YIELD</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_PY_YIELD" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-PY_YIELD"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">PY_YIELD</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_RAISE">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_RAISE</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_RAISE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-RAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">RAISE</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_RERAISE">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_RERAISE</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_RERAISE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-RERAISE"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">RERAISE</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MONITORING_EVENT_STOP_ITERATION">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_EVENT_STOP_ITERATION</span></span></span><a class="headerlink" href="#c.PY_MONITORING_EVENT_STOP_ITERATION" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-STOP_ITERATION"><code class="xref std std-monitoring-event docutils literal notranslate"><span class="pre">STOP_ITERATION</span></code></a></p></td>
</tr>
</tbody>
</table>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMonitoring_ExitScope">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMonitoring_ExitScope</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMonitoring_ExitScope" title="Link to this definition">¶</a><br /></dt>
<dd><p>Exit the last scope that was entered with <code class="xref c c-func docutils literal notranslate"><span class="pre">PyMonitoring_EnterScope()</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PY_MONITORING_IS_INSTRUMENTED_EVENT">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PY_MONITORING_IS_INSTRUMENTED_EVENT</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">uint8_t</span></span><span class="w"> </span><span class="n"><span class="pre">ev</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PY_MONITORING_IS_INSTRUMENTED_EVENT" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if the event corresponding to the event ID <em>ev</em> is
a <a class="reference internal" href="../library/sys.monitoring.html#monitoring-event-local"><span class="std std-ref">local event</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.13.3: </span>This function is <a class="reference internal" href="../glossary.html#term-soft-deprecated"><span class="xref std std-term">soft deprecated</span></a>.</p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Monitoring C API</a></li>
<li><a class="reference internal" href="#generating-execution-events">Generating Execution Events</a><ul>
<li><a class="reference internal" href="#managing-the-monitoring-state">Managing the Monitoring State</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="apiabiversion.html"
                          title="previous chapter">API and ABI Versioning</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../installing/index.html"
                          title="next chapter">Installing Python Modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/monitoring.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="../installing/index.html" title="Installing Python Modules"
             >next</a> |</li>
        <li class="right" >
          <a href="apiabiversion.html" title="API and ABI Versioning"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Monitoring C API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>