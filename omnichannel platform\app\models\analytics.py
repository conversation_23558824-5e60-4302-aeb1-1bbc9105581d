"""Analytics and reporting models."""

from datetime import datetime
from typing import Optional, Dict, Any, List, TYPE_CHECKING

from sqlmodel import Field, SQLModel, Relationship, Column, JSON
from sqlalchemy import String, Integer, ForeignKey, DateTime, Float, Boolean, func, Text, Column

from .base import BaseModel

if TYPE_CHECKING:
    from .whatsapp import Campaign, WhatsAppAccount
    from .omnichannel import OmnichannelMessage
    from .user import User
    from .analytics import MessageAnalytics


class MessageAnalytics(BaseModel, table=True):
    """Message analytics and metrics."""
    __tablename__ = "message_analytics"
    
    message_id: int = Field(
        sa_column=Column(Integer, ForeignKey("omnichannel_messages.id", ondelete="CASCADE"), nullable=False),
        description="Reference to the message"
    )
    account_id: int = Field(
        sa_column=Column(Integer, ForeignKey("whatsapp_accounts.id", ondelete="CASCADE"), nullable=False),
        description="Reference to the WhatsApp account used"
    )
    user_id: int = Field(
        sa_column=Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False),
        description="Reference to the user who sent the message"
    )
    
    # Delivery metrics
    delivery_status: str = Field(
        default="pending",
        sa_column=Column(String(20), default="pending", nullable=False),
        description="Current delivery status (pending, sent, delivered, failed, etc.)"
    )
    sent_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=True),
        description="When the message was sent"
    )
    delivered_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=True),
        description="When the message was delivered"
    )
    
    # Read metrics
    read_status: bool = Field(
        default=False,
        sa_column=Column(Boolean, default=False, nullable=False),
        description="Whether the message has been read"
    )
    read_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), nullable=True),
        description="When the message was read"
    )
    
    # Response metrics
    response_received: bool = Field(
        default=False,
        sa_column=Column(Boolean, default=False, nullable=False),
        description="Whether a response was received"
    )
    response_message_id: Optional[int] = Field(
        default=None,
        sa_column=Column(Integer, ForeignKey("omnichannel_messages.id"), nullable=True),
        description="Reference to the response message if any"
    )
    
    # Timing metrics (in seconds)
    delivery_time: Optional[float] = Field(
        default=None,
        sa_column=Column(Float, nullable=True),
        description="Time taken to deliver the message in seconds"
    )
    read_time: Optional[float] = Field(
        default=None,
        sa_column=Column(Float, nullable=True),
        description="Time taken for the message to be read in seconds"
    )
    response_time: Optional[float] = Field(
        default=None,
        sa_column=Column(Float, nullable=True),
        description="Time taken to receive a response in seconds"
    )
    
    # Engagement metrics
    link_clicks: int = Field(
        default=0,
        sa_column=Column(Integer, default=0, nullable=False),
        description="Number of links clicked in the message"
    )
    button_clicks: Dict[str, int] = Field(
        default_factory=dict,
        sa_column=Column(JSON, nullable=True),
        description="Count of button clicks by button ID"
    )
    
    # Error tracking
    error_message: Optional[str] = Field(
        default=None,
        sa_column=Column(Text, nullable=True),
        description="Error message if delivery failed"
    )
    
    # Relationships
    message: "OmnichannelMessage" = Relationship(back_populates="analytics", sa_relationship_kwargs={"foreign_keys": lambda: [MessageAnalytics.message_id]})
    account: "WhatsAppAccount" = Relationship(back_populates="message_analytics")
    user: "User" = Relationship(back_populates="message_analytics")
    
    @property
    def is_delivered(self) -> bool:
        """Check if the message was successfully delivered."""
        return self.delivery_status == "delivered"
    
    @property
    def has_failed(self) -> bool:
        """Check if the message delivery failed."""
        return self.delivery_status == "failed"
    
    def mark_as_sent(self) -> None:
        """Mark the message as sent."""
        self.delivery_status = "sent"
        self.sent_at = datetime.utcnow()
    
    def mark_as_delivered(self) -> None:
        """Mark the message as delivered and calculate delivery time."""
        self.delivery_status = "delivered"
        self.delivered_at = datetime.utcnow()
        if self.sent_at:
            self.delivery_time = (self.delivered_at - self.sent_at).total_seconds()
    
    def mark_as_read(self) -> None:
        """Mark the message as read and calculate read time."""
        self.read_status = True
        self.read_at = datetime.utcnow()
        if self.delivered_at:
            self.read_time = (self.read_at - self.delivered_at).total_seconds()
    
    def record_response(self, response_message_id: int) -> None:
        """Record a response to this message.
        
        Args:
            response_message_id: ID of the response message
        """
        self.response_received = True
        self.response_message_id = response_message_id
        if self.sent_at:
            self.response_time = (datetime.utcnow() - self.sent_at).total_seconds()
    
    # Additional analytics data
    analytics_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class CampaignAnalytics(SQLModel, table=True):
    """Campaign analytics and performance metrics."""
    __tablename__ = "campaign_analytics"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    campaign_id: int = Field(sa_column=Column(Integer, ForeignKey("campaigns.id"), nullable=False))
    
    # Campaign metrics
    total_recipients: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    messages_sent: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    messages_delivered: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    messages_read: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    responses_received: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    
    # Performance metrics
    delivery_rate: float = Field(default=0.0, sa_column=Column(Float, default=0.0, nullable=False))
    read_rate: float = Field(default=0.0, sa_column=Column(Float, default=0.0, nullable=False))
    response_rate: float = Field(default=0.0, sa_column=Column(Float, default=0.0, nullable=False))
    
    # Timing metrics
    average_delivery_time: Optional[float] = Field(default=None, sa_column=Column(Float, nullable=True))
    average_read_time: Optional[float] = Field(default=None, sa_column=Column(Float, nullable=True))
    average_response_time: Optional[float] = Field(default=None, sa_column=Column(Float, nullable=True))
    
    # Cost metrics
    total_cost: Optional[float] = Field(default=None, sa_column=Column(Float, nullable=True))
    cost_per_message: Optional[float] = Field(default=None, sa_column=Column(Float, nullable=True))
    
    # Additional analytics
    analytics_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel
