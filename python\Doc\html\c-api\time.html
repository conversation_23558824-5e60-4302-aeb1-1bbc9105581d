<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="PyTime C API" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/time.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The clock C API provides access to system clocks. It is similar to the Python time module. For C API related to the datetime module, see DateTime Objects. Types: Clock Functions: The following func..." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The clock C API provides access to system clocks. It is similar to the Python time module. For C API related to the datetime module, see DateTime Objects. Types: Clock Functions: The following func..." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>PyTime C API &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Support for Perf Maps" href="perfmaps.html" />
    <link rel="prev" title="Codec registry and support functions" href="codec.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/time.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">PyTime C API</a><ul>
<li><a class="reference internal" href="#types">Types</a></li>
<li><a class="reference internal" href="#clock-functions">Clock Functions</a></li>
<li><a class="reference internal" href="#raw-clock-functions">Raw Clock Functions</a></li>
<li><a class="reference internal" href="#conversion-functions">Conversion functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="codec.html"
                          title="previous chapter">Codec registry and support functions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="perfmaps.html"
                          title="next chapter">Support for Perf Maps</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/time.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="perfmaps.html" title="Support for Perf Maps"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="codec.html" title="Codec registry and support functions"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="utilities.html" accesskey="U">Utilities</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">PyTime C API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="pytime-c-api">
<span id="c-api-time"></span><h1>PyTime C API<a class="headerlink" href="#pytime-c-api" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.13.</span></p>
</div>
<p>The clock C API provides access to system clocks.
It is similar to the Python <a class="reference internal" href="../library/time.html#module-time" title="time: Time access and conversions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code></a> module.</p>
<p>For C API related to the <a class="reference internal" href="../library/datetime.html#module-datetime" title="datetime: Basic date and time types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">datetime</span></code></a> module, see <a class="reference internal" href="datetime.html#datetimeobjects"><span class="std std-ref">DateTime Objects</span></a>.</p>
<section id="types">
<h2>Types<a class="headerlink" href="#types" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyTime_t">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_t</span></span></span><a class="headerlink" href="#c.PyTime_t" title="Link to this definition">¶</a><br /></dt>
<dd><p>A timestamp or duration in nanoseconds, represented as a signed 64-bit
integer.</p>
<p>The reference point for timestamps depends on the clock used. For example,
<a class="reference internal" href="#c.PyTime_Time" title="PyTime_Time"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTime_Time()</span></code></a> returns timestamps relative to the UNIX epoch.</p>
<p>The supported range is around [-292.3 years; +292.3 years].
Using the Unix epoch (January 1st, 1970) as reference, the supported date
range is around [1677-09-21; 2262-04-11].
The exact limits are exposed as constants:</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTime_MIN">
<a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_MIN</span></span></span><a class="headerlink" href="#c.PyTime_MIN" title="Link to this definition">¶</a><br /></dt>
<dd><p>Minimum value of <a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTime_t</span></code></a>.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTime_MAX">
<a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_MAX</span></span></span><a class="headerlink" href="#c.PyTime_MAX" title="Link to this definition">¶</a><br /></dt>
<dd><p>Maximum value of <a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTime_t</span></code></a>.</p>
</dd></dl>

</section>
<section id="clock-functions">
<h2>Clock Functions<a class="headerlink" href="#clock-functions" title="Link to this heading">¶</a></h2>
<p>The following functions take a pointer to a <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n">PyTime_t</span></a></span> that they
set to the value of a particular clock.
Details of each clock are given in the documentation of the corresponding
Python function.</p>
<p>The functions return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, or <code class="docutils literal notranslate"><span class="pre">-1</span></code> (with an exception set)
on failure.</p>
<p>On integer overflow, they set the <code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_OverflowError</span></code> exception and
set <code class="docutils literal notranslate"><span class="pre">*result</span></code> to the value clamped to the <code class="docutils literal notranslate"><span class="pre">[PyTime_MIN;</span> <span class="pre">PyTime_MAX]</span></code>
range.
(On current systems, integer overflows are likely caused by misconfigured
system time.)</p>
<p>As any other C API (unless otherwise specified), the functions must be called
with the <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a> held.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyTime_Monotonic">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_Monotonic</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">result</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyTime_Monotonic" title="Link to this definition">¶</a><br /></dt>
<dd><p>Read the monotonic clock.
See <a class="reference internal" href="../library/time.html#time.monotonic" title="time.monotonic"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.monotonic()</span></code></a> for important details on this clock.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyTime_PerfCounter">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_PerfCounter</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">result</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyTime_PerfCounter" title="Link to this definition">¶</a><br /></dt>
<dd><p>Read the performance counter.
See <a class="reference internal" href="../library/time.html#time.perf_counter" title="time.perf_counter"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.perf_counter()</span></code></a> for important details on this clock.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyTime_Time">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_Time</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">result</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyTime_Time" title="Link to this definition">¶</a><br /></dt>
<dd><p>Read the “wall clock” time.
See <a class="reference internal" href="../library/time.html#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.time()</span></code></a> for details important on this clock.</p>
</dd></dl>

</section>
<section id="raw-clock-functions">
<h2>Raw Clock Functions<a class="headerlink" href="#raw-clock-functions" title="Link to this heading">¶</a></h2>
<p>Similar to clock functions, but don’t set an exception on error and don’t
require the caller to hold the GIL.</p>
<p>On success, the functions return <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<p>On failure, they set <code class="docutils literal notranslate"><span class="pre">*result</span></code> to <code class="docutils literal notranslate"><span class="pre">0</span></code> and return <code class="docutils literal notranslate"><span class="pre">-1</span></code>, <em>without</em> setting
an exception. To get the cause of the error, acquire the GIL and call the
regular (non-<code class="docutils literal notranslate"><span class="pre">Raw</span></code>) function. Note that the regular function may succeed after
the <code class="docutils literal notranslate"><span class="pre">Raw</span></code> one failed.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyTime_MonotonicRaw">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_MonotonicRaw</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">result</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyTime_MonotonicRaw" title="Link to this definition">¶</a><br /></dt>
<dd><p>Similar to <a class="reference internal" href="#c.PyTime_Monotonic" title="PyTime_Monotonic"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTime_Monotonic()</span></code></a>,
but don’t set an exception on error and don’t require holding the GIL.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyTime_PerfCounterRaw">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_PerfCounterRaw</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">result</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyTime_PerfCounterRaw" title="Link to this definition">¶</a><br /></dt>
<dd><p>Similar to <a class="reference internal" href="#c.PyTime_PerfCounter" title="PyTime_PerfCounter"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTime_PerfCounter()</span></code></a>,
but don’t set an exception on error and don’t require holding the GIL.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyTime_TimeRaw">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_TimeRaw</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">result</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyTime_TimeRaw" title="Link to this definition">¶</a><br /></dt>
<dd><p>Similar to <a class="reference internal" href="#c.PyTime_Time" title="PyTime_Time"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTime_Time()</span></code></a>,
but don’t set an exception on error and don’t require holding the GIL.</p>
</dd></dl>

</section>
<section id="conversion-functions">
<h2>Conversion functions<a class="headerlink" href="#conversion-functions" title="Link to this heading">¶</a></h2>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyTime_AsSecondsDouble">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTime_AsSecondsDouble</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyTime_t" title="PyTime_t"><span class="n"><span class="pre">PyTime_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">t</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyTime_AsSecondsDouble" title="Link to this definition">¶</a><br /></dt>
<dd><p>Convert a timestamp to a number of seconds as a C <span class="c-expr sig sig-inline c"><span class="kt">double</span></span>.</p>
<p>The function cannot fail, but note that <span class="c-expr sig sig-inline c"><span class="kt">double</span></span> has limited
accuracy for large values.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">PyTime C API</a><ul>
<li><a class="reference internal" href="#types">Types</a></li>
<li><a class="reference internal" href="#clock-functions">Clock Functions</a></li>
<li><a class="reference internal" href="#raw-clock-functions">Raw Clock Functions</a></li>
<li><a class="reference internal" href="#conversion-functions">Conversion functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="codec.html"
                          title="previous chapter">Codec registry and support functions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="perfmaps.html"
                          title="next chapter">Support for Perf Maps</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/time.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="perfmaps.html" title="Support for Perf Maps"
             >next</a> |</li>
        <li class="right" >
          <a href="codec.html" title="Codec registry and support functions"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="utilities.html" >Utilities</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">PyTime C API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>