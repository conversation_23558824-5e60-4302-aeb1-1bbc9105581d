from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session
from typing import Optional
from app.models.omnichannel import ChannelAccount
from app.db.session import get_db
from app.schemas.integration import IntegrationRead
from app.api.dependencies import get_current_user
from app.models.user import User

router = APIRouter(prefix="/integrations/onboarding", tags=["onboarding"])

@router.post("/start", response_model=dict)
def start_onboarding(channel_type: str, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if current_user.role not in ("admin", "super_admin"):
        raise HTTPException(status_code=403, detail="Only admin or super_admin can onboard channels.")
    # Placeholder: return OAuth URL or instructions for the channel provider
    if channel_type == "whatsapp_cloud":
        return {"oauth_url": "https://facebook.com/oauth?..."}
    elif channel_type == "telegram":
        return {"instructions": "Please provide your Telegram bot token to complete the setup."}
    return {"instructions": f"Onboarding for {channel_type} not implemented yet."}

@router.post("/complete", response_model=IntegrationRead)
def complete_onboarding(channel_type: str, code: Optional[str] = None, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if current_user.role not in ("admin", "super_admin"):
        raise HTTPException(status_code=403, detail="Only admin or super_admin can complete onboarding.")
    # Placeholder: exchange code/token and save config
    integration = ChannelAccount(
        name=f"{channel_type} Integration",
        channel_type=channel_type,
        config={"token": code or "dummy-token"},
        is_active=True,
        company_id=current_user.company_id
    )
    db.add(integration)
    db.commit()
    db.refresh(integration)
    return integration

@router.get("/{integration_id}/status", response_model=dict)
def check_status(integration_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    integration = db.get(ChannelAccount, integration_id)
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    if current_user.role != "super_admin" and integration.company_id != current_user.company_id:
        raise HTTPException(status_code=403, detail="Forbidden")
    # Placeholder: check connection/health
    return {"status": "connected" if integration.is_active else "disconnected"}

@router.delete("/{integration_id}", status_code=status.HTTP_204_NO_CONTENT)
def disconnect_integration(integration_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    integration = db.get(ChannelAccount, integration_id)
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    if current_user.role != "super_admin" and integration.company_id != current_user.company_id:
        raise HTTPException(status_code=403, detail="Forbidden")
    db.delete(integration)
    db.commit()
    return None
