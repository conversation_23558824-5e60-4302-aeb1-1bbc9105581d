<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Omnichannel Platform - Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: #dc3545;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
        .success {
            color: #155724;
            margin-top: 10px;
            padding: 10px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
        }
        .test-credentials {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-credentials h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .test-credentials p {
            margin: 5px 0;
            font-size: 14px;
        }
        .user-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .button-group button {
            flex: 1;
        }
        .check-btn {
            background-color: #28a745;
        }
        .check-btn:hover {
            background-color: #218838;
        }
        .logout-btn {
            background-color: #dc3545;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Omnichannel Platform</h1>

        <div class="status">
            <strong>Backend Status:</strong> <span id="backendStatus">Checking...</span>
        </div>

        <div class="test-credentials">
            <h3>Test Credentials:</h3>
            <p><strong>Super Admin:</strong> <EMAIL> / admin123</p>
            <p><strong>Test User:</strong> <EMAIL> / test123</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" id="loginBtn">Login</button>
        </form>

        <div id="message"></div>
        <div id="userInfo" class="user-info" style="display: none;"></div>

        <div class="button-group">
            <button id="checkAuthBtn" class="check-btn">Check Auth</button>
            <button id="logoutBtn" class="logout-btn">Logout</button>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <a href="http://localhost:8000/docs" target="_blank" style="color: #007bff;">
                📚 Open API Documentation
            </a>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/api';

        const loginForm = document.getElementById("loginForm");
        const loginBtn = document.getElementById("loginBtn");
        const checkAuthBtn = document.getElementById("checkAuthBtn");
        const logoutBtn = document.getElementById("logoutBtn");
        const messageDiv = document.getElementById("message");
        const userInfoDiv = document.getElementById("userInfo");
        const backendStatusSpan = document.getElementById("backendStatus");

        function showMessage(message, isError = false) {
            messageDiv.innerHTML = `<div class="${isError ? "error" : "success"}">${message}</div>`;
        }

        function showUserInfo(user) {
            userInfoDiv.innerHTML = `
                <h3>✅ Logged in as:</h3>
                <p><strong>Name:</strong> ${user.first_name} ${user.last_name}</p>
                <p><strong>Email:</strong> ${user.email}</p>
                <p><strong>Role:</strong> ${user.role}</p>
                <p><strong>Company ID:</strong> ${user.company_id}</p>
            `;
            userInfoDiv.style.display = "block";
        }

        function hideUserInfo() {
            userInfoDiv.style.display = "none";
        }

        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    backendStatusSpan.textContent = "✅ Online";
                    backendStatusSpan.style.color = "#28a745";
                } else {
                    backendStatusSpan.textContent = "❌ Error";
                    backendStatusSpan.style.color = "#dc3545";
                }
            } catch (error) {
                backendStatusSpan.textContent = "❌ Offline";
                backendStatusSpan.style.color = "#dc3545";
            }
        }

        loginForm.addEventListener("submit", async (e) => {
            e.preventDefault();

            const email = document.getElementById("email").value;
            const password = document.getElementById("password").value;

            loginBtn.disabled = true;
            loginBtn.textContent = "Logging in...";

            try {
                const response = await fetch(`${API_BASE}/auth/token`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    credentials: "include",
                    body: JSON.stringify({ email, password }),
                });

                if (response.ok) {
                    const data = await response.json();
                    showMessage("✅ Login successful!");
                    showUserInfo(data.user);
                    loginForm.reset();
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    showMessage(`❌ ${errorData.detail || "Login failed"}`, true);
                    hideUserInfo();
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, true);
                hideUserInfo();
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = "Login";
            }
        });

        checkAuthBtn.addEventListener("click", async () => {
            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    credentials: "include",
                });

                if (response.ok) {
                    const user = await response.json();
                    showMessage("✅ Authentication valid!");
                    showUserInfo(user);
                } else {
                    showMessage("❌ Not authenticated", true);
                    hideUserInfo();
                }
            } catch (error) {
                showMessage(`❌ Error checking authentication: ${error.message}`, true);
                hideUserInfo();
            }
        });

        logoutBtn.addEventListener("click", async () => {
            try {
                const response = await fetch(`${API_BASE}/auth/logout`, {
                    method: "POST",
                    credentials: "include",
                });

                showMessage("✅ Logged out successfully!");
                hideUserInfo();
                loginForm.reset();
            } catch (error) {
                showMessage(`❌ Error during logout: ${error.message}`, true);
            }
        });

        // Check backend status and authentication on page load
        window.addEventListener("load", () => {
            checkBackendStatus();
            checkAuthBtn.click();
        });
    </script>
</body>
</html>
