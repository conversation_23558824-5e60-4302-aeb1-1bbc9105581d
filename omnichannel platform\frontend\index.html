<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Omnichannel Platform - Login Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f7fafc;
            color: #2d3748;
            line-height: 1.6;
        }

        /* Login Container */
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 50px auto;
        }

        /* Platform Layout */
        #platformDashboard {
            display: none;
            min-height: 100vh;
            background-color: #f7fafc;
        }

        .platform-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            background: #4299e1;
            color: white;
        }

        .sidebar-header h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .sidebar-nav {
            padding: 0;
        }

        .nav-item {
            display: block;
            padding: 12px 20px;
            color: #4a5568;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            border-bottom: 1px solid #f7fafc;
        }

        .nav-item:hover {
            background-color: #edf2f7;
            color: #2d3748;
        }

        .nav-item.active {
            background-color: #4299e1;
            color: white;
            border-left: 4px solid #2b6cb0;
        }

        .nav-item i {
            margin-right: 10px;
            width: 16px;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 0;
        }

        .top-bar {
            background: white;
            padding: 15px 30px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #4a5568;
            font-size: 14px;
        }

        .content-area {
            padding: 30px;
            max-width: 1200px;
        }

        /* Settings Page Styles */
        .settings-sidebar {
            width: 280px;
            background: white;
            border-radius: 8px;
            padding: 0;
            margin-right: 30px;
            height: fit-content;
            border: 1px solid #e2e8f0;
        }

        .settings-nav-item {
            display: block;
            padding: 15px 20px;
            color: #4a5568;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            border-bottom: 1px solid #f7fafc;
        }

        .settings-nav-item:hover {
            background-color: #edf2f7;
        }

        .settings-nav-item.active {
            background-color: #4299e1;
            color: white;
        }

        .settings-content {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 30px;
            border: 1px solid #e2e8f0;
        }

        .settings-layout {
            display: flex;
            gap: 0;
        }

        /* Integration Cards */
        .integration-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .integration-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.2s;
        }

        .integration-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .integration-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .integration-title {
            font-weight: 600;
            color: #2d3748;
            font-size: 16px;
        }

        .integration-description {
            color: #718096;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .integration-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-disconnected {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-error {
            background-color: #fbb6ce;
            color: #702459;
        }

        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background-color: #cbd5e0;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .toggle-switch.active {
            background-color: #4299e1;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }

        /* Dashboard Metrics */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            text-align: center;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #718096;
            font-size: 14px;
        }

        /* Buttons */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3182ce;
        }

        .logout-btn {
            background-color: #e53e3e;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .logout-btn:hover {
            background-color: #c53030;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2d3748;
        }

        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.2s;
        }

        input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .error {
            color: #e53e3e;
            margin-top: 10px;
            padding: 10px;
            background-color: #fed7d7;
            border: 1px solid #feb2b2;
            border-radius: 6px;
        }

        .success {
            color: #22543d;
            margin-top: 10px;
            padding: 10px;
            background-color: #c6f6d5;
            border: 1px solid #9ae6b4;
            border-radius: 6px;
        }

        .test-credentials {
            background-color: #ebf8ff;
            border: 1px solid #4299e1;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .test-credentials h3 {
            margin: 0 0 10px 0;
            color: #2b6cb0;
        }

        .test-credentials p {
            margin: 5px 0;
            font-size: 14px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: #dc3545;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
        .success {
            color: #155724;
            margin-top: 10px;
            padding: 10px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
        }
        .test-credentials {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-credentials h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .test-credentials p {
            margin: 5px 0;
            font-size: 14px;
        }
        .user-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .button-group button {
            flex: 1;
        }
        .check-btn {
            background-color: #28a745;
        }
        .check-btn:hover {
            background-color: #218838;
        }
        .logout-btn {
            background-color: #dc3545;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        .nav-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 150px;
        }
        .nav-btn:hover {
            opacity: 0.8;
        }
        .content-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .metric-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .conversation-item, .contact-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .conversation-item h4, .contact-item h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .conversation-item p, .contact-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Login Container (shown initially) -->
    <div id="loginContainer" class="container">
        <h1>🚀 Omnichannel Platform Login</h1>

        <div class="test-credentials">
            <h3>Test Credentials:</h3>
            <p><strong>Super Admin:</strong> <EMAIL> / admin123</p>
            <p><strong>Test User:</strong> <EMAIL> / test123</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" id="loginBtn" class="btn btn-primary" style="width: 100%;">Login</button>
        </form>

        <div id="message"></div>

        <div style="margin-top: 20px; text-align: center;">
            <button id="testPlatformBtn" class="btn btn-primary">Test Platform</button>
        </div>
    </div>

    <!-- Platform Dashboard (hidden initially) -->
    <div id="platformDashboard">
        <div class="platform-layout">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <h2>Omnichannel</h2>
                </div>
                <nav class="sidebar-nav">
                    <button class="nav-item active" onclick="showSection('dashboard')">
                        <i>📊</i> Dashboard
                    </button>
                    <button class="nav-item" onclick="showSection('conversations')">
                        <i>💬</i> Conversations
                    </button>
                    <button class="nav-item" onclick="showSection('inboxes')">
                        <i>📥</i> Inboxes
                    </button>
                    <button class="nav-item" onclick="showSection('bot-builder')">
                        <i>🤖</i> Bot Builder
                    </button>
                    <button class="nav-item" onclick="showSection('settings')">
                        <i>⚙️</i> Settings
                    </button>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="top-bar">
                    <h1 class="page-title" id="pageTitle">Dashboard</h1>
                    <div class="user-info">
                        <span id="userWelcome">Welcome, User</span>
                        <button id="dashboardLogoutBtn" class="logout-btn">Logout</button>
                    </div>
                </div>

                <div class="content-area" id="contentArea">
                    <!-- Content will be loaded here dynamically -->
                </div>
            </div>
        </div>
    </div>

        <!-- Content Sections -->
        <div id="dashboardSection" class="content-section">
            <h2>📊 Dashboard Overview</h2>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; max-width: 1000px;">
                <div class="metric-card">
                    <h3>Total Messages</h3>
                    <div class="metric-value" id="totalMessages">Loading...</div>
                </div>
                <div class="metric-card">
                    <h3>Active Conversations</h3>
                    <div class="metric-value" id="activeConversations">Loading...</div>
                </div>
                <div class="metric-card">
                    <h3>Response Rate</h3>
                    <div class="metric-value" id="responseRate">Loading...</div>
                </div>
                <div class="metric-card">
                    <h3>Avg Response Time</h3>
                    <div class="metric-value" id="avgResponseTime">Loading...</div>
                </div>
            </div>
        </div>

        <div id="conversationsSection" class="content-section" style="display: none;">
            <h2>💬 Conversations</h2>
            <div id="conversationsList">Loading conversations...</div>
        </div>

        <div id="contactsSection" class="content-section" style="display: none;">
            <h2>👥 Contacts</h2>
            <div id="contactsList">Loading contacts...</div>
        </div>

        <div id="analyticsSection" class="content-section" style="display: none;">
            <h2>📈 Analytics</h2>
            <div id="analyticsData">Loading analytics...</div>
        </div>

        <div id="settingsSection" class="content-section" style="display: none;">
            <h2>⚙️ Settings</h2>
            <div id="settingsContent">Loading settings...</div>
        </div>

        <div id="integrationsSection" class="content-section" style="display: none;">
            <h2>🔗 Integrations</h2>
            <div id="integrationsContent">Loading integrations...</div>
        </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/api';

        const loginForm = document.getElementById("loginForm");
        const loginBtn = document.getElementById("loginBtn");
        const checkAuthBtn = document.getElementById("checkAuthBtn");
        const logoutBtn = document.getElementById("logoutBtn");
        const testPlatformBtn = document.getElementById("testPlatformBtn");
        const messageDiv = document.getElementById("message");
        const userInfoDiv = document.getElementById("userInfo");
        const backendStatusSpan = document.getElementById("backendStatus");

        function showMessage(message, isError = false) {
            messageDiv.innerHTML = `<div class="${isError ? "error" : "success"}">${message}</div>`;
        }

        function showUserInfo(user) {
            userInfoDiv.innerHTML = `
                <h3>✅ Logged in as:</h3>
                <p><strong>Name:</strong> ${user.first_name} ${user.last_name}</p>
                <p><strong>Email:</strong> ${user.email}</p>
                <p><strong>Role:</strong> ${user.role}</p>
                <p><strong>Company ID:</strong> ${user.company_id}</p>
            `;
            userInfoDiv.style.display = "block";
        }

        function hideUserInfo() {
            userInfoDiv.style.display = "none";
        }

        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    backendStatusSpan.textContent = "✅ Online";
                    backendStatusSpan.style.color = "#28a745";
                } else {
                    backendStatusSpan.textContent = "❌ Error";
                    backendStatusSpan.style.color = "#dc3545";
                }
            } catch (error) {
                backendStatusSpan.textContent = "❌ Offline";
                backendStatusSpan.style.color = "#dc3545";
            }
        }

        loginForm.addEventListener("submit", async (e) => {
            e.preventDefault();

            const email = document.getElementById("email").value;
            const password = document.getElementById("password").value;

            loginBtn.disabled = true;
            loginBtn.textContent = "Logging in...";

            try {
                const response = await fetch(`${API_BASE}/auth/token`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    credentials: "include",
                    body: JSON.stringify({ email, password }),
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log("Login successful, user data:", data.user);
                    showMessage("✅ Login successful! Opening platform...");
                    showUserInfo(data.user);
                    loginForm.reset();
                    // Show the platform dashboard immediately
                    setTimeout(() => {
                        console.log("Showing platform for user:", data.user);
                        showPlatform(data.user);
                    }, 500);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    showMessage(`❌ ${errorData.detail || "Login failed"}`, true);
                    hideUserInfo();
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, true);
                hideUserInfo();
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = "Login";
            }
        });

        checkAuthBtn.addEventListener("click", async () => {
            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    credentials: "include",
                });

                if (response.ok) {
                    const user = await response.json();
                    showMessage("✅ Authentication valid!");
                    showUserInfo(user);
                    // Show the platform dashboard if authenticated
                    showPlatform(user);
                } else {
                    showMessage("❌ Not authenticated", true);
                    hideUserInfo();
                }
            } catch (error) {
                showMessage(`❌ Error checking authentication: ${error.message}`, true);
                hideUserInfo();
            }
        });

        logoutBtn.addEventListener("click", async () => {
            try {
                const response = await fetch(`${API_BASE}/auth/logout`, {
                    method: "POST",
                    credentials: "include",
                });

                showMessage("✅ Logged out successfully!");
                hideUserInfo();
                loginForm.reset();
            } catch (error) {
                showMessage(`❌ Error during logout: ${error.message}`, true);
            }
        });

        // Dashboard functionality
        const loginContainer = document.querySelector('.container');
        const dashboardContainer = document.getElementById('platformDashboard');
        const dashboardLogoutBtn = document.getElementById('dashboardLogoutBtn');
        const userWelcome = document.getElementById('userWelcome');

        function showPlatform(user) {
            console.log("showPlatform called with user:", user);
            console.log("loginContainer:", loginContainer);
            console.log("dashboardContainer:", dashboardContainer);

            if (!loginContainer || !dashboardContainer) {
                console.error("Required containers not found!");
                return;
            }

            loginContainer.style.display = 'none';
            dashboardContainer.style.display = 'block';
            userWelcome.innerHTML = `
                <h3>Welcome back, ${user.first_name} ${user.last_name}!</h3>
                <p>Role: ${user.role} | Company ID: ${user.company_id}</p>
            `;
            console.log("Platform should now be visible");
            loadDashboardData();
        }

        function hidePlatform() {
            loginContainer.style.display = 'block';
            dashboardContainer.style.display = 'none';
        }

        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // Show selected section
            document.getElementById(sectionName + 'Section').style.display = 'block';

            // Load section data
            switch(sectionName) {
                case 'dashboard':
                    loadDashboardData();
                    break;
                case 'conversations':
                    loadConversations();
                    break;
                case 'contacts':
                    loadContacts();
                    break;
                case 'analytics':
                    loadAnalytics();
                    break;
                case 'settings':
                    loadSettings();
                    break;
                case 'integrations':
                    loadIntegrations();
                    break;
            }
        }

        async function loadDashboardData() {
            try {
                const response = await fetch(`${API_BASE}/analytics/dashboard-metrics`);
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('totalMessages').textContent = data.total_messages || '0';
                    document.getElementById('activeConversations').textContent = data.active_conversations || '0';
                    document.getElementById('responseRate').textContent = (data.response_rate || 0) + '%';
                    document.getElementById('avgResponseTime').textContent = (data.avg_response_time || 0) + 'min';
                } else {
                    // Show sample data if API fails
                    document.getElementById('totalMessages').textContent = '1,234';
                    document.getElementById('activeConversations').textContent = '56';
                    document.getElementById('responseRate').textContent = '94%';
                    document.getElementById('avgResponseTime').textContent = '2.3min';
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                // Show sample data
                document.getElementById('totalMessages').textContent = '1,234';
                document.getElementById('activeConversations').textContent = '56';
                document.getElementById('responseRate').textContent = '94%';
                document.getElementById('avgResponseTime').textContent = '2.3min';
            }
        }

        async function loadConversations() {
            const conversationsList = document.getElementById('conversationsList');
            conversationsList.innerHTML = `
                <div class="conversation-item">
                    <h4>Customer Support - John Doe</h4>
                    <p>Last message: "Thank you for your help!" - 2 minutes ago</p>
                </div>
                <div class="conversation-item">
                    <h4>Sales Inquiry - Jane Smith</h4>
                    <p>Last message: "I'm interested in your premium plan" - 15 minutes ago</p>
                </div>
                <div class="conversation-item">
                    <h4>Technical Support - Mike Johnson</h4>
                    <p>Last message: "The integration is working perfectly now" - 1 hour ago</p>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #666;">
                    💡 This is sample data. Real conversations will be loaded from the API.
                </p>
            `;
        }

        async function loadContacts() {
            const contactsList = document.getElementById('contactsList');
            contactsList.innerHTML = `
                <div class="contact-item">
                    <h4>John Doe</h4>
                    <p>Email: <EMAIL> | Phone: +****************</p>
                </div>
                <div class="contact-item">
                    <h4>Jane Smith</h4>
                    <p>Email: <EMAIL> | Phone: +****************</p>
                </div>
                <div class="contact-item">
                    <h4>Mike Johnson</h4>
                    <p>Email: <EMAIL> | Phone: +****************</p>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #666;">
                    💡 This is sample data. Real contacts will be loaded from the API.
                </p>
            `;
        }

        async function loadAnalytics() {
            const analyticsData = document.getElementById('analyticsData');
            analyticsData.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="metric-card">
                        <h3>Messages This Week</h3>
                        <div class="metric-value">2,847</div>
                    </div>
                    <div class="metric-card">
                        <h3>New Customers</h3>
                        <div class="metric-value">127</div>
                    </div>
                    <div class="metric-card">
                        <h3>Conversion Rate</h3>
                        <div class="metric-value">12.4%</div>
                    </div>
                    <div class="metric-card">
                        <h3>Customer Satisfaction</h3>
                        <div class="metric-value">4.8/5</div>
                    </div>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #666;">
                    💡 This is sample data. Real analytics will be loaded from the API.
                </p>
            `;
        }

        async function loadSettings() {
            const settingsContent = document.getElementById('settingsContent');
            settingsContent.innerHTML = `
                <div style="max-width: 600px;">
                    <h3>Account Settings</h3>
                    <div style="margin-bottom: 20px;">
                        <label>Company Name:</label>
                        <input type="text" value="Default Company" style="width: 100%; margin-top: 5px;">
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label>Notification Preferences:</label>
                        <div style="margin-top: 10px;">
                            <label><input type="checkbox" checked> Email notifications</label><br>
                            <label><input type="checkbox" checked> SMS notifications</label><br>
                            <label><input type="checkbox"> Push notifications</label>
                        </div>
                    </div>
                    <button style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
                        Save Settings
                    </button>
                </div>
                <p style="margin-top: 20px; color: #666;">
                    💡 This is a demo interface. Real settings will be loaded from the API.
                </p>
            `;
        }

        async function loadIntegrations() {
            const integrationsContent = document.getElementById('integrationsContent');
            integrationsContent.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div class="metric-card">
                        <h3>WhatsApp Business</h3>
                        <div style="color: #28a745; font-weight: bold;">✅ Connected</div>
                        <button style="margin-top: 10px; padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px;">
                            Configure
                        </button>
                    </div>
                    <div class="metric-card">
                        <h3>Facebook Messenger</h3>
                        <div style="color: #dc3545; font-weight: bold;">❌ Not Connected</div>
                        <button style="margin-top: 10px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;">
                            Connect
                        </button>
                    </div>
                    <div class="metric-card">
                        <h3>Telegram</h3>
                        <div style="color: #dc3545; font-weight: bold;">❌ Not Connected</div>
                        <button style="margin-top: 10px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;">
                            Connect
                        </button>
                    </div>
                    <div class="metric-card">
                        <h3>Email</h3>
                        <div style="color: #28a745; font-weight: bold;">✅ Connected</div>
                        <button style="margin-top: 10px; padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px;">
                            Configure
                        </button>
                    </div>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #666;">
                    💡 This is a demo interface. Real integrations will be loaded from the API.
                </p>
            `;
        }

        // Test platform button
        testPlatformBtn.addEventListener("click", () => {
            console.log("Test platform button clicked");
            const testUser = {
                first_name: "Test",
                last_name: "User",
                email: "<EMAIL>",
                role: "admin",
                company_id: 1
            };
            showPlatform(testUser);
        });

        // Dashboard logout
        dashboardLogoutBtn.addEventListener("click", async () => {
            try {
                await fetch(`${API_BASE}/auth/logout`, {
                    method: "POST",
                    credentials: "include",
                });
                hidePlatform();
                showMessage("✅ Logged out successfully!");
                hideUserInfo();
                loginForm.reset();
            } catch (error) {
                showMessage(`❌ Error during logout: ${error.message}`, true);
            }
        });

        // Check backend status and authentication on page load
        window.addEventListener("load", () => {
            checkBackendStatus();
            checkAuthBtn.click();
        });
    </script>
</body>
</html>
