<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Omnichannel Platform - Login Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f7fafc;
            color: #2d3748;
            line-height: 1.6;
        }

        /* Login Container */
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 50px auto;
        }

        /* Platform Layout */
        #platformDashboard {
            display: none;
            min-height: 100vh;
            background-color: #f7fafc;
        }

        .platform-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            background: #4299e1;
            color: white;
        }

        .sidebar-header h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .sidebar-nav {
            padding: 0;
        }

        .nav-item {
            display: block;
            padding: 12px 20px;
            color: #4a5568;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            border-bottom: 1px solid #f7fafc;
        }

        .nav-item:hover {
            background-color: #edf2f7;
            color: #2d3748;
        }

        .nav-item.active {
            background-color: #4299e1;
            color: white;
            border-left: 4px solid #2b6cb0;
        }

        .nav-item i {
            margin-right: 10px;
            width: 16px;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 0;
        }

        .top-bar {
            background: white;
            padding: 15px 30px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #4a5568;
            font-size: 14px;
        }

        .content-area {
            padding: 30px;
            max-width: 1200px;
        }

        /* Settings Page Styles */
        .settings-sidebar {
            width: 280px;
            background: white;
            border-radius: 8px;
            padding: 0;
            margin-right: 30px;
            height: fit-content;
            border: 1px solid #e2e8f0;
        }

        .settings-nav-item {
            display: block;
            padding: 15px 20px;
            color: #4a5568;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            border-bottom: 1px solid #f7fafc;
        }

        .settings-nav-item:hover {
            background-color: #edf2f7;
        }

        .settings-nav-item.active {
            background-color: #4299e1;
            color: white;
        }

        .settings-content {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 30px;
            border: 1px solid #e2e8f0;
        }

        .settings-layout {
            display: flex;
            gap: 0;
        }

        /* Integration Cards */
        .integration-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .integration-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.2s;
        }

        .integration-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .integration-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .integration-title {
            font-weight: 600;
            color: #2d3748;
            font-size: 16px;
        }

        .integration-description {
            color: #718096;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .integration-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-disconnected {
            background-color: #fed7d7;
            color: #742a2a;
        }

        .status-error {
            background-color: #fbb6ce;
            color: #702459;
        }

        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background-color: #cbd5e0;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .toggle-switch.active {
            background-color: #4299e1;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }

        /* Dashboard Metrics */
        .dashboard-header {
            margin-bottom: 30px;
        }

        .dashboard-header h1 {
            margin: 0 0 8px 0;
            color: #1a202c;
            font-size: 28px;
            font-weight: 600;
        }

        .dashboard-header p {
            margin: 0;
            color: #718096;
            font-size: 16px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .additional-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .metric-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f7fafc;
            border-radius: 8px;
        }

        .metric-content {
            flex: 1;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #718096;
            font-size: 14px;
        }

        .dashboard-charts {
            margin-top: 30px;
        }

        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-bottom: 20px;
        }

        .chart-container h3 {
            margin: 0 0 15px 0;
            color: #1a202c;
            font-size: 18px;
            font-weight: 600;
        }

        .chart-placeholder {
            background: #f7fafc;
            padding: 40px;
            border-radius: 6px;
            text-align: center;
            color: #718096;
        }

        .chart-placeholder p {
            margin: 5px 0;
        }

        #dataSourceInfo {
            font-size: 12px;
            color: #a0aec0;
            font-style: italic;
        }

        /* Conversations Styles */
        .conversations-header {
            margin-bottom: 30px;
        }

        .conversations-header h1 {
            margin: 0 0 8px 0;
            color: #1a202c;
            font-size: 28px;
            font-weight: 600;
        }

        .conversations-header p {
            margin: 0;
            color: #718096;
            font-size: 16px;
        }

        .conversations-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #1a202c;
        }

        .filter-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #4a5568;
            font-size: 14px;
        }

        .search-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-group input {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            width: 250px;
            font-size: 14px;
        }

        .advanced-search-btn {
            padding: 8px 12px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .advanced-search-btn:hover {
            background: #3182ce;
        }

        /* Advanced Search Modal Styles */
        .advanced-search-modal {
            max-width: 600px;
            width: 90%;
        }

        .search-criteria-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .search-criteria-full {
            grid-column: 1 / -1;
        }

        .date-range-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-range-group input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }

        .quick-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }

        .quick-filter-btn {
            padding: 6px 12px;
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .quick-filter-btn:hover {
            background: #edf2f7;
        }

        .quick-filter-btn.active {
            background: #4299e1;
            color: white;
            border-color: #4299e1;
        }

        .search-results-summary {
            background: #f7fafc;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #4a5568;
        }

        /* Search Summary Styles */
        .search-summary {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }

        .search-summary-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 8px;
        }

        .search-results-text {
            color: #234e52;
            font-weight: 500;
        }

        .active-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .filter-tag {
            background: #4299e1;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .clear-search-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .clear-search-btn:hover {
            background: #c53030;
        }

        /* Bulk Operations Styles */
        .bulk-operations-toolbar {
            background: #4299e1;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 16px;
            display: none;
            align-items: center;
            justify-content: space-between;
            animation: slideDown 0.3s ease-out;
        }

        .bulk-operations-toolbar.show {
            display: flex;
        }

        .bulk-selection-info {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
        }

        .bulk-action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .bulk-action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .bulk-action-btn.danger {
            background: #e53e3e;
            border-color: #c53030;
        }

        .bulk-action-btn.danger:hover {
            background: #c53030;
        }

        .conversation-item, .contact-item {
            position: relative;
        }

        .bulk-checkbox {
            position: absolute;
            top: 12px;
            left: 12px;
            width: 18px;
            height: 18px;
            cursor: pointer;
            z-index: 2;
        }

        .conversation-item.bulk-mode, .contact-item.bulk-mode {
            padding-left: 45px;
        }

        .conversation-item.selected, .contact-item.selected {
            background-color: #e6fffa !important;
            border-left-color: #38b2ac !important;
        }

        .bulk-mode-toggle {
            background: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
            margin-left: 8px;
        }

        .bulk-mode-toggle:hover {
            background: #3182ce;
        }

        .bulk-mode-toggle.active {
            background: #38b2ac;
        }

        .select-all-checkbox {
            margin-right: 8px;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .conversations-container {
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .conversations-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .conversation-item {
            display: flex;
            padding: 16px 20px;
            border-bottom: 1px solid #f7fafc;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .conversation-item:hover {
            background-color: #f7fafc;
        }

        .conversation-item.unread {
            background-color: #ebf8ff;
            border-left: 4px solid #4299e1;
        }

        .conversation-avatar {
            margin-right: 15px;
        }

        .avatar-circle {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background: #f7fafc;
        }

        .avatar-circle.whatsapp { background: #25d366; color: white; }
        .avatar-circle.telegram { background: #0088cc; color: white; }
        .avatar-circle.email { background: #ea4335; color: white; }
        .avatar-circle.sms { background: #8b5cf6; color: white; }

        .conversation-content {
            flex: 1;
            min-width: 0;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .conversation-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1a202c;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-time {
            font-size: 12px;
            color: #a0aec0;
            white-space: nowrap;
        }

        .conversation-preview {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .conversation-preview p {
            margin: 0;
            font-size: 14px;
            color: #718096;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }

        .unread-badge {
            background: #4299e1;
            color: white;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 8px;
        }

        .conversation-meta {
            display: flex;
            gap: 8px;
        }

        .channel-badge, .status-badge {
            font-size: 11px;
            padding: 2px 8px;
            border-radius: 4px;
            text-transform: uppercase;
            font-weight: 500;
        }

        .channel-badge.whatsapp { background: #dcfce7; color: #166534; }
        .channel-badge.telegram { background: #dbeafe; color: #1e40af; }
        .channel-badge.email { background: #fef2f2; color: #dc2626; }
        .channel-badge.sms { background: #f3e8ff; color: #7c3aed; }

        .status-badge.active { background: #dcfce7; color: #166534; }
        .status-badge.archived { background: #f1f5f9; color: #475569; }
        .status-badge.spam { background: #fef2f2; color: #dc2626; }

        .loading-state, .empty-state {
            padding: 40px;
            text-align: center;
            color: #718096;
        }

        .empty-state small {
            display: block;
            margin-top: 8px;
            color: #a0aec0;
        }

        /* Contacts Styles */
        .contacts-header {
            margin-bottom: 30px;
        }

        .contacts-header h1 {
            margin: 0 0 8px 0;
            color: #1a202c;
            font-size: 28px;
            font-weight: 600;
        }

        .contacts-header p {
            margin: 0;
            color: #718096;
            font-size: 16px;
        }

        .contacts-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .contacts-container {
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .contacts-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .contact-item {
            display: flex;
            padding: 16px 20px;
            border-bottom: 1px solid #f7fafc;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .contact-item:hover {
            background-color: #f7fafc;
        }

        .contact-avatar {
            margin-right: 15px;
        }

        .contact-content {
            flex: 1;
            min-width: 0;
        }

        .contact-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .contact-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1a202c;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .contact-time {
            font-size: 12px;
            color: #a0aec0;
            white-space: nowrap;
        }

        .contact-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .contact-details p {
            margin: 0;
            font-size: 14px;
            color: #718096;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }

        .contact-meta {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* Analytics Styles */
        .analytics-header {
            margin-bottom: 30px;
        }

        .analytics-header h1 {
            margin: 0 0 8px 0;
            color: #1a202c;
            font-size: 28px;
            font-weight: 600;
        }

        .analytics-header p {
            margin: 0;
            color: #718096;
            font-size: 16px;
        }

        .analytics-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .analytics-grid {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .analytics-section {
            background: white;
            padding: 25px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .analytics-section h3 {
            margin: 0 0 20px 0;
            color: #1a202c;
            font-size: 20px;
            font-weight: 600;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .chart-container h4 {
            margin: 0 0 15px 0;
            color: #1a202c;
            font-size: 16px;
            font-weight: 600;
        }

        .chart-placeholder {
            background: #f7fafc;
            border: 2px dashed #e2e8f0;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .chart-placeholder p {
            margin: 0 0 8px 0;
            color: #4a5568;
            font-size: 16px;
        }

        .chart-data {
            color: #718096 !important;
            font-size: 14px !important;
        }

        .mock-chart {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .chart-bars {
            display: flex;
            align-items: end;
            gap: 8px;
            height: 120px;
            margin-bottom: 15px;
        }

        .chart-bar {
            width: 20px;
            background: linear-gradient(to top, #4299e1, #63b3ed);
            border-radius: 4px 4px 0 0;
            min-height: 20px;
        }

        .analytics-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .analytics-table h4 {
            margin: 0 0 15px 0;
            color: #1a202c;
            font-size: 16px;
            font-weight: 600;
        }

        .table-container {
            background: #f7fafc;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .analytics-table-content {
            width: 100%;
            border-collapse: collapse;
        }

        .analytics-table-content th {
            background: #edf2f7;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #2d3748;
            border-bottom: 1px solid #e2e8f0;
        }

        .analytics-table-content td {
            padding: 12px;
            border-bottom: 1px solid #f1f5f9;
            color: #4a5568;
        }

        .analytics-table-content tr:hover {
            background: #f7fafc;
        }

        .metric-change {
            font-size: 12px;
            font-weight: 600;
            margin-top: 4px;
        }

        .metric-change:contains('+') {
            color: #38a169;
        }

        .metric-change:contains('-') {
            color: #e53e3e;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-header h3 {
            margin: 0;
            color: #1a202c;
            font-size: 20px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #a0aec0;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: #718096;
        }

        .modal-body {
            padding: 25px;
        }

        .config-section {
            margin-bottom: 30px;
        }

        .config-section h4 {
            margin: 0 0 8px 0;
            color: #1a202c;
            font-size: 16px;
            font-weight: 600;
        }

        .config-description {
            margin: 0 0 20px 0;
            color: #718096;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            color: #2d3748;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-group small {
            display: block;
            margin-top: 4px;
            color: #a0aec0;
            font-size: 12px;
        }

        .webhook-url-display {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 12px;
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }

        .webhook-url-display code {
            flex: 1;
            background: none;
            color: #2d3748;
            font-size: 13px;
        }

        .copy-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .copy-btn:hover {
            background: #3182ce;
        }

        .status-display {
            background: #f7fafc;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            color: #4a5568;
            font-weight: 500;
        }

        .status-value {
            color: #2d3748;
            font-weight: 600;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 20px 25px;
            border-top: 1px solid #e2e8f0;
        }

        .btn-secondary {
            background: #edf2f7;
            color: #4a5568;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: #3182ce;
        }

        .integration-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }

        .config-btn, .test-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            font-weight: 500;
        }

        .config-btn:hover, .test-btn:hover {
            background: #3182ce;
        }

        .test-btn {
            background: #38a169;
        }

        .test-btn:hover {
            background: #2f855a;
        }

        /* Conversation Modal Styles */
        .conversation-modal {
            max-width: 800px;
            width: 95%;
            height: 80vh;
            display: flex;
            flex-direction: column;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            border-bottom: 1px solid #e2e8f0;
            background: #f7fafc;
        }

        .conversation-info h3 {
            margin: 0 0 4px 0;
            color: #1a202c;
            font-size: 18px;
            font-weight: 600;
        }

        .conversation-info p {
            margin: 0;
            color: #718096;
            font-size: 14px;
        }

        .conversation-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f7fafc;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            position: relative;
        }

        .message-sent {
            justify-content: flex-end;
        }

        /* Message Threading Styles */
        .message-thread {
            margin-left: 40px;
            border-left: 2px solid #e2e8f0;
            padding-left: 15px;
            margin-top: 8px;
        }

        .message-thread .message {
            margin-bottom: 8px;
        }

        .message-thread-indicator {
            position: absolute;
            left: -8px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            background: #4299e1;
            border-radius: 50%;
            border: 2px solid white;
            font-size: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .message-reply-indicator {
            background: #38b2ac;
            font-size: 10px;
        }

        .message-actions {
            position: absolute;
            top: 5px;
            right: 5px;
            opacity: 0;
            transition: opacity 0.2s ease;
            display: flex;
            gap: 5px;
        }

        .message:hover .message-actions {
            opacity: 1;
        }

        .message-action-btn {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .message-action-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }

        .reply-context {
            background: #f1f5f9;
            border-left: 3px solid #4299e1;
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 0 6px 6px 0;
            font-size: 13px;
            color: #64748b;
        }

        .reply-context-author {
            font-weight: 600;
            color: #4299e1;
            margin-bottom: 2px;
        }

        .reply-context-text {
            font-style: italic;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 300px;
        }

        .thread-collapse-btn {
            background: #e2e8f0;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
            color: #64748b;
            cursor: pointer;
            margin: 5px 0;
            transition: background 0.2s ease;
        }

        .thread-collapse-btn:hover {
            background: #cbd5e0;
        }

        .thread-collapsed {
            display: none;
        }

        .thread-summary {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 8px 12px;
            margin: 5px 0;
            font-size: 13px;
            color: #64748b;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .thread-summary:hover {
            background: #edf2f7;
        }

        .thread-count {
            font-weight: 600;
            color: #4299e1;
        }

        /* Reply Context in Composer */
        .reply-context-composer {
            margin-bottom: 10px;
        }

        .reply-context-composer .reply-context {
            position: relative;
            padding-right: 30px;
        }

        .reply-context-close {
            position: absolute;
            top: 5px;
            right: 5px;
            background: none;
            border: none;
            font-size: 16px;
            color: #64748b;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .reply-context-close:hover {
            background: #e2e8f0;
        }

        /* Message Thread Controls */
        .message-thread-controls {
            margin-top: 8px;
            margin-bottom: 5px;
        }

        .collapse-icon {
            margin-left: 5px;
            font-size: 10px;
            transition: transform 0.2s ease;
        }

        /* File Upload and Media Support Styles */
        .file-upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f7fafc;
            transition: all 0.3s ease;
            cursor: pointer;
            margin: 10px 0;
        }

        .file-upload-area:hover {
            border-color: #4299e1;
            background: #ebf8ff;
        }

        .file-upload-area.dragover {
            border-color: #4299e1;
            background: #ebf8ff;
            transform: scale(1.02);
        }

        .file-upload-input {
            display: none;
        }

        .file-upload-icon {
            font-size: 48px;
            color: #a0aec0;
            margin-bottom: 10px;
        }

        .file-upload-text {
            color: #4a5568;
            font-size: 14px;
        }

        .file-upload-hint {
            color: #718096;
            font-size: 12px;
            margin-top: 5px;
        }

        .media-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }

        .media-preview-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
            background: white;
        }

        .media-preview-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
        }

        .media-preview-file {
            width: 120px;
            height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px;
            background: #f7fafc;
        }

        .media-preview-file-icon {
            font-size: 24px;
            margin-bottom: 5px;
            color: #4299e1;
        }

        .media-preview-file-name {
            font-size: 10px;
            text-align: center;
            color: #4a5568;
            word-break: break-all;
            line-height: 1.2;
        }

        .media-preview-remove {
            position: absolute;
            top: 2px;
            right: 2px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .media-preview-remove:hover {
            background: rgba(0, 0, 0, 0.9);
        }

        .media-caption-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            margin-top: 10px;
            resize: none;
        }

        .media-caption-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .file-size-info {
            font-size: 11px;
            color: #718096;
            margin-top: 5px;
        }

        .file-upload-progress {
            width: 100%;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 10px;
        }

        .file-upload-progress-bar {
            height: 100%;
            background: #4299e1;
            transition: width 0.3s ease;
        }

        .media-type-selector {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }

        .media-type-btn {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .media-type-btn:hover {
            background: #edf2f7;
        }

        .media-type-btn.active {
            background: #4299e1;
            color: white;
            border-color: #4299e1;
        }

        .message-media {
            max-width: 300px;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .message-media img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .message-media-file {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            max-width: 300px;
        }

        .message-media-file-icon {
            font-size: 24px;
            color: #4299e1;
        }

        .message-media-file-info {
            flex: 1;
        }

        .message-media-file-name {
            font-weight: 600;
            color: #2d3748;
            font-size: 14px;
        }

        .message-media-file-size {
            color: #718096;
            font-size: 12px;
        }

        .message-media-caption {
            margin-top: 8px;
            font-style: italic;
            color: #4a5568;
        }

        /* Call and Communication Buttons */
        .conversation-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .action-btn {
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .action-btn:hover {
            background: #3182ce;
            transform: translateY(-1px);
        }

        .action-btn.call-btn {
            background: #38a169;
        }

        .action-btn.call-btn:hover {
            background: #2f855a;
        }

        .action-btn.video-btn {
            background: #805ad5;
        }

        .action-btn.video-btn:hover {
            background: #6b46c1;
        }

        .action-btn.info-btn {
            background: #718096;
        }

        .action-btn.info-btn:hover {
            background: #4a5568;
        }

        .action-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        /* Call Interface Styles */
        .call-interface {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border: 1px solid #e2e8f0;
            z-index: 10000;
            overflow: hidden;
        }

        .call-header {
            background: #4299e1;
            color: white;
            padding: 15px 20px;
            text-align: center;
        }

        .call-status {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .call-contact {
            font-size: 18px;
            font-weight: 600;
        }

        .call-duration {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 5px;
        }

        .call-controls {
            padding: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .call-control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: all 0.2s ease;
        }

        .call-control-btn.mute-btn {
            background: #718096;
            color: white;
        }

        .call-control-btn.mute-btn:hover {
            background: #4a5568;
        }

        .call-control-btn.mute-btn.muted {
            background: #e53e3e;
        }

        .call-control-btn.end-btn {
            background: #e53e3e;
            color: white;
        }

        .call-control-btn.end-btn:hover {
            background: #c53030;
        }

        .call-control-btn.speaker-btn {
            background: #718096;
            color: white;
        }

        .call-control-btn.speaker-btn:hover {
            background: #4a5568;
        }

        .call-control-btn.speaker-btn.active {
            background: #4299e1;
        }

        /* Call and Communication Buttons */
        .conversation-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .action-btn {
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .action-btn:hover {
            background: #3182ce;
            transform: translateY(-1px);
        }

        .action-btn.call-btn {
            background: #38a169;
        }

        .action-btn.call-btn:hover {
            background: #2f855a;
        }

        .action-btn.video-btn {
            background: #805ad5;
        }

        .action-btn.video-btn:hover {
            background: #6b46c1;
        }

        .action-btn.info-btn {
            background: #718096;
        }

        .action-btn.info-btn:hover {
            background: #4a5568;
        }

        .action-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        /* Call Interface Styles */
        .call-interface {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border: 1px solid #e2e8f0;
            z-index: 10000;
            overflow: hidden;
        }

        .call-header {
            background: #4299e1;
            color: white;
            padding: 15px 20px;
            text-align: center;
        }

        .call-status {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .call-contact {
            font-size: 18px;
            font-weight: 600;
        }

        .call-duration {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 5px;
        }

        .call-controls {
            padding: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .call-control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: all 0.2s ease;
        }

        .call-control-btn.mute-btn {
            background: #718096;
            color: white;
        }

        .call-control-btn.mute-btn:hover {
            background: #4a5568;
        }

        .call-control-btn.mute-btn.muted {
            background: #e53e3e;
        }

        .call-control-btn.end-btn {
            background: #e53e3e;
            color: white;
        }

        .call-control-btn.end-btn:hover {
            background: #c53030;
        }

        .call-control-btn.speaker-btn {
            background: #718096;
            color: white;
        }

        .call-control-btn.speaker-btn:hover {
            background: #4a5568;
        }

        .call-control-btn.speaker-btn.active {
            background: #4299e1;
        }

        /* Media Message Types */
        .media-message-types {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .media-message-types .message-type-btn {
            font-size: 12px;
            padding: 6px 10px;
        }

        .message-received {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            position: relative;
        }

        .message-sent .message-content {
            background: #4299e1;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message-received .message-content {
            background: white;
            color: #1a202c;
            border: 1px solid #e2e8f0;
            border-bottom-left-radius: 4px;
        }

        .message-text {
            margin-bottom: 6px;
            line-height: 1.4;
        }

        .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            opacity: 0.8;
        }

        .message-status {
            margin-left: 8px;
            font-weight: 500;
        }

        .loading-messages, .no-messages, .error-messages {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #718096;
        }

        .loading-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message-composer {
            border-top: 1px solid #e2e8f0;
            background: white;
        }

        .composer-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e2e8f0;
        }

        .message-type-selector {
            display: flex;
            gap: 10px;
        }

        .message-type-btn {
            background: #edf2f7;
            color: #4a5568;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .message-type-btn.active {
            background: #4299e1;
            color: white;
        }

        .message-type-btn:hover:not(.active) {
            background: #e2e8f0;
        }

        .composer-body {
            padding: 20px;
        }

        .composer-section {
            display: block;
        }

        .composer-section.hidden {
            display: none;
        }

        .message-input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .message-input-container textarea {
            flex: 1;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            resize: vertical;
            min-height: 60px;
            font-family: inherit;
        }

        .message-input-container textarea:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .send-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            white-space: nowrap;
        }

        .send-btn:hover {
            background: #3182ce;
        }

        .template-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .template-selector select {
            flex: 1;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }

        .refresh-templates-btn {
            background: #edf2f7;
            color: #4a5568;
            border: none;
            padding: 10px 12px;
            border-radius: 6px;
            cursor: pointer;
        }

        .refresh-templates-btn:hover {
            background: #e2e8f0;
        }

        .template-preview {
            background: #f7fafc;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            margin-bottom: 15px;
        }

        .template-preview-content h4 {
            margin: 0 0 8px 0;
            color: #1a202c;
            font-size: 16px;
        }

        .template-body {
            margin: 8px 0;
            color: #2d3748;
            font-style: italic;
        }

        .template-parameters {
            margin-bottom: 15px;
        }

        .template-parameters h5 {
            margin: 0 0 10px 0;
            color: #2d3748;
            font-size: 14px;
        }

        .parameter-input {
            margin-bottom: 10px;
        }

        .parameter-input label {
            display: block;
            margin-bottom: 4px;
            color: #4a5568;
            font-size: 13px;
            font-weight: 500;
        }

        .parameter-input input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
        }

        .parameter-input input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
        }

        .media-upload {
            margin-bottom: 15px;
        }

        .media-upload input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .media-upload input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }

        /* Buttons */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3182ce;
        }

        .logout-btn {
            background-color: #e53e3e;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .logout-btn:hover {
            background-color: #c53030;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2d3748;
        }

        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.2s;
        }

        input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .error {
            color: #e53e3e;
            margin-top: 10px;
            padding: 10px;
            background-color: #fed7d7;
            border: 1px solid #feb2b2;
            border-radius: 6px;
        }

        .success {
            color: #22543d;
            margin-top: 10px;
            padding: 10px;
            background-color: #c6f6d5;
            border: 1px solid #9ae6b4;
            border-radius: 6px;
        }

        .test-credentials {
            background-color: #ebf8ff;
            border: 1px solid #4299e1;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .test-credentials h3 {
            margin: 0 0 10px 0;
            color: #2b6cb0;
        }

        .test-credentials p {
            margin: 5px 0;
            font-size: 14px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: #dc3545;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
        .success {
            color: #155724;
            margin-top: 10px;
            padding: 10px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
        }
        .test-credentials {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-credentials h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .test-credentials p {
            margin: 5px 0;
            font-size: 14px;
        }
        .user-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .button-group button {
            flex: 1;
        }
        .check-btn {
            background-color: #28a745;
        }
        .check-btn:hover {
            background-color: #218838;
        }
        .logout-btn {
            background-color: #dc3545;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        .nav-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 150px;
        }
        .nav-btn:hover {
            opacity: 0.8;
        }
        .content-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .metric-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .conversation-item, .contact-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .conversation-item h4, .contact-item h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .conversation-item p, .contact-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Login Container (shown initially) -->
    <div id="loginContainer" class="container">
        <h1>🚀 Omnichannel Platform Login</h1>

        <div class="test-credentials">
            <h3>Test Credentials:</h3>
            <p><strong>Super Admin:</strong> <EMAIL> / admin123</p>
            <p><strong>Test User:</strong> <EMAIL> / test123</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" id="loginBtn" class="btn btn-primary" style="width: 100%;">Login</button>
        </form>

        <div id="message"></div>

        <div style="margin-top: 20px; text-align: center;">
            <button id="testPlatformBtn" class="btn btn-primary">Test Platform</button>
        </div>
    </div>

    <!-- Platform Dashboard (hidden initially) -->
    <div id="platformDashboard">
        <div class="platform-layout">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <h2>Omnichannel</h2>
                </div>
                <nav class="sidebar-nav">
                    <button class="nav-item active" onclick="showSection('dashboard')">
                        <i>📊</i> Dashboard
                    </button>
                    <button class="nav-item" onclick="showSection('conversations')">
                        <i>💬</i> Conversations
                    </button>
                    <button class="nav-item" onclick="showSection('contacts')">
                        <i>👥</i> Contacts
                    </button>
                    <button class="nav-item" onclick="showSection('inboxes')">
                        <i>📥</i> Inboxes
                    </button>
                    <button class="nav-item" onclick="showSection('bot-builder')">
                        <i>🤖</i> Bot Builder
                    </button>
                    <button class="nav-item" onclick="showSection('analytics')">
                        <i>📊</i> Analytics
                    </button>
                    <button class="nav-item" onclick="showSection('settings')">
                        <i>⚙️</i> Settings
                    </button>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="top-bar">
                    <h1 class="page-title" id="pageTitle">Dashboard</h1>
                    <div class="user-info">
                        <span id="userWelcome">Welcome, User</span>
                        <button id="dashboardLogoutBtn" class="logout-btn">Logout</button>
                    </div>
                </div>

                <div class="content-area" id="contentArea">
                    <!-- Content will be loaded here dynamically -->
                </div>
            </div>
        </div>
    </div>



    <script>
        const API_BASE = 'http://localhost:8000/api/api';
        let currentUser = null;
        let currentSection = 'dashboard';

        // DOM Elements
        const loginContainer = document.getElementById("loginContainer");
        const platformDashboard = document.getElementById("platformDashboard");
        const loginForm = document.getElementById("loginForm");
        const messageDiv = document.getElementById("message");
        const contentArea = document.getElementById("contentArea");
        const pageTitle = document.getElementById("pageTitle");
        const userWelcome = document.getElementById("userWelcome");

        // Initialize the application
        function initApp() {
            // Show login page first
            loginContainer.style.display = "block";
            platformDashboard.style.display = "none";

            // Check if user is already logged in
            checkExistingAuth();
        }

        // Check for existing authentication
        async function checkExistingAuth() {
            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    credentials: 'include'
                });

                if (response.ok) {
                    const user = await response.json();
                    currentUser = user;
                    showPlatform();
                }
            } catch (error) {
                console.log('No existing auth found');
            }
        }

        // Show success/error messages
        function showMessage(message, isError = false) {
            messageDiv.innerHTML = `<div class="${isError ? "error" : "success"}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }

        // Show the main platform interface
        function showPlatform() {
            loginContainer.style.display = "none";
            platformDashboard.style.display = "block";

            if (currentUser) {
                userWelcome.textContent = `Welcome, ${currentUser.first_name} ${currentUser.last_name}`;
            }

            // Load the default section
            showSection('dashboard');
        }

        // Navigation function
        function showSection(section) {
            currentSection = section;

            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event?.target?.classList.add('active');

            // Update page title
            const titles = {
                'dashboard': 'Dashboard',
                'conversations': 'Conversations',
                'contacts': 'Contacts',
                'inboxes': 'Inboxes',
                'bot-builder': 'Bot Builder',
                'analytics': 'Analytics',
                'settings': 'Settings'
            };
            pageTitle.textContent = titles[section] || section;

            // Load section content
            loadSectionContent(section);
        }

        // Load content for each section
        function loadSectionContent(section) {
            switch(section) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'conversations':
                    loadConversations();
                    break;
                case 'contacts':
                    loadContacts();
                    break;
                case 'inboxes':
                    loadInboxes();
                    break;
                case 'bot-builder':
                    loadBotBuilder();
                    break;
                case 'analytics':
                    loadAnalytics();
                    break;
                case 'settings':
                    loadSettings();
                    break;
                default:
                    contentArea.innerHTML = `<h2>Section: ${section}</h2><p>Content coming soon...</p>`;
            }
        }

        // Dashboard content
        function loadDashboard() {
            contentArea.innerHTML = `
                <div class="dashboard-header">
                    <h1>Dashboard</h1>
                    <p>Welcome back! Here's what's happening with your omnichannel platform.</p>
                </div>

                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">📊</div>
                        <div class="metric-content">
                            <div class="metric-value" id="totalMessages">Loading...</div>
                            <div class="metric-label">Total Messages</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">💬</div>
                        <div class="metric-content">
                            <div class="metric-value" id="activeConversations">Loading...</div>
                            <div class="metric-label">Active Conversations</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">👥</div>
                        <div class="metric-content">
                            <div class="metric-value" id="totalCustomers">Loading...</div>
                            <div class="metric-label">Total Customers</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">⏱️</div>
                        <div class="metric-content">
                            <div class="metric-value" id="avgResponseTime">Loading...</div>
                            <div class="metric-label">Avg Response Time</div>
                        </div>
                    </div>
                </div>

                <div class="additional-metrics">
                    <div class="metric-card">
                        <div class="metric-icon">📈</div>
                        <div class="metric-content">
                            <div class="metric-value" id="responseRate">Loading...</div>
                            <div class="metric-label">Response Rate</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">📤</div>
                        <div class="metric-content">
                            <div class="metric-value" id="messageVolume">Loading...</div>
                            <div class="metric-label">Message Volume</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">✅</div>
                        <div class="metric-content">
                            <div class="metric-value" id="deliveryRate">Loading...</div>
                            <div class="metric-label">Delivery Rate</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🎯</div>
                        <div class="metric-content">
                            <div class="metric-value" id="agentPerformance">Loading...</div>
                            <div class="metric-label">Resolved Conversations</div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-charts">
                    <div class="chart-container">
                        <h3>Real-time Analytics</h3>
                        <div class="chart-placeholder">
                            <p>📊 Live data from your backend API</p>
                            <p id="dataSourceInfo">Loading data source...</p>
                        </div>
                    </div>
                </div>
            `;

            // Load all dashboard data
            loadDashboardMetrics();
            loadAdditionalMetrics();
        }

        // Settings content (matching your screenshot)
        function loadSettings() {
            contentArea.innerHTML = `
                <div class="settings-layout">
                    <div class="settings-sidebar">
                        <button class="settings-nav-item active" onclick="showSettingsSection('integrations')">
                            Integrations
                        </button>
                        <button class="settings-nav-item" onclick="showSettingsSection('general')">
                            General
                        </button>
                        <button class="settings-nav-item" onclick="showSettingsSection('notifications')">
                            Notifications
                        </button>
                        <button class="settings-nav-item" onclick="showSettingsSection('security')">
                            Security
                        </button>
                    </div>
                    <div class="settings-content" id="settingsContent">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            `;
            showSettingsSection('integrations');
        }

        // Login form handler
        loginForm.addEventListener("submit", async (e) => {
            e.preventDefault();

            const email = document.getElementById("email").value;
            const password = document.getElementById("password").value;
            const loginBtn = document.getElementById("loginBtn");

            loginBtn.disabled = true;
            loginBtn.textContent = "Logging in...";

            try {
                const response = await fetch(`${API_BASE}/auth/token`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    credentials: "include",
                    body: JSON.stringify({ email, password }),
                });

                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.user;
                    showMessage("✅ Login successful! Opening platform...");
                    loginForm.reset();

                    // Show the platform dashboard
                    setTimeout(() => {
                        showPlatform();
                    }, 1000);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    showMessage(`❌ ${errorData.detail || "Login failed"}`, true);
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, true);
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = "Login";
            }
        });

        // Additional content loading functions
        function loadConversations() {
            contentArea.innerHTML = `
                <div class="conversations-header">
                    <h1>💬 Conversations</h1>
                    <p>Manage all your customer conversations across channels</p>
                </div>

                <div class="conversations-filters">
                    <div class="filter-group">
                        <select id="statusFilter" onchange="filterConversations()">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="archived">Archived</option>
                            <option value="spam">Spam</option>
                        </select>

                        <select id="channelFilter" onchange="filterConversations()">
                            <option value="">All Channels</option>
                            <option value="whatsapp">WhatsApp</option>
                            <option value="telegram">Telegram</option>
                            <option value="email">Email</option>
                        </select>

                        <label>
                            <input type="checkbox" id="unreadOnlyFilter" onchange="filterConversations()">
                            Unread only
                        </label>
                    </div>

                    <div class="search-group">
                        <input type="text" id="searchInput" placeholder="Search conversations..." onkeyup="searchConversations()">
                        <button class="advanced-search-btn" onclick="openAdvancedSearch()" title="Advanced Search">
                            🔍+
                        </button>
                        <button class="bulk-mode-toggle" id="conversationBulkToggle" onclick="toggleConversationBulkMode()">
                            📋 Bulk Select
                        </button>
                    </div>
                </div>

                <!-- Bulk Operations Toolbar -->
                <div class="bulk-operations-toolbar" id="conversationBulkToolbar">
                    <div class="bulk-selection-info">
                        <input type="checkbox" class="select-all-checkbox" id="selectAllConversations" onchange="toggleSelectAllConversations()">
                        <span id="selectedConversationsCount">0 selected</span>
                    </div>
                    <div class="bulk-actions">
                        <button class="bulk-action-btn" onclick="bulkMarkAsRead()">
                            ✓ Mark as Read
                        </button>
                        <button class="bulk-action-btn" onclick="bulkArchive()">
                            📁 Archive
                        </button>
                        <button class="bulk-action-btn" onclick="bulkAssignTags()">
                            🏷️ Add Tags
                        </button>
                        <button class="bulk-action-btn" onclick="bulkAssign()">
                            👤 Assign
                        </button>
                        <button class="bulk-action-btn danger" onclick="bulkDelete()">
                            🗑️ Delete
                        </button>
                    </div>
                </div>

                <div class="conversations-container">
                    <div id="conversationsList" class="conversations-list">
                        <div class="loading-state">
                            <p>Loading conversations...</p>
                        </div>
                    </div>
                </div>
            `;

            // Load conversations data
            fetchConversations();
        }

        // Contacts content
        function loadContacts() {
            contentArea.innerHTML = `
                <div class="contacts-header">
                    <h1>👥 Contacts</h1>
                    <p>Manage your customer contacts and their information</p>
                </div>

                <div class="contacts-filters">
                    <div class="filter-group">
                        <select id="contactChannelFilter">
                            <option value="">All Channels</option>
                            <option value="whatsapp">WhatsApp</option>
                            <option value="telegram">Telegram</option>
                            <option value="email">Email</option>
                            <option value="sms">SMS</option>
                        </select>

                        <select id="contactStatusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="blocked">Blocked</option>
                            <option value="inactive">Inactive</option>
                        </select>

                        <label>
                            <input type="checkbox" id="recentContactsOnly"> Recent contacts only
                        </label>
                    </div>

                    <div class="search-group">
                        <input type="text" id="contactSearch" placeholder="Search contacts..." />
                        <button class="advanced-search-btn" onclick="openAdvancedContactSearch()" title="Advanced Contact Search">
                            🔍+
                        </button>
                        <button class="bulk-mode-toggle" id="contactBulkToggle" onclick="toggleContactBulkMode()">
                            📋 Bulk Select
                        </button>
                        <button onclick="addNewContact()" style="background: #4299e1; color: white; border: none; padding: 8px 16px; border-radius: 6px; margin-left: 10px;">
                            + Add Contact
                        </button>
                    </div>
                </div>

                <!-- Bulk Operations Toolbar for Contacts -->
                <div class="bulk-operations-toolbar" id="contactBulkToolbar">
                    <div class="bulk-selection-info">
                        <input type="checkbox" class="select-all-checkbox" id="selectAllContacts" onchange="toggleSelectAllContacts()">
                        <span id="selectedContactsCount">0 selected</span>
                    </div>
                    <div class="bulk-actions">
                        <button class="bulk-action-btn" onclick="bulkUpdateContactStatus()">
                            ✓ Update Status
                        </button>
                        <button class="bulk-action-btn" onclick="bulkAddContactTags()">
                            🏷️ Add Tags
                        </button>
                        <button class="bulk-action-btn" onclick="bulkExportContacts()">
                            📤 Export
                        </button>
                        <button class="bulk-action-btn" onclick="bulkSendMessage()">
                            💬 Send Message
                        </button>
                        <button class="bulk-action-btn danger" onclick="bulkDeleteContacts()">
                            🗑️ Delete
                        </button>
                    </div>
                </div>

                <div class="contacts-container">
                    <div class="contacts-list" id="contactsList">
                        <div class="loading-state">Loading contacts...</div>
                    </div>
                </div>
            `;

            // Add event listeners for filters
            document.getElementById('contactChannelFilter').addEventListener('change', filterContacts);
            document.getElementById('contactStatusFilter').addEventListener('change', filterContacts);
            document.getElementById('recentContactsOnly').addEventListener('change', filterContacts);
            document.getElementById('contactSearch').addEventListener('input', searchContacts);

            // Load contacts data
            fetchContacts();
        }

        function loadInboxes() {
            contentArea.innerHTML = `
                <h2>📥 Inboxes</h2>
                <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e2e8f0;">
                    <p>No inboxes configured.</p>
                </div>
            `;
        }

        function loadBotBuilder() {
            contentArea.innerHTML = `
                <h2>🤖 Bot Builder</h2>
                <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e2e8f0;">
                    <p>Bot builder coming soon...</p>
                </div>
            `;
        }

        // Contacts API and management functions
        async function fetchContacts(filters = {}) {
            try {
                // For now, we'll use mock data since there's no dedicated customer endpoint yet
                // TODO: Replace with real API call when customer endpoint is available
                // const params = new URLSearchParams();
                // if (filters.channel) params.append('channel', filters.channel);
                // if (filters.status) params.append('status', filters.status);
                // if (filters.search) params.append('search', filters.search);
                // if (filters.recent_only) params.append('recent_only', 'true');

                // const response = await fetch(`${API_BASE}/api/customers/?${params}`, {
                //     credentials: "include",
                //     headers: { "Content-Type": "application/json" }
                // });

                // if (response.ok) {
                //     const contacts = await response.json();
                //     displayContacts(contacts);
                // } else {
                //     throw new Error('Failed to fetch contacts');
                // }

                // For now, use mock data
                console.log("Loading contacts with mock data (real API endpoint not yet available)");
                const mockContacts = getMockContacts();
                displayContacts(mockContacts);

            } catch (error) {
                console.error('Error fetching contacts:', error);
                // Fallback to mock data
                const mockContacts = getMockContacts();
                displayContacts(mockContacts);
            }
        }

        function displayContacts(contacts) {
            const contactsList = document.getElementById('contactsList');

            if (!contacts || contacts.length === 0) {
                contactsList.innerHTML = `
                    <div class="empty-state">
                        <h3>No contacts found</h3>
                        <small>Try adjusting your filters or add a new contact</small>
                    </div>
                `;
                return;
            }

            const isBulkMode = document.getElementById('contactBulkToggle')?.classList.contains('active') || false;

            contactsList.innerHTML = contacts.map(contact => `
                <div class="contact-item ${isBulkMode ? 'bulk-mode' : ''}"
                     onclick="${isBulkMode ? `toggleContactSelection('${contact.id}')` : `openContact('${contact.id}')`}"
                     data-contact-id="${contact.id}">
                    ${isBulkMode ? `<input type="checkbox" class="bulk-checkbox" onclick="event.stopPropagation(); toggleContactSelection('${contact.id}')" data-contact-id="${contact.id}">` : ''}>
                    <div class="contact-avatar">
                        <div class="avatar-circle ${contact.primary_channel}">
                            ${getChannelIcon(contact.primary_channel)}
                        </div>
                    </div>
                    <div class="contact-content">
                        <div class="contact-header">
                            <h4>${contact.name}</h4>
                            <span class="contact-time">${formatTime(contact.last_contact)}</span>
                        </div>
                        <div class="contact-details">
                            <p>${contact.phone || contact.email || 'No contact info'}</p>
                            <div class="contact-meta">
                                <span class="channel-badge ${contact.primary_channel}">${contact.primary_channel}</span>
                                <span class="status-badge ${contact.status}">${contact.status}</span>
                                ${contact.unread_messages > 0 ? `<span class="unread-badge">${contact.unread_messages}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function getMockContacts() {
            return [
                {
                    id: '1',
                    name: 'John Smith',
                    phone: '+1234567890',
                    email: '<EMAIL>',
                    primary_channel: 'whatsapp',
                    status: 'active',
                    last_contact: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
                    unread_messages: 2,
                    total_conversations: 5
                },
                {
                    id: '2',
                    name: 'Sarah Johnson',
                    phone: '+1987654321',
                    email: '<EMAIL>',
                    primary_channel: 'telegram',
                    status: 'active',
                    last_contact: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                    unread_messages: 0,
                    total_conversations: 3
                },
                {
                    id: '3',
                    name: 'Mike Wilson',
                    phone: null,
                    email: '<EMAIL>',
                    primary_channel: 'email',
                    status: 'active',
                    last_contact: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
                    unread_messages: 1,
                    total_conversations: 8
                },
                {
                    id: '4',
                    name: 'Emma Davis',
                    phone: '+1555123456',
                    email: null,
                    primary_channel: 'sms',
                    status: 'inactive',
                    last_contact: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
                    unread_messages: 0,
                    total_conversations: 1
                },
                {
                    id: '5',
                    name: 'Alex Chen',
                    phone: '+1444555666',
                    email: '<EMAIL>',
                    primary_channel: 'whatsapp',
                    status: 'blocked',
                    last_contact: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
                    unread_messages: 0,
                    total_conversations: 12
                }
            ];
        }

        function filterContacts() {
            const channelFilter = document.getElementById('contactChannelFilter').value;
            const statusFilter = document.getElementById('contactStatusFilter').value;
            const recentOnly = document.getElementById('recentContactsOnly').checked;

            const filters = {};
            if (channelFilter) filters.channel = channelFilter;
            if (statusFilter) filters.status = statusFilter;
            if (recentOnly) filters.recent_only = true;

            fetchContacts(filters);
        }

        function searchContacts() {
            const searchTerm = document.getElementById('contactSearch').value;
            const filters = {};
            if (searchTerm) filters.search = searchTerm;

            fetchContacts(filters);
        }

        // Advanced Contact Search
        function openAdvancedContactSearch() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content advanced-search-modal">
                    <div class="modal-header">
                        <h3>👥 Advanced Contact Search</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>

                    <div class="modal-body">
                        <!-- Quick Filters -->
                        <div class="form-group">
                            <label>Quick Filters</label>
                            <div class="quick-filters">
                                <button class="quick-filter-btn" data-filter="recent" onclick="applyContactQuickFilter('recent')">Recent</button>
                                <button class="quick-filter-btn" data-filter="active" onclick="applyContactQuickFilter('active')">Active</button>
                                <button class="quick-filter-btn" data-filter="whatsapp" onclick="applyContactQuickFilter('whatsapp')">WhatsApp</button>
                                <button class="quick-filter-btn" data-filter="sms" onclick="applyContactQuickFilter('sms')">SMS</button>
                                <button class="quick-filter-btn" data-filter="email" onclick="applyContactQuickFilter('email')">Email</button>
                                <button class="quick-filter-btn" data-filter="vip" onclick="applyContactQuickFilter('vip')">VIP</button>
                            </div>
                        </div>

                        <!-- Search Criteria -->
                        <div class="search-criteria-grid">
                            <div class="form-group">
                                <label for="advancedContactName">Name</label>
                                <input type="text" id="advancedContactName" placeholder="Search by name...">
                            </div>

                            <div class="form-group">
                                <label for="advancedContactPhone">Phone Number</label>
                                <input type="text" id="advancedContactPhone" placeholder="Search by phone...">
                            </div>

                            <div class="form-group">
                                <label for="advancedContactEmail">Email</label>
                                <input type="text" id="advancedContactEmail" placeholder="Search by email...">
                            </div>

                            <div class="form-group">
                                <label for="advancedContactChannel">Preferred Channel</label>
                                <select id="advancedContactChannel">
                                    <option value="">All Channels</option>
                                    <option value="whatsapp">WhatsApp</option>
                                    <option value="sms">SMS</option>
                                    <option value="email">Email</option>
                                    <option value="telegram">Telegram</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="advancedContactStatus">Status</label>
                                <select id="advancedContactStatus">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="blocked">Blocked</option>
                                    <option value="vip">VIP</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="advancedContactTags">Tags</label>
                                <input type="text" id="advancedContactTags" placeholder="Search by tags (comma separated)...">
                            </div>

                            <div class="form-group search-criteria-full">
                                <label>Last Contact Date</label>
                                <div class="date-range-group">
                                    <input type="date" id="contactDateFrom" placeholder="From">
                                    <span>to</span>
                                    <input type="date" id="contactDateTo" placeholder="To">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="advancedContactLocation">Location</label>
                                <input type="text" id="advancedContactLocation" placeholder="City, Country...">
                            </div>

                            <div class="form-group">
                                <label for="advancedContactSource">Source</label>
                                <select id="advancedContactSource">
                                    <option value="">Any Source</option>
                                    <option value="website">Website</option>
                                    <option value="social">Social Media</option>
                                    <option value="referral">Referral</option>
                                    <option value="campaign">Campaign</option>
                                    <option value="manual">Manual Entry</option>
                                </select>
                            </div>
                        </div>

                        <!-- Search Results Summary -->
                        <div id="contactSearchResultsSummary" class="search-results-summary" style="display: none;">
                            <span id="contactResultsCount">0</span> contacts found
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="clearAdvancedContactSearch()">Clear All</button>
                        <button class="btn btn-primary" onclick="executeAdvancedContactSearch()">Search</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Set default date range (last 90 days)
            const today = new Date();
            const ninetyDaysAgo = new Date(today.getTime() - (90 * 24 * 60 * 60 * 1000));
            document.getElementById('contactDateTo').value = today.toISOString().split('T')[0];
            document.getElementById('contactDateFrom').value = ninetyDaysAgo.toISOString().split('T')[0];
        }

        function applyContactQuickFilter(filterType) {
            // Remove active class from all quick filter buttons
            document.querySelectorAll('.quick-filter-btn').forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            event.target.classList.add('active');

            switch(filterType) {
                case 'recent':
                    const today = new Date();
                    const weekAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
                    document.getElementById('contactDateFrom').value = weekAgo.toISOString().split('T')[0];
                    document.getElementById('contactDateTo').value = today.toISOString().split('T')[0];
                    break;
                case 'active':
                    document.getElementById('advancedContactStatus').value = 'active';
                    break;
                case 'whatsapp':
                    document.getElementById('advancedContactChannel').value = 'whatsapp';
                    break;
                case 'sms':
                    document.getElementById('advancedContactChannel').value = 'sms';
                    break;
                case 'email':
                    document.getElementById('advancedContactChannel').value = 'email';
                    break;
                case 'vip':
                    document.getElementById('advancedContactStatus').value = 'vip';
                    break;
            }
        }

        function clearAdvancedContactSearch() {
            // Clear all form fields
            document.getElementById('advancedContactName').value = '';
            document.getElementById('advancedContactPhone').value = '';
            document.getElementById('advancedContactEmail').value = '';
            document.getElementById('advancedContactChannel').value = '';
            document.getElementById('advancedContactStatus').value = '';
            document.getElementById('advancedContactTags').value = '';
            document.getElementById('advancedContactLocation').value = '';
            document.getElementById('advancedContactSource').value = '';

            // Reset date range to last 90 days
            const today = new Date();
            const ninetyDaysAgo = new Date(today.getTime() - (90 * 24 * 60 * 60 * 1000));
            document.getElementById('contactDateTo').value = today.toISOString().split('T')[0];
            document.getElementById('contactDateFrom').value = ninetyDaysAgo.toISOString().split('T')[0];

            // Remove active class from quick filter buttons
            document.querySelectorAll('.quick-filter-btn').forEach(btn => btn.classList.remove('active'));

            // Hide results summary
            document.getElementById('contactSearchResultsSummary').style.display = 'none';
        }

        async function executeAdvancedContactSearch() {
            const searchCriteria = {
                name: document.getElementById('advancedContactName').value,
                phone: document.getElementById('advancedContactPhone').value,
                email: document.getElementById('advancedContactEmail').value,
                channel: document.getElementById('advancedContactChannel').value,
                status: document.getElementById('advancedContactStatus').value,
                tags: document.getElementById('advancedContactTags').value,
                location: document.getElementById('advancedContactLocation').value,
                source: document.getElementById('advancedContactSource').value,
                date_from: document.getElementById('contactDateFrom').value,
                date_to: document.getElementById('contactDateTo').value
            };

            // Check for recent filter from quick filters
            const recentFilter = document.querySelector('.quick-filter-btn[data-filter="recent"].active');
            if (recentFilter) {
                searchCriteria.recent_only = true;
            }

            // Remove empty criteria
            Object.keys(searchCriteria).forEach(key => {
                if (!searchCriteria[key]) {
                    delete searchCriteria[key];
                }
            });

            try {
                // Execute search
                await fetchContacts(searchCriteria);

                // Show results summary
                const resultsCount = document.querySelectorAll('.contact-item').length;
                document.getElementById('contactResultsCount').textContent = resultsCount;
                document.getElementById('contactSearchResultsSummary').style.display = 'block';

                // Close modal
                closeModal();

                // Update main search input to show active search
                const mainSearchInput = document.getElementById('contactSearch');
                if (searchCriteria.name) {
                    mainSearchInput.value = searchCriteria.name;
                } else if (searchCriteria.phone) {
                    mainSearchInput.value = searchCriteria.phone;
                } else if (searchCriteria.email) {
                    mainSearchInput.value = searchCriteria.email;
                }

                // Show search summary in contacts section
                showContactSearchSummary(searchCriteria, resultsCount);

            } catch (error) {
                console.error('Advanced contact search failed:', error);
                alert('Contact search failed. Please try again.');
            }
        }

        function showContactSearchSummary(criteria, resultsCount) {
            // Remove existing search summary
            const existingSummary = document.querySelector('.contact-search-summary');
            if (existingSummary) {
                existingSummary.remove();
            }

            // Create search summary
            const summary = document.createElement('div');
            summary.className = 'search-summary contact-search-summary';
            summary.innerHTML = `
                <div class="search-summary-content">
                    <span class="search-results-text">
                        <strong>${resultsCount}</strong> contacts found
                    </span>
                    <div class="active-filters">
                        ${Object.entries(criteria).map(([key, value]) => {
                            if (key === 'recent_only') return '<span class="filter-tag">Recent Only</span>';
                            if (key === 'date_from' || key === 'date_to') return '';
                            return `<span class="filter-tag">${key}: ${value}</span>`;
                        }).filter(Boolean).join('')}
                        ${criteria.date_from && criteria.date_to ?
                            `<span class="filter-tag">Date: ${criteria.date_from} to ${criteria.date_to}</span>` : ''}
                    </div>
                    <button class="clear-search-btn" onclick="clearActiveContactSearch()">Clear Search</button>
                </div>
            `;

            // Insert summary before contacts list
            const contactsContainer = document.querySelector('.contacts-container');
            contactsContainer.insertBefore(summary, contactsContainer.firstChild);
        }

        function clearActiveContactSearch() {
            // Remove search summary
            const searchSummary = document.querySelector('.contact-search-summary');
            if (searchSummary) {
                searchSummary.remove();
            }

            // Clear main search input
            document.getElementById('contactSearch').value = '';

            // Reset filters and reload contacts
            document.getElementById('contactChannelFilter').value = '';
            document.getElementById('contactStatusFilter').value = '';
            document.getElementById('recentContactsOnly').checked = false;

            // Reload all contacts
            fetchContacts();
        }

        // Bulk Operations Functions
        let selectedConversations = new Set();
        let selectedContacts = new Set();

        function toggleConversationBulkMode() {
            const toggle = document.getElementById('conversationBulkToggle');
            const toolbar = document.getElementById('conversationBulkToolbar');

            toggle.classList.toggle('active');

            if (toggle.classList.contains('active')) {
                toggle.textContent = '❌ Exit Bulk';
                toolbar.classList.add('show');
            } else {
                toggle.textContent = '📋 Bulk Select';
                toolbar.classList.remove('show');
                selectedConversations.clear();
                updateSelectedConversationsCount();
            }

            // Re-render conversations to show/hide checkboxes
            const currentConversations = Array.from(document.querySelectorAll('.conversation-item')).map(item => ({
                id: item.dataset.conversationId
            }));

            if (currentConversations.length > 0) {
                // Trigger re-render by calling fetchConversations
                fetchConversations();
            }
        }

        function toggleContactBulkMode() {
            const toggle = document.getElementById('contactBulkToggle');
            const toolbar = document.getElementById('contactBulkToolbar');

            toggle.classList.toggle('active');

            if (toggle.classList.contains('active')) {
                toggle.textContent = '❌ Exit Bulk';
                toolbar.classList.add('show');
            } else {
                toggle.textContent = '📋 Bulk Select';
                toolbar.classList.remove('show');
                selectedContacts.clear();
                updateSelectedContactsCount();
            }

            // Re-render contacts to show/hide checkboxes
            const currentContacts = Array.from(document.querySelectorAll('.contact-item')).map(item => ({
                id: item.dataset.contactId
            }));

            if (currentContacts.length > 0) {
                // Trigger re-render by calling fetchContacts
                fetchContacts();
            }
        }

        function toggleConversationSelection(conversationId) {
            if (selectedConversations.has(conversationId)) {
                selectedConversations.delete(conversationId);
            } else {
                selectedConversations.add(conversationId);
            }

            // Update checkbox state
            const checkbox = document.querySelector(`input[data-conversation-id="${conversationId}"]`);
            if (checkbox) {
                checkbox.checked = selectedConversations.has(conversationId);
            }

            // Update conversation item styling
            const conversationItem = document.querySelector(`div[data-conversation-id="${conversationId}"]`);
            if (conversationItem) {
                if (selectedConversations.has(conversationId)) {
                    conversationItem.classList.add('selected');
                } else {
                    conversationItem.classList.remove('selected');
                }
            }

            updateSelectedConversationsCount();
            updateSelectAllConversationsState();
        }

        function toggleContactSelection(contactId) {
            if (selectedContacts.has(contactId)) {
                selectedContacts.delete(contactId);
            } else {
                selectedContacts.add(contactId);
            }

            // Update checkbox state
            const checkbox = document.querySelector(`input[data-contact-id="${contactId}"]`);
            if (checkbox) {
                checkbox.checked = selectedContacts.has(contactId);
            }

            // Update contact item styling
            const contactItem = document.querySelector(`div[data-contact-id="${contactId}"]`);
            if (contactItem) {
                if (selectedContacts.has(contactId)) {
                    contactItem.classList.add('selected');
                } else {
                    contactItem.classList.remove('selected');
                }
            }

            updateSelectedContactsCount();
            updateSelectAllContactsState();
        }

        function toggleSelectAllConversations() {
            const selectAllCheckbox = document.getElementById('selectAllConversations');
            const conversationCheckboxes = document.querySelectorAll('input[data-conversation-id]');

            if (selectAllCheckbox.checked) {
                // Select all
                conversationCheckboxes.forEach(checkbox => {
                    const conversationId = checkbox.dataset.conversationId;
                    selectedConversations.add(conversationId);
                    checkbox.checked = true;

                    const conversationItem = document.querySelector(`div[data-conversation-id="${conversationId}"]`);
                    if (conversationItem) {
                        conversationItem.classList.add('selected');
                    }
                });
            } else {
                // Deselect all
                conversationCheckboxes.forEach(checkbox => {
                    const conversationId = checkbox.dataset.conversationId;
                    selectedConversations.delete(conversationId);
                    checkbox.checked = false;

                    const conversationItem = document.querySelector(`div[data-conversation-id="${conversationId}"]`);
                    if (conversationItem) {
                        conversationItem.classList.remove('selected');
                    }
                });
            }

            updateSelectedConversationsCount();
        }

        function toggleSelectAllContacts() {
            const selectAllCheckbox = document.getElementById('selectAllContacts');
            const contactCheckboxes = document.querySelectorAll('input[data-contact-id]');

            if (selectAllCheckbox.checked) {
                // Select all
                contactCheckboxes.forEach(checkbox => {
                    const contactId = checkbox.dataset.contactId;
                    selectedContacts.add(contactId);
                    checkbox.checked = true;

                    const contactItem = document.querySelector(`div[data-contact-id="${contactId}"]`);
                    if (contactItem) {
                        contactItem.classList.add('selected');
                    }
                });
            } else {
                // Deselect all
                contactCheckboxes.forEach(checkbox => {
                    const contactId = checkbox.dataset.contactId;
                    selectedContacts.delete(contactId);
                    checkbox.checked = false;

                    const contactItem = document.querySelector(`div[data-contact-id="${contactId}"]`);
                    if (contactItem) {
                        contactItem.classList.remove('selected');
                    }
                });
            }

            updateSelectedContactsCount();
        }

        function updateSelectedConversationsCount() {
            const count = selectedConversations.size;
            const countElement = document.getElementById('selectedConversationsCount');
            if (countElement) {
                countElement.textContent = `${count} selected`;
            }
        }

        function updateSelectedContactsCount() {
            const count = selectedContacts.size;
            const countElement = document.getElementById('selectedContactsCount');
            if (countElement) {
                countElement.textContent = `${count} selected`;
            }
        }

        function updateSelectAllConversationsState() {
            const selectAllCheckbox = document.getElementById('selectAllConversations');
            const totalCheckboxes = document.querySelectorAll('input[data-conversation-id]').length;

            if (selectAllCheckbox && totalCheckboxes > 0) {
                selectAllCheckbox.checked = selectedConversations.size === totalCheckboxes;
                selectAllCheckbox.indeterminate = selectedConversations.size > 0 && selectedConversations.size < totalCheckboxes;
            }
        }

        function updateSelectAllContactsState() {
            const selectAllCheckbox = document.getElementById('selectAllContacts');
            const totalCheckboxes = document.querySelectorAll('input[data-contact-id]').length;

            if (selectAllCheckbox && totalCheckboxes > 0) {
                selectAllCheckbox.checked = selectedContacts.size === totalCheckboxes;
                selectAllCheckbox.indeterminate = selectedContacts.size > 0 && selectedContacts.size < totalCheckboxes;
            }
        }

        // Bulk Action Functions for Conversations
        async function bulkMarkAsRead() {
            if (selectedConversations.size === 0) {
                alert('Please select conversations to mark as read.');
                return;
            }

            if (!confirm(`Mark ${selectedConversations.size} conversation(s) as read?`)) {
                return;
            }

            try {
                const conversationIds = Array.from(selectedConversations);

                // Call API to mark conversations as read
                const response = await fetch('/api/api/conversations/bulk-mark-read', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ conversation_ids: conversationIds })
                });

                if (response.ok) {
                    alert(`${conversationIds.length} conversation(s) marked as read.`);
                    selectedConversations.clear();
                    updateSelectedConversationsCount();
                    fetchConversations(); // Refresh the list
                } else {
                    throw new Error('Failed to mark conversations as read');
                }
            } catch (error) {
                console.error('Bulk mark as read failed:', error);
                alert('Failed to mark conversations as read. Please try again.');
            }
        }

        async function bulkArchive() {
            if (selectedConversations.size === 0) {
                alert('Please select conversations to archive.');
                return;
            }

            if (!confirm(`Archive ${selectedConversations.size} conversation(s)?`)) {
                return;
            }

            try {
                const conversationIds = Array.from(selectedConversations);

                const response = await fetch('/api/api/conversations/bulk-archive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ conversation_ids: conversationIds })
                });

                if (response.ok) {
                    alert(`${conversationIds.length} conversation(s) archived.`);
                    selectedConversations.clear();
                    updateSelectedConversationsCount();
                    fetchConversations();
                } else {
                    throw new Error('Failed to archive conversations');
                }
            } catch (error) {
                console.error('Bulk archive failed:', error);
                alert('Failed to archive conversations. Please try again.');
            }
        }

        async function bulkAssignTags() {
            if (selectedConversations.size === 0) {
                alert('Please select conversations to tag.');
                return;
            }

            const tags = prompt(`Enter tags for ${selectedConversations.size} conversation(s) (comma-separated):`);
            if (!tags) return;

            try {
                const conversationIds = Array.from(selectedConversations);
                const tagList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);

                const response = await fetch('/api/api/conversations/bulk-assign-tags', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        conversation_ids: conversationIds,
                        tags: tagList
                    })
                });

                if (response.ok) {
                    alert(`Tags assigned to ${conversationIds.length} conversation(s).`);
                    selectedConversations.clear();
                    updateSelectedConversationsCount();
                    fetchConversations();
                } else {
                    throw new Error('Failed to assign tags');
                }
            } catch (error) {
                console.error('Bulk assign tags failed:', error);
                alert('Failed to assign tags. Please try again.');
            }
        }

        async function bulkAssign() {
            if (selectedConversations.size === 0) {
                alert('Please select conversations to assign.');
                return;
            }

            const assignee = prompt(`Enter assignee for ${selectedConversations.size} conversation(s):`);
            if (!assignee) return;

            try {
                const conversationIds = Array.from(selectedConversations);

                const response = await fetch('/api/api/conversations/bulk-assign', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        conversation_ids: conversationIds,
                        assignee: assignee
                    })
                });

                if (response.ok) {
                    alert(`${conversationIds.length} conversation(s) assigned to ${assignee}.`);
                    selectedConversations.clear();
                    updateSelectedConversationsCount();
                    fetchConversations();
                } else {
                    throw new Error('Failed to assign conversations');
                }
            } catch (error) {
                console.error('Bulk assign failed:', error);
                alert('Failed to assign conversations. Please try again.');
            }
        }

        async function bulkDelete() {
            if (selectedConversations.size === 0) {
                alert('Please select conversations to delete.');
                return;
            }

            if (!confirm(`⚠️ DELETE ${selectedConversations.size} conversation(s)? This action cannot be undone!`)) {
                return;
            }

            try {
                const conversationIds = Array.from(selectedConversations);

                const response = await fetch('/api/api/conversations/bulk-delete', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ conversation_ids: conversationIds })
                });

                if (response.ok) {
                    alert(`${conversationIds.length} conversation(s) deleted.`);
                    selectedConversations.clear();
                    updateSelectedConversationsCount();
                    fetchConversations();
                } else {
                    throw new Error('Failed to delete conversations');
                }
            } catch (error) {
                console.error('Bulk delete failed:', error);
                alert('Failed to delete conversations. Please try again.');
            }
        }

        // Bulk Action Functions for Contacts
        async function bulkUpdateContactStatus() {
            if (selectedContacts.size === 0) {
                alert('Please select contacts to update status.');
                return;
            }

            const status = prompt(`Enter new status for ${selectedContacts.size} contact(s) (active/inactive/blocked/vip):`);
            if (!status || !['active', 'inactive', 'blocked', 'vip'].includes(status.toLowerCase())) {
                alert('Please enter a valid status: active, inactive, blocked, or vip');
                return;
            }

            try {
                const contactIds = Array.from(selectedContacts);

                const response = await fetch('/api/api/contacts/bulk-update-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        contact_ids: contactIds,
                        status: status.toLowerCase()
                    })
                });

                if (response.ok) {
                    alert(`${contactIds.length} contact(s) status updated to ${status}.`);
                    selectedContacts.clear();
                    updateSelectedContactsCount();
                    fetchContacts();
                } else {
                    throw new Error('Failed to update contact status');
                }
            } catch (error) {
                console.error('Bulk update contact status failed:', error);
                alert('Failed to update contact status. Please try again.');
            }
        }

        async function bulkAddContactTags() {
            if (selectedContacts.size === 0) {
                alert('Please select contacts to tag.');
                return;
            }

            const tags = prompt(`Enter tags for ${selectedContacts.size} contact(s) (comma-separated):`);
            if (!tags) return;

            try {
                const contactIds = Array.from(selectedContacts);
                const tagList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);

                const response = await fetch('/api/api/contacts/bulk-add-tags', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        contact_ids: contactIds,
                        tags: tagList
                    })
                });

                if (response.ok) {
                    alert(`Tags added to ${contactIds.length} contact(s).`);
                    selectedContacts.clear();
                    updateSelectedContactsCount();
                    fetchContacts();
                } else {
                    throw new Error('Failed to add tags to contacts');
                }
            } catch (error) {
                console.error('Bulk add contact tags failed:', error);
                alert('Failed to add tags to contacts. Please try again.');
            }
        }

        async function bulkExportContacts() {
            if (selectedContacts.size === 0) {
                alert('Please select contacts to export.');
                return;
            }

            try {
                const contactIds = Array.from(selectedContacts);

                const response = await fetch('/api/api/contacts/bulk-export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ contact_ids: contactIds })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `contacts_export_${new Date().toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    alert(`${contactIds.length} contact(s) exported successfully.`);
                } else {
                    throw new Error('Failed to export contacts');
                }
            } catch (error) {
                console.error('Bulk export contacts failed:', error);
                alert('Failed to export contacts. Please try again.');
            }
        }

        async function bulkSendMessage() {
            if (selectedContacts.size === 0) {
                alert('Please select contacts to send message.');
                return;
            }

            const message = prompt(`Enter message to send to ${selectedContacts.size} contact(s):`);
            if (!message) return;

            if (!confirm(`Send message to ${selectedContacts.size} contact(s)?`)) {
                return;
            }

            try {
                const contactIds = Array.from(selectedContacts);

                const response = await fetch('/api/api/contacts/bulk-send-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        contact_ids: contactIds,
                        message: message
                    })
                });

                if (response.ok) {
                    alert(`Message sent to ${contactIds.length} contact(s).`);
                    selectedContacts.clear();
                    updateSelectedContactsCount();
                } else {
                    throw new Error('Failed to send bulk message');
                }
            } catch (error) {
                console.error('Bulk send message failed:', error);
                alert('Failed to send bulk message. Please try again.');
            }
        }

        async function bulkDeleteContacts() {
            if (selectedContacts.size === 0) {
                alert('Please select contacts to delete.');
                return;
            }

            if (!confirm(`⚠️ DELETE ${selectedContacts.size} contact(s)? This action cannot be undone!`)) {
                return;
            }

            try {
                const contactIds = Array.from(selectedContacts);

                const response = await fetch('/api/api/contacts/bulk-delete', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ contact_ids: contactIds })
                });

                if (response.ok) {
                    alert(`${contactIds.length} contact(s) deleted.`);
                    selectedContacts.clear();
                    updateSelectedContactsCount();
                    fetchContacts();
                } else {
                    throw new Error('Failed to delete contacts');
                }
            } catch (error) {
                console.error('Bulk delete contacts failed:', error);
                alert('Failed to delete contacts. Please try again.');
            }
        }

        // Enhanced Message Threading Functions
        function renderMessagesWithThreading(messages) {
            // Group messages by thread
            const messageThreads = groupMessagesByThread(messages);

            return messageThreads.map(thread => {
                if (thread.replies && thread.replies.length > 0) {
                    return renderMessageThread(thread);
                } else {
                    return renderSingleMessage(thread.message);
                }
            }).join('');
        }

        function groupMessagesByThread(messages) {
            const threads = [];
            const messageMap = new Map();

            // First pass: create message map
            messages.forEach(message => {
                messageMap.set(message.id, {
                    message: message,
                    replies: []
                });
            });

            // Second pass: group replies
            messages.forEach(message => {
                if (message.reply_to_id && messageMap.has(message.reply_to_id)) {
                    messageMap.get(message.reply_to_id).replies.push(message);
                } else if (!message.reply_to_id) {
                    threads.push(messageMap.get(message.id));
                }
            });

            return threads;
        }

        function renderSingleMessage(message) {
            const isOutgoing = message.direction === 'outgoing';
            const messageId = message.id;

            return `
                <div class="message ${isOutgoing ? 'message-sent' : 'message-received'}" data-message-id="${messageId}">
                    ${message.reply_to_id ? renderReplyContext(message.reply_to_id) : ''}
                    <div class="message-content">
                        <div class="message-text">${message.content}</div>
                        <div class="message-meta">
                            <span class="message-time">${new Date(message.created_at).toLocaleTimeString()}</span>
                            ${isOutgoing ? `<span class="message-status">${message.status}</span>` : ''}
                        </div>
                    </div>
                    <div class="message-actions">
                        <button class="message-action-btn" onclick="replyToMessage('${messageId}', '${message.content.substring(0, 50)}...')">
                            ↩️ Reply
                        </button>
                        ${isOutgoing ? `<button class="message-action-btn" onclick="editMessage('${messageId}')">✏️ Edit</button>` : ''}
                        <button class="message-action-btn" onclick="forwardMessage('${messageId}')">
                            ➡️ Forward
                        </button>
                    </div>
                </div>
            `;
        }

        function renderMessageThread(thread) {
            const mainMessage = thread.message;
            const replies = thread.replies;
            const isOutgoing = mainMessage.direction === 'outgoing';
            const threadId = `thread-${mainMessage.id}`;

            return `
                <div class="message ${isOutgoing ? 'message-sent' : 'message-received'}" data-message-id="${mainMessage.id}">
                    <div class="message-content">
                        <div class="message-text">${mainMessage.content}</div>
                        <div class="message-meta">
                            <span class="message-time">${new Date(mainMessage.created_at).toLocaleTimeString()}</span>
                            ${isOutgoing ? `<span class="message-status">${mainMessage.status}</span>` : ''}
                        </div>
                    </div>
                    <div class="message-actions">
                        <button class="message-action-btn" onclick="replyToMessage('${mainMessage.id}', '${mainMessage.content.substring(0, 50)}...')">
                            ↩️ Reply
                        </button>
                        ${isOutgoing ? `<button class="message-action-btn" onclick="editMessage('${mainMessage.id}')">✏️ Edit</button>` : ''}
                        <button class="message-action-btn" onclick="forwardMessage('${mainMessage.id}')">
                            ➡️ Forward
                        </button>
                    </div>

                    <div class="message-thread-controls">
                        <button class="thread-collapse-btn" onclick="toggleThread('${threadId}')">
                            <span class="thread-count">${replies.length}</span> ${replies.length === 1 ? 'reply' : 'replies'}
                            <span class="collapse-icon">▼</span>
                        </button>
                    </div>

                    <div class="message-thread" id="${threadId}">
                        ${replies.map(reply => `
                            <div class="message ${reply.direction === 'outgoing' ? 'message-sent' : 'message-received'}" data-message-id="${reply.id}">
                                <div class="message-thread-indicator">↳</div>
                                <div class="message-content">
                                    <div class="message-text">${reply.content}</div>
                                    <div class="message-meta">
                                        <span class="message-time">${new Date(reply.created_at).toLocaleTimeString()}</span>
                                        ${reply.direction === 'outgoing' ? `<span class="message-status">${reply.status}</span>` : ''}
                                    </div>
                                </div>
                                <div class="message-actions">
                                    <button class="message-action-btn" onclick="replyToMessage('${reply.id}', '${reply.content.substring(0, 50)}...')">
                                        ↩️ Reply
                                    </button>
                                    ${reply.direction === 'outgoing' ? `<button class="message-action-btn" onclick="editMessage('${reply.id}')">✏️ Edit</button>` : ''}
                                    <button class="message-action-btn" onclick="forwardMessage('${reply.id}')">
                                        ➡️ Forward
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        function renderReplyContext(replyToId) {
            // Find the original message being replied to
            const originalMessage = currentConversationMessages.find(msg => msg.id === replyToId);
            if (!originalMessage) return '';

            return `
                <div class="reply-context">
                    <div class="reply-context-author">
                        ${originalMessage.direction === 'outgoing' ? 'You' : 'Customer'}
                    </div>
                    <div class="reply-context-text">
                        ${originalMessage.content}
                    </div>
                </div>
            `;
        }

        // Thread Management Functions
        function toggleThread(threadId) {
            const thread = document.getElementById(threadId);
            const button = document.querySelector(`button[onclick="toggleThread('${threadId}')"]`);
            const icon = button.querySelector('.collapse-icon');

            if (thread.classList.contains('thread-collapsed')) {
                thread.classList.remove('thread-collapsed');
                icon.textContent = '▼';
            } else {
                thread.classList.add('thread-collapsed');
                icon.textContent = '▶';
            }
        }

        // Message Action Functions
        let replyToMessageId = null;
        let replyToMessageText = '';
        let currentConversationMessages = [];

        function replyToMessage(messageId, messageText) {
            replyToMessageId = messageId;
            replyToMessageText = messageText;

            // Show reply context in composer
            showReplyContext(messageText);

            // Focus on message input
            const messageInput = document.getElementById('messageText');
            if (messageInput) {
                messageInput.focus();
            }
        }

        function showReplyContext(messageText) {
            const composer = document.querySelector('.message-composer');

            // Remove existing reply context
            const existingContext = composer.querySelector('.reply-context-composer');
            if (existingContext) {
                existingContext.remove();
            }

            // Add new reply context
            const replyContext = document.createElement('div');
            replyContext.className = 'reply-context-composer';
            replyContext.innerHTML = `
                <div class="reply-context">
                    <div class="reply-context-author">Replying to:</div>
                    <div class="reply-context-text">${messageText}</div>
                    <button class="reply-context-close" onclick="clearReplyContext()">×</button>
                </div>
            `;

            composer.insertBefore(replyContext, composer.firstChild);
        }

        function clearReplyContext() {
            replyToMessageId = null;
            replyToMessageText = '';

            const replyContext = document.querySelector('.reply-context-composer');
            if (replyContext) {
                replyContext.remove();
            }
        }

        function editMessage(messageId) {
            const message = currentConversationMessages.find(msg => msg.id === messageId);
            if (!message) {
                alert('Message not found');
                return;
            }

            if (message.direction !== 'outgoing') {
                alert('You can only edit your own messages');
                return;
            }

            const newContent = prompt('Edit message:', message.content);
            if (newContent === null || newContent.trim() === '') {
                return;
            }

            // In a real implementation, you would call an API to edit the message
            alert('Message editing is not yet implemented in the backend. This would update the message content.');
        }

        function forwardMessage(messageId) {
            const message = currentConversationMessages.find(msg => msg.id === messageId);
            if (!message) {
                alert('Message not found');
                return;
            }

            // In a real implementation, you would open a contact/conversation selector
            const recipient = prompt('Enter recipient (phone number or contact name):');
            if (!recipient) {
                return;
            }

            if (confirm(`Forward message "${message.content.substring(0, 50)}..." to ${recipient}?`)) {
                // In a real implementation, you would call an API to forward the message
                alert('Message forwarding is not yet implemented in the backend. This would send the message to the specified recipient.');
            }
        }

        // File Upload and Media Functions
        let selectedFiles = [];
        let currentMediaType = 'image';

        function selectMediaType(type) {
            currentMediaType = type;

            // Update active button
            document.querySelectorAll('.media-type-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-media-type="${type}"]`).classList.add('active');

            // Update file input accept attribute
            const fileInput = document.getElementById('mediaFile');
            switch(type) {
                case 'image':
                    fileInput.accept = 'image/*';
                    break;
                case 'document':
                    fileInput.accept = 'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/plain';
                    break;
                case 'audio':
                    fileInput.accept = 'audio/*';
                    break;
                case 'video':
                    fileInput.accept = 'video/*';
                    break;
            }
        }

        function triggerFileUpload() {
            document.getElementById('mediaFile').click();
        }

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFilesToSelection(files);
        }

        function handleFileDrop(event) {
            event.preventDefault();
            event.stopPropagation();

            const uploadArea = event.currentTarget;
            uploadArea.classList.remove('dragover');

            const files = Array.from(event.dataTransfer.files);
            addFilesToSelection(files);
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.remove('dragover');
        }

        function addFilesToSelection(files) {
            const maxFileSize = 16 * 1024 * 1024; // 16MB
            const validFiles = [];

            files.forEach(file => {
                if (file.size > maxFileSize) {
                    alert(`File "${file.name}" is too large. Maximum size is 16MB.`);
                    return;
                }

                if (selectedFiles.length + validFiles.length >= 10) {
                    alert('Maximum 10 files can be selected at once.');
                    return;
                }

                validFiles.push(file);
            });

            selectedFiles = selectedFiles.concat(validFiles);
            updateMediaPreview();
            updateSendButton();
        }

        function updateMediaPreview() {
            const preview = document.getElementById('mediaPreview');

            if (selectedFiles.length === 0) {
                preview.innerHTML = '';
                return;
            }

            preview.innerHTML = selectedFiles.map((file, index) => {
                const isImage = file.type.startsWith('image/');
                const fileSize = formatFileSize(file.size);

                if (isImage) {
                    const url = URL.createObjectURL(file);
                    return `
                        <div class="media-preview-item">
                            <img src="${url}" alt="${file.name}" class="media-preview-image" />
                            <button class="media-preview-remove" onclick="removeFile(${index})">×</button>
                        </div>
                    `;
                } else {
                    return `
                        <div class="media-preview-item">
                            <div class="media-preview-file">
                                <div class="media-preview-file-icon">${getFileIcon(file.type)}</div>
                                <div class="media-preview-file-name">${file.name}</div>
                                <div class="file-size-info">${fileSize}</div>
                            </div>
                            <button class="media-preview-remove" onclick="removeFile(${index})">×</button>
                        </div>
                    `;
                }
            }).join('');
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateMediaPreview();
            updateSendButton();
        }

        function updateSendButton() {
            const sendBtn = document.getElementById('sendMediaBtn');
            sendBtn.disabled = selectedFiles.length === 0;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function getFileIcon(mimeType) {
            if (mimeType.startsWith('image/')) return '🖼️';
            if (mimeType.startsWith('video/')) return '🎥';
            if (mimeType.startsWith('audio/')) return '🎵';
            if (mimeType.includes('pdf')) return '📄';
            if (mimeType.includes('word')) return '📝';
            if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
            if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return '📽️';
            return '📎';
        }

        // Call Functionality
        let currentCall = null;
        let callStartTime = null;
        let callTimer = null;

        function initiateCall(type) {
            const conversationTitle = document.getElementById('conversationTitle').textContent;

            if (currentCall) {
                alert('A call is already in progress');
                return;
            }

            // In a real implementation, you would integrate with WebRTC or a calling service
            const confirmed = confirm(`Initiate ${type} call with ${conversationTitle}?`);
            if (!confirmed) return;

            startCall(conversationTitle, type);
        }

        function startCall(contactName, callType) {
            currentCall = {
                contact: contactName,
                type: callType,
                status: 'connecting'
            };

            callStartTime = new Date();

            // Create call interface
            const callInterface = document.createElement('div');
            callInterface.className = 'call-interface';
            callInterface.id = 'callInterface';
            callInterface.innerHTML = `
                <div class="call-header">
                    <div class="call-status" id="callStatus">Connecting...</div>
                    <div class="call-contact">${contactName}</div>
                    <div class="call-duration" id="callDuration">00:00</div>
                </div>
                <div class="call-controls">
                    <button class="call-control-btn mute-btn" id="muteBtn" onclick="toggleMute()" title="Mute">
                        🎤
                    </button>
                    <button class="call-control-btn end-btn" onclick="endCall()" title="End Call">
                        📞
                    </button>
                    <button class="call-control-btn speaker-btn" id="speakerBtn" onclick="toggleSpeaker()" title="Speaker">
                        🔊
                    </button>
                </div>
            `;

            document.body.appendChild(callInterface);

            // Simulate call connection
            setTimeout(() => {
                if (currentCall) {
                    currentCall.status = 'connected';
                    document.getElementById('callStatus').textContent = 'Connected';
                    startCallTimer();
                }
            }, 2000);
        }

        function startCallTimer() {
            callTimer = setInterval(() => {
                if (!currentCall) return;

                const elapsed = new Date() - callStartTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);

                const duration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                const durationElement = document.getElementById('callDuration');
                if (durationElement) {
                    durationElement.textContent = duration;
                }
            }, 1000);
        }

        function endCall() {
            if (callTimer) {
                clearInterval(callTimer);
                callTimer = null;
            }

            const callInterface = document.getElementById('callInterface');
            if (callInterface) {
                callInterface.remove();
            }

            currentCall = null;
            callStartTime = null;

            // In a real implementation, you would end the actual call
            console.log('Call ended');
        }

        function toggleMute() {
            const muteBtn = document.getElementById('muteBtn');
            const isMuted = muteBtn.classList.contains('muted');

            if (isMuted) {
                muteBtn.classList.remove('muted');
                muteBtn.innerHTML = '🎤';
                muteBtn.title = 'Mute';
            } else {
                muteBtn.classList.add('muted');
                muteBtn.innerHTML = '🔇';
                muteBtn.title = 'Unmute';
            }

            // In a real implementation, you would mute/unmute the microphone
            console.log('Microphone', isMuted ? 'unmuted' : 'muted');
        }

        function toggleSpeaker() {
            const speakerBtn = document.getElementById('speakerBtn');
            const isActive = speakerBtn.classList.contains('active');

            if (isActive) {
                speakerBtn.classList.remove('active');
                speakerBtn.innerHTML = '🔊';
            } else {
                speakerBtn.classList.add('active');
                speakerBtn.innerHTML = '📢';
            }

            // In a real implementation, you would toggle speaker output
            console.log('Speaker', isActive ? 'disabled' : 'enabled');
        }

        function showContactInfo() {
            const conversationTitle = document.getElementById('conversationTitle').textContent;

            // In a real implementation, you would show a detailed contact info modal
            alert(`Contact Information for ${conversationTitle}\n\nThis would show detailed contact information, conversation history, notes, and other relevant details.`);
        }

        function openContact(contactId) {
            console.log('Opening contact:', contactId);
            // TODO: Implement contact detail view
            alert(`Contact details for ID: ${contactId} - Feature coming soon!`);
        }

        function addNewContact() {
            console.log('Adding new contact');
            // TODO: Implement add contact modal
            alert('Add new contact feature coming soon!');
        }

        // Analytics content
        function loadAnalytics() {
            contentArea.innerHTML = `
                <div class="analytics-header">
                    <h1>📊 Analytics</h1>
                    <p>Comprehensive analytics and reporting for your omnichannel platform</p>
                </div>

                <div class="analytics-filters">
                    <div class="filter-group">
                        <select id="analyticsDateRange">
                            <option value="7_days">Last 7 days</option>
                            <option value="30_days">Last 30 days</option>
                            <option value="90_days">Last 90 days</option>
                            <option value="custom">Custom range</option>
                        </select>

                        <select id="analyticsChannel">
                            <option value="">All Channels</option>
                            <option value="whatsapp">WhatsApp</option>
                            <option value="telegram">Telegram</option>
                            <option value="email">Email</option>
                            <option value="sms">SMS</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <button onclick="refreshAnalytics()" style="background: #4299e1; color: white; border: none; padding: 8px 16px; border-radius: 6px;">
                            🔄 Refresh Data
                        </button>
                        <button onclick="exportAnalytics()" style="background: #38a169; color: white; border: none; padding: 8px 16px; border-radius: 6px; margin-left: 8px;">
                            📥 Export Report
                        </button>
                    </div>
                </div>

                <div class="analytics-grid">
                    <!-- Key Metrics Row -->
                    <div class="analytics-section">
                        <h3>📈 Key Performance Metrics</h3>
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-icon">📨</div>
                                <div class="metric-content">
                                    <div class="metric-value" id="analyticsMessageVolume">Loading...</div>
                                    <div class="metric-label">Message Volume</div>
                                    <div class="metric-change" id="messageVolumeChange">+0%</div>
                                </div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-icon">✅</div>
                                <div class="metric-content">
                                    <div class="metric-value" id="analyticsDeliveryRate">Loading...</div>
                                    <div class="metric-label">Delivery Rate</div>
                                    <div class="metric-change" id="deliveryRateChange">+0%</div>
                                </div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-icon">⏱️</div>
                                <div class="metric-content">
                                    <div class="metric-value" id="analyticsResponseTime">Loading...</div>
                                    <div class="metric-label">Avg Response Time</div>
                                    <div class="metric-change" id="responseTimeChange">+0%</div>
                                </div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-icon">🎯</div>
                                <div class="metric-content">
                                    <div class="metric-value" id="analyticsAgentPerformance">Loading...</div>
                                    <div class="metric-label">Resolved Conversations</div>
                                    <div class="metric-change" id="agentPerformanceChange">+0%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="analytics-section">
                        <h3>📊 Performance Charts</h3>
                        <div class="charts-grid">
                            <div class="chart-container">
                                <h4>Message Volume Over Time</h4>
                                <div class="chart-placeholder" id="messageVolumeChart">
                                    <p>📈 Message volume trends</p>
                                    <p class="chart-data">Loading chart data...</p>
                                </div>
                            </div>

                            <div class="chart-container">
                                <h4>Delivery Rate by Channel</h4>
                                <div class="chart-placeholder" id="deliveryRateChart">
                                    <p>📊 Channel performance</p>
                                    <p class="chart-data">Loading chart data...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Analytics Row -->
                    <div class="analytics-section">
                        <h3>🔍 Detailed Analytics</h3>
                        <div class="analytics-details">
                            <div class="analytics-table">
                                <h4>Channel Performance</h4>
                                <div class="table-container" id="channelPerformanceTable">
                                    <div class="loading-state">Loading channel data...</div>
                                </div>
                            </div>

                            <div class="analytics-table">
                                <h4>Recent Activity</h4>
                                <div class="table-container" id="recentActivityTable">
                                    <div class="loading-state">Loading activity data...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add event listeners
            document.getElementById('analyticsDateRange').addEventListener('change', refreshAnalytics);
            document.getElementById('analyticsChannel').addEventListener('change', refreshAnalytics);

            // Load analytics data
            loadAnalyticsData();
        }

        // Analytics data loading functions
        async function loadAnalyticsData() {
            try {
                // Load all analytics endpoints in parallel
                await Promise.all([
                    loadAnalyticsMetrics(),
                    loadAnalyticsCharts(),
                    loadAnalyticsTables()
                ]);
            } catch (error) {
                console.error('Error loading analytics data:', error);
            }
        }

        async function loadAnalyticsMetrics() {
            try {
                // Load message volume
                const volumeResponse = await fetch(`${API_BASE}/api/analytics/message-volume`, {
                    credentials: "include"
                });
                if (volumeResponse.ok) {
                    const volumeData = await volumeResponse.json();
                    document.getElementById('analyticsMessageVolume').textContent =
                        volumeData.message_volume?.toLocaleString() || '0';
                    document.getElementById('messageVolumeChange').textContent =
                        volumeData.change_percent ? `${volumeData.change_percent > 0 ? '+' : ''}${volumeData.change_percent}%` : '+0%';
                } else {
                    // Fallback data
                    document.getElementById('analyticsMessageVolume').textContent = '2,847';
                    document.getElementById('messageVolumeChange').textContent = '+15.2%';
                }

                // Load delivery rates
                const deliveryResponse = await fetch(`${API_BASE}/api/analytics/delivery-rates`, {
                    credentials: "include"
                });
                if (deliveryResponse.ok) {
                    const deliveryData = await deliveryResponse.json();
                    document.getElementById('analyticsDeliveryRate').textContent =
                        Math.round((deliveryData.delivery_rate || 0) * 100) + '%';
                    document.getElementById('deliveryRateChange').textContent =
                        deliveryData.change_percent ? `${deliveryData.change_percent > 0 ? '+' : ''}${deliveryData.change_percent}%` : '****%';
                } else {
                    // Fallback data
                    document.getElementById('analyticsDeliveryRate').textContent = '98%';
                    document.getElementById('deliveryRateChange').textContent = '****%';
                }

                // Load response times
                const responseResponse = await fetch(`${API_BASE}/api/analytics/response-times`, {
                    credentials: "include"
                });
                if (responseResponse.ok) {
                    const responseData = await responseResponse.json();
                    const avgTime = responseData.average_response_time_seconds || 150;
                    document.getElementById('analyticsResponseTime').textContent =
                        avgTime < 60 ? `${avgTime}s` : `${Math.round(avgTime / 60)}min`;
                    document.getElementById('responseTimeChange').textContent =
                        responseData.change_percent ? `${responseData.change_percent > 0 ? '+' : ''}${responseData.change_percent}%` : '-8.3%';
                } else {
                    // Fallback data
                    document.getElementById('analyticsResponseTime').textContent = '2.5min';
                    document.getElementById('responseTimeChange').textContent = '-8.3%';
                }

                // Load agent performance
                const agentResponse = await fetch(`${API_BASE}/api/analytics/agent-performance`, {
                    credentials: "include"
                });
                if (agentResponse.ok) {
                    const agentData = await agentResponse.json();
                    document.getElementById('analyticsAgentPerformance').textContent =
                        agentData.resolved_conversations?.toLocaleString() || '0';
                    document.getElementById('agentPerformanceChange').textContent =
                        agentData.change_percent ? `${agentData.change_percent > 0 ? '+' : ''}${agentData.change_percent}%` : '+12.4%';
                } else {
                    // Fallback data
                    document.getElementById('analyticsAgentPerformance').textContent = '123';
                    document.getElementById('agentPerformanceChange').textContent = '+12.4%';
                }

            } catch (error) {
                console.error('Error loading analytics metrics:', error);
                // Set fallback values
                document.getElementById('analyticsMessageVolume').textContent = '2,847';
                document.getElementById('messageVolumeChange').textContent = '+15.2%';
                document.getElementById('analyticsDeliveryRate').textContent = '98%';
                document.getElementById('deliveryRateChange').textContent = '****%';
                document.getElementById('analyticsResponseTime').textContent = '2.5min';
                document.getElementById('responseTimeChange').textContent = '-8.3%';
                document.getElementById('analyticsAgentPerformance').textContent = '123';
                document.getElementById('agentPerformanceChange').textContent = '+12.4%';
            }
        }

        async function loadAnalyticsCharts() {
            try {
                // For now, display mock chart data since we don't have chart endpoints yet
                document.getElementById('messageVolumeChart').innerHTML = `
                    <div class="mock-chart">
                        <div class="chart-bars">
                            <div class="chart-bar" style="height: 60%"></div>
                            <div class="chart-bar" style="height: 80%"></div>
                            <div class="chart-bar" style="height: 45%"></div>
                            <div class="chart-bar" style="height: 90%"></div>
                            <div class="chart-bar" style="height: 75%"></div>
                            <div class="chart-bar" style="height: 95%"></div>
                            <div class="chart-bar" style="height: 85%"></div>
                        </div>
                        <p class="chart-data">📈 Message volume trending upward</p>
                    </div>
                `;

                document.getElementById('deliveryRateChart').innerHTML = `
                    <div class="mock-chart">
                        <div class="chart-pie">
                            <div class="pie-segment whatsapp" style="--percentage: 45%">WhatsApp 45%</div>
                            <div class="pie-segment telegram" style="--percentage: 25%">Telegram 25%</div>
                            <div class="pie-segment email" style="--percentage: 20%">Email 20%</div>
                            <div class="pie-segment sms" style="--percentage: 10%">SMS 10%</div>
                        </div>
                        <p class="chart-data">📊 WhatsApp leading channel performance</p>
                    </div>
                `;

            } catch (error) {
                console.error('Error loading analytics charts:', error);
            }
        }

        async function loadAnalyticsTables() {
            try {
                // Channel performance table
                document.getElementById('channelPerformanceTable').innerHTML = `
                    <table class="analytics-table-content">
                        <thead>
                            <tr>
                                <th>Channel</th>
                                <th>Messages</th>
                                <th>Delivery Rate</th>
                                <th>Response Rate</th>
                                <th>Avg Response Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="channel-badge whatsapp">WhatsApp</span></td>
                                <td>1,284</td>
                                <td>98.5%</td>
                                <td>87.2%</td>
                                <td>2.1min</td>
                            </tr>
                            <tr>
                                <td><span class="channel-badge telegram">Telegram</span></td>
                                <td>712</td>
                                <td>99.1%</td>
                                <td>92.4%</td>
                                <td>1.8min</td>
                            </tr>
                            <tr>
                                <td><span class="channel-badge email">Email</span></td>
                                <td>568</td>
                                <td>97.8%</td>
                                <td>45.6%</td>
                                <td>4.2min</td>
                            </tr>
                            <tr>
                                <td><span class="channel-badge sms">SMS</span></td>
                                <td>283</td>
                                <td>99.6%</td>
                                <td>78.3%</td>
                                <td>3.1min</td>
                            </tr>
                        </tbody>
                    </table>
                `;

                // Recent activity table
                document.getElementById('recentActivityTable').innerHTML = `
                    <table class="analytics-table-content">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Event</th>
                                <th>Channel</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2 min ago</td>
                                <td>Message sent to John Smith</td>
                                <td><span class="channel-badge whatsapp">WhatsApp</span></td>
                                <td><span class="status-badge delivered">Delivered</span></td>
                            </tr>
                            <tr>
                                <td>5 min ago</td>
                                <td>Campaign "Summer Sale" completed</td>
                                <td><span class="channel-badge telegram">Telegram</span></td>
                                <td><span class="status-badge completed">Completed</span></td>
                            </tr>
                            <tr>
                                <td>12 min ago</td>
                                <td>New contact added: Sarah Johnson</td>
                                <td><span class="channel-badge email">Email</span></td>
                                <td><span class="status-badge active">Active</span></td>
                            </tr>
                            <tr>
                                <td>18 min ago</td>
                                <td>Bot flow "Support" triggered</td>
                                <td><span class="channel-badge whatsapp">WhatsApp</span></td>
                                <td><span class="status-badge active">Active</span></td>
                            </tr>
                        </tbody>
                    </table>
                `;

            } catch (error) {
                console.error('Error loading analytics tables:', error);
            }
        }

        function refreshAnalytics() {
            console.log('Refreshing analytics data...');
            loadAnalyticsData();
        }

        function exportAnalytics() {
            console.log('Exporting analytics report...');
            // TODO: Implement analytics export functionality
            alert('Export analytics feature coming soon!');
        }

        // Settings sections
        function showSettingsSection(section) {
            const settingsContent = document.getElementById('settingsContent');

            // Update active nav
            document.querySelectorAll('.settings-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event?.target?.classList.add('active');

            switch(section) {
                case 'integrations':
                    loadIntegrationsSettings();
                    break;
                default:
                    settingsContent.innerHTML = `<h3>${section}</h3><p>Settings for ${section} coming soon...</p>`;
            }
        }

        // Integrations settings content (matching your screenshot)
        function loadIntegrationsSettings() {
            const settingsContent = document.getElementById('settingsContent');
            settingsContent.innerHTML = `
                <h2>Integrations</h2>
                <p style="color: #718096; margin-bottom: 30px;">Connect your favorite tools and services to enhance your omnichannel experience.</p>

                <div class="integration-grid">
                    <div class="integration-card">
                        <div class="integration-header">
                            <div class="integration-title">WhatsApp Business</div>
                            <div class="toggle-switch active" onclick="toggleIntegration(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        <div class="integration-description">Connect your WhatsApp Business account for messaging</div>
                        <div class="integration-status">
                            <span class="status-badge status-connected">Connected</span>
                        </div>
                        <div class="integration-actions">
                            <button class="config-btn" onclick="openWhatsAppConfig()">⚙️ Configure</button>
                            <button class="test-btn" onclick="testWhatsAppConnection()">🧪 Test Connection</button>
                        </div>
                    </div>

                    <div class="integration-card">
                        <div class="integration-header">
                            <div class="integration-title">Telegram</div>
                            <div class="toggle-switch" onclick="toggleIntegration(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        <div class="integration-description">Connect your Telegram bot for messaging</div>
                        <div class="integration-status">
                            <span class="status-badge status-disconnected">Disconnected</span>
                        </div>
                    </div>

                    <div class="integration-card">
                        <div class="integration-header">
                            <div class="integration-title">Facebook Messenger</div>
                            <div class="toggle-switch" onclick="toggleIntegration(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        <div class="integration-description">Connect Facebook Messenger for customer support</div>
                        <div class="integration-status">
                            <span class="status-badge status-error">Error</span>
                        </div>
                    </div>

                    <div class="integration-card">
                        <div class="integration-header">
                            <div class="integration-title">Email</div>
                            <div class="toggle-switch active" onclick="toggleIntegration(this)">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        <div class="integration-description">Connect your email for customer communications</div>
                        <div class="integration-status">
                            <span class="status-badge status-connected">Connected</span>
                        </div>
                    </div>
                </div>
            `;
        }
        // Toggle integration function
        function toggleIntegration(toggleElement) {
            toggleElement.classList.toggle('active');

            // Update status badge
            const card = toggleElement.closest('.integration-card');
            const statusBadge = card.querySelector('.status-badge');

            if (toggleElement.classList.contains('active')) {
                statusBadge.textContent = 'Connected';
                statusBadge.className = 'status-badge status-connected';
            } else {
                statusBadge.textContent = 'Disconnected';
                statusBadge.className = 'status-badge status-disconnected';
            }
        }

        // WhatsApp Configuration Functions
        function openWhatsAppConfig() {
            // Create modal overlay
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content whatsapp-config-modal">
                    <div class="modal-header">
                        <h3>📱 WhatsApp Business API Configuration</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>

                    <div class="modal-body">
                        <div class="config-section">
                            <h4>API Credentials</h4>
                            <p class="config-description">Enter your WhatsApp Business API credentials from Meta Business Manager</p>

                            <div class="form-group">
                                <label for="accessToken">Access Token *</label>
                                <input type="password" id="accessToken" placeholder="Enter your WhatsApp Business API access token" />
                                <small>Get this from your Meta Business Manager > WhatsApp > API Setup</small>
                            </div>

                            <div class="form-group">
                                <label for="phoneNumberId">Phone Number ID *</label>
                                <input type="text" id="phoneNumberId" placeholder="Enter your phone number ID" />
                                <small>Found in Meta Business Manager > WhatsApp > API Setup</small>
                            </div>

                            <div class="form-group">
                                <label for="businessAccountId">Business Account ID *</label>
                                <input type="text" id="businessAccountId" placeholder="Enter your WhatsApp Business Account ID" />
                                <small>Your WhatsApp Business Account ID from Meta Business Manager</small>
                            </div>
                        </div>

                        <div class="config-section">
                            <h4>Webhook Configuration</h4>
                            <p class="config-description">Configure webhook settings for receiving messages</p>

                            <div class="form-group">
                                <label for="webhookVerifyToken">Webhook Verify Token</label>
                                <input type="text" id="webhookVerifyToken" placeholder="Enter webhook verification token" />
                                <small>Use this token when setting up your webhook in Meta Business Manager</small>
                            </div>

                            <div class="form-group">
                                <label>Webhook URL</label>
                                <div class="webhook-url-display">
                                    <code id="webhookUrl">${window.location.origin}/api/webhooks/whatsapp</code>
                                    <button onclick="copyWebhookUrl()" class="copy-btn">📋 Copy</button>
                                </div>
                                <small>Use this URL in your Meta Business Manager webhook configuration</small>
                            </div>
                        </div>

                        <div class="config-section">
                            <h4>Current Status</h4>
                            <div class="status-display" id="configStatus">
                                <div class="status-item">
                                    <span class="status-label">API Connection:</span>
                                    <span class="status-value" id="apiStatus">Not tested</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">Webhook:</span>
                                    <span class="status-value" id="webhookStatus">Ready for incoming messages ✅</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">Message Receiving:</span>
                                    <span class="status-value">Active (real-time polling) 🔄</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button class="btn-secondary" onclick="closeModal()">Cancel</button>
                        <button class="btn-primary" onclick="testWhatsAppConfig()">🧪 Test Configuration</button>
                        <button class="btn-primary" onclick="testMessageSending()">📤 Test Message Sending</button>
                        <button class="btn-primary" onclick="saveWhatsAppConfig()">💾 Save Configuration</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            loadCurrentWhatsAppConfig();
        }

        async function loadCurrentWhatsAppConfig() {
            try {
                // Try to load existing configuration
                const response = await fetch(`${API_BASE}/api/whatsapp/accounts/`, {
                    credentials: "include"
                });

                if (response.ok) {
                    const accounts = await response.json();
                    if (accounts.length > 0) {
                        const account = accounts[0];
                        // Populate form with existing data (don't show sensitive tokens)
                        document.getElementById('phoneNumberId').value = account.phone_number_id || '';
                        document.getElementById('businessAccountId').value = account.business_account_id || '';
                        document.getElementById('webhookVerifyToken').value = account.webhook_verify_token || '';

                        // Update status
                        document.getElementById('apiStatus').textContent = account.status || 'Unknown';
                        document.getElementById('webhookStatus').textContent = account.webhook_verify_token ? 'Configured' : 'Not configured';
                    }
                }
            } catch (error) {
                console.error('Error loading WhatsApp config:', error);
            }
        }

        async function saveWhatsAppConfig() {
            const accessToken = document.getElementById('accessToken').value;
            const phoneNumberId = document.getElementById('phoneNumberId').value;
            const businessAccountId = document.getElementById('businessAccountId').value;
            const webhookVerifyToken = document.getElementById('webhookVerifyToken').value;

            if (!accessToken || !phoneNumberId || !businessAccountId) {
                alert('Please fill in all required fields (Access Token, Phone Number ID, Business Account ID)');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/whatsapp/accounts/`, {
                    method: 'POST',
                    credentials: "include",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        access_token: accessToken,
                        phone_number_id: phoneNumberId,
                        business_account_id: businessAccountId,
                        webhook_verify_token: webhookVerifyToken,
                        phone_number: phoneNumberId // Will be updated with actual number after verification
                    })
                });

                if (response.ok) {
                    alert('✅ WhatsApp configuration saved successfully!');
                    closeModal();
                    // Refresh the integrations page to show updated status
                    loadIntegrationsSettings();
                } else {
                    const error = await response.json();
                    alert(`❌ Error saving configuration: ${error.detail || 'Unknown error'}`);
                }
            } catch (error) {
                alert(`❌ Network error: ${error.message}`);
            }
        }

        async function testWhatsAppConfig() {
            const accessToken = document.getElementById('accessToken').value;
            const phoneNumberId = document.getElementById('phoneNumberId').value;

            if (!accessToken || !phoneNumberId) {
                alert('Please enter Access Token and Phone Number ID to test');
                return;
            }

            try {
                // Test the configuration by making a simple API call
                const testResponse = await fetch(`https://graph.facebook.com/v17.0/${phoneNumberId}`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });

                if (testResponse.ok) {
                    const data = await testResponse.json();
                    document.getElementById('apiStatus').textContent = 'Connected ✅';
                    alert(`✅ Connection successful! Phone number: ${data.display_phone_number || 'Unknown'}`);
                } else {
                    document.getElementById('apiStatus').textContent = 'Failed ❌';
                    alert('❌ Connection failed. Please check your credentials.');
                }
            } catch (error) {
                document.getElementById('apiStatus').textContent = 'Error ❌';
                alert(`❌ Test failed: ${error.message}`);
            }
        }

        function testWhatsAppConnection() {
            // Quick test without opening config modal
            alert('🧪 Testing WhatsApp connection... Feature will check current configuration.');
            // TODO: Implement quick connection test
        }

        async function testMessageSending() {
            const phoneNumber = prompt('Enter a test phone number (with country code, e.g., +1234567890):');
            if (!phoneNumber) return;

            const message = prompt('Enter a test message:', 'Hello! This is a test message from the omnichannel platform.');
            if (!message) return;

            try {
                const response = await fetch(`${API_BASE}/api/whatsapp/send-message`, {
                    method: 'POST',
                    credentials: "include",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone_number: phoneNumber,
                        message_data: {
                            type: 'text',
                            text: {
                                body: message
                            }
                        }
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(`✅ Test message sent successfully!\n\nMessage ID: ${result.message_id}\nTo: ${phoneNumber}`);
                } else {
                    alert(`❌ Failed to send test message: ${result.error}`);
                }
            } catch (error) {
                alert(`❌ Error sending test message: ${error.message}`);
            }
        }

        function copyWebhookUrl() {
            const webhookUrl = document.getElementById('webhookUrl').textContent;
            navigator.clipboard.writeText(webhookUrl).then(() => {
                alert('📋 Webhook URL copied to clipboard!');
            }).catch(() => {
                alert('❌ Failed to copy URL. Please copy manually.');
            });
        }

        // Global variable to store polling interval
        let messagePollingInterval = null;

        function startMessagePolling(conversationId) {
            // Clear any existing polling
            if (messagePollingInterval) {
                clearInterval(messagePollingInterval);
            }

            // Poll for new messages every 3 seconds
            messagePollingInterval = setInterval(async () => {
                await loadConversationMessages(conversationId);
            }, 3000);
        }

        function stopMessagePolling() {
            if (messagePollingInterval) {
                clearInterval(messagePollingInterval);
                messagePollingInterval = null;
            }
        }

        function closeModal() {
            // Stop message polling when closing conversation modal
            stopMessagePolling();

            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        // Load dashboard metrics from real API
        async function loadDashboardMetrics() {
            try {
                // First try the main dashboard metrics endpoint
                const response = await fetch(`${API_BASE}/api/analytics/dashboard-metrics`, {
                    credentials: "include",
                    headers: {
                        "Content-Type": "application/json"
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log("Dashboard metrics loaded:", data);

                    // Update the dashboard with real data
                    document.getElementById('totalMessages').textContent = data.messages_sent?.toLocaleString() || '0';
                    document.getElementById('activeConversations').textContent = data.active_conversations?.toLocaleString() || '0';
                    document.getElementById('totalCustomers').textContent = data.total_customers?.toLocaleString() || '0';

                    // Calculate response rate (placeholder calculation)
                    const responseRate = data.total_conversations > 0 ?
                        Math.round((data.active_conversations / data.total_conversations) * 100) : 0;
                    document.getElementById('responseRate').textContent = responseRate + '%';

                    document.getElementById('avgResponseTime').textContent =
                        (data.avg_response_time_minutes || 0) + 'min';

                    // Update data source info
                    document.getElementById('dataSourceInfo').textContent =
                        `Data source: ${data.data_source || 'API'} | Last updated: ${new Date().toLocaleTimeString()}`;

                    // Show data source indicator
                    if (data.data_source) {
                        console.log(`Data source: ${data.data_source}`);
                    }
                } else {
                    // Fallback to test endpoint
                    console.log("Main endpoint failed, trying test endpoint...");
                    await loadDashboardMetricsTest();
                }
            } catch (error) {
                console.error('Error loading dashboard metrics:', error);
                // Fallback to test endpoint
                await loadDashboardMetricsTest();
            }
        }

        // Fallback function for test endpoint
        async function loadDashboardMetricsTest() {
            try {
                const response = await fetch(`${API_BASE}/api/analytics/dashboard-metrics-test`, {
                    credentials: "include"
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log("Test dashboard metrics loaded:", data);

                    document.getElementById('totalMessages').textContent = data.messages_sent?.toLocaleString() || '0';
                    document.getElementById('activeConversations').textContent = data.active_conversations?.toLocaleString() || '0';
                    document.getElementById('totalCustomers').textContent = data.total_customers?.toLocaleString() || '0';

                    const responseRate = data.total_conversations > 0 ?
                        Math.round((data.active_conversations / data.total_conversations) * 100) : 94;
                    document.getElementById('responseRate').textContent = responseRate + '%';

                    document.getElementById('avgResponseTime').textContent =
                        (data.avg_response_time_minutes || 2.5) + 'min';

                    // Update data source info
                    document.getElementById('dataSourceInfo').textContent =
                        `Data source: ${data.data_source || 'test'} | Last updated: ${new Date().toLocaleTimeString()}`;

                    console.log(`Data source: ${data.data_source || 'test'}`);
                } else {
                    throw new Error('Test endpoint also failed');
                }
            } catch (error) {
                console.error('Error loading test dashboard metrics:', error);
                // Show fallback data
                document.getElementById('totalMessages').textContent = '1,234';
                document.getElementById('activeConversations').textContent = '56';
                document.getElementById('totalCustomers').textContent = '3,456';
                document.getElementById('responseRate').textContent = '94%';
                document.getElementById('avgResponseTime').textContent = '2.3min';
                document.getElementById('dataSourceInfo').textContent = 'Data source: fallback | Connection failed';
            }
        }

        // Load additional metrics from other analytics endpoints
        async function loadAdditionalMetrics() {
            try {
                // Load message volume
                const volumeResponse = await fetch(`${API_BASE}/api/analytics/message-volume`, {
                    credentials: "include"
                });
                if (volumeResponse.ok) {
                    const volumeData = await volumeResponse.json();
                    document.getElementById('messageVolume').textContent =
                        volumeData.message_volume?.toLocaleString() || '0';
                }

                // Load delivery rates
                const deliveryResponse = await fetch(`${API_BASE}/api/analytics/delivery-rates`, {
                    credentials: "include"
                });
                if (deliveryResponse.ok) {
                    const deliveryData = await deliveryResponse.json();
                    document.getElementById('deliveryRate').textContent =
                        Math.round((deliveryData.delivery_rate || 0) * 100) + '%';
                }

                // Load agent performance
                const agentResponse = await fetch(`${API_BASE}/api/analytics/agent-performance`, {
                    credentials: "include"
                });
                if (agentResponse.ok) {
                    const agentData = await agentResponse.json();
                    document.getElementById('agentPerformance').textContent =
                        agentData.resolved_conversations?.toLocaleString() || '0';
                }

            } catch (error) {
                console.error('Error loading additional metrics:', error);
                // Set fallback values
                document.getElementById('messageVolume').textContent = '2,847';
                document.getElementById('deliveryRate').textContent = '98%';
                document.getElementById('agentPerformance').textContent = '123';
            }
        }

        // Fetch conversations from API
        async function fetchConversations(filters = {}) {
            try {
                const params = new URLSearchParams();
                if (filters.status) params.append('status', filters.status);
                if (filters.channel) params.append('channel', filters.channel);
                if (filters.search) params.append('search', filters.search);
                if (filters.unread_only) params.append('unread_only', 'true');
                params.append('limit', '50');

                const response = await fetch(`${API_BASE}/api/conversations/?${params}`, {
                    credentials: "include",
                    headers: { "Content-Type": "application/json" }
                });

                if (response.ok) {
                    const conversations = await response.json();
                    displayConversations(conversations);
                } else {
                    // Fallback to mock data if API fails
                    displayConversations(getMockConversations());
                }
            } catch (error) {
                console.error('Error fetching conversations:', error);
                displayConversations(getMockConversations());
            }
        }

        // Display conversations in the UI
        function displayConversations(conversations) {
            const conversationsList = document.getElementById('conversationsList');

            if (!conversations || conversations.length === 0) {
                conversationsList.innerHTML = `
                    <div class="empty-state">
                        <p>No conversations found</p>
                        <small>Start a conversation to see it here</small>
                    </div>
                `;
                return;
            }

            const isBulkMode = document.getElementById('conversationBulkToggle')?.classList.contains('active') || false;

            conversationsList.innerHTML = conversations.map(conversation => `
                <div class="conversation-item ${conversation.unread_count > 0 ? 'unread' : ''} ${isBulkMode ? 'bulk-mode' : ''}"
                     onclick="${isBulkMode ? `toggleConversationSelection('${conversation.id}')` : `openConversation('${conversation.id}')`}"
                     data-conversation-id="${conversation.id}">
                    ${isBulkMode ? `<input type="checkbox" class="bulk-checkbox" onclick="event.stopPropagation(); toggleConversationSelection('${conversation.id}')" data-conversation-id="${conversation.id}">` : ''}
                    <div class="conversation-avatar">
                        <div class="avatar-circle ${conversation.channel}">
                            ${getChannelIcon(conversation.channel)}
                        </div>
                    </div>
                    <div class="conversation-content">
                        <div class="conversation-header">
                            <h4>${conversation.title || 'Untitled Conversation'}</h4>
                            <span class="conversation-time">${formatTime(conversation.last_message_at || conversation.updated_at)}</span>
                        </div>
                        <div class="conversation-preview">
                            <p>${conversation.last_message || 'No messages yet'}</p>
                            ${conversation.unread_count > 0 ? `<span class="unread-badge">${conversation.unread_count}</span>` : ''}
                        </div>
                        <div class="conversation-meta">
                            <span class="channel-badge ${conversation.channel}">${conversation.channel}</span>
                            <span class="status-badge ${conversation.status}">${conversation.status}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Helper functions for conversations
        function getChannelIcon(channel) {
            const icons = {
                'whatsapp': '📱',
                'telegram': '✈️',
                'email': '📧',
                'sms': '💬',
                'facebook': '📘',
                'instagram': '📷'
            };
            return icons[channel] || '💬';
        }

        function formatTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            if (diffDays < 7) return `${diffDays}d ago`;
            return date.toLocaleDateString();
        }

        function getMockConversations() {
            return [
                {
                    id: '1',
                    title: 'John Smith',
                    status: 'active',
                    channel: 'whatsapp',
                    channel_id: '+1234567890',
                    contact_id: '1',
                    last_message_at: new Date(Date.now() - 300000).toISOString(), // 5 mins ago
                    last_message: 'Thanks for your help!',
                    unread_count: 2,
                    created_at: new Date(Date.now() - 86400000).toISOString(),
                    updated_at: new Date(Date.now() - 300000).toISOString()
                },
                {
                    id: '2',
                    title: 'Sarah Johnson',
                    status: 'active',
                    channel: 'telegram',
                    channel_id: '@sarahj',
                    contact_id: '2',
                    last_message_at: new Date(Date.now() - 1800000).toISOString(), // 30 mins ago
                    last_message: 'Can you send me the invoice?',
                    unread_count: 0,
                    created_at: new Date(Date.now() - 172800000).toISOString(),
                    updated_at: new Date(Date.now() - 1800000).toISOString()
                },
                {
                    id: '3',
                    title: '<EMAIL>',
                    status: 'active',
                    channel: 'email',
                    channel_id: '<EMAIL>',
                    contact_id: '3',
                    last_message_at: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
                    last_message: 'Issue has been resolved',
                    unread_count: 1,
                    created_at: new Date(Date.now() - 259200000).toISOString(),
                    updated_at: new Date(Date.now() - 7200000).toISOString()
                }
            ];
        }

        // Filter and search functions
        function filterConversations() {
            const status = document.getElementById('statusFilter')?.value;
            const channel = document.getElementById('channelFilter')?.value;
            const unreadOnly = document.getElementById('unreadOnlyFilter')?.checked;
            const search = document.getElementById('searchInput')?.value;

            const filters = {};
            if (status) filters.status = status;
            if (channel) filters.channel = channel;
            if (unreadOnly) filters.unread_only = true;
            if (search) filters.search = search;

            fetchConversations(filters);
        }

        function searchConversations() {
            // Debounce search to avoid too many API calls
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(() => {
                filterConversations();
            }, 300);
        }

        // Advanced Search Functions
        function openAdvancedSearch() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content advanced-search-modal">
                    <div class="modal-header">
                        <h3>🔍 Advanced Search & Filters</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>

                    <div class="modal-body">
                        <!-- Quick Filters -->
                        <div class="form-group">
                            <label>Quick Filters</label>
                            <div class="quick-filters">
                                <button class="quick-filter-btn" data-filter="today" onclick="applyQuickFilter('today')">Today</button>
                                <button class="quick-filter-btn" data-filter="week" onclick="applyQuickFilter('week')">This Week</button>
                                <button class="quick-filter-btn" data-filter="month" onclick="applyQuickFilter('month')">This Month</button>
                                <button class="quick-filter-btn" data-filter="unread" onclick="applyQuickFilter('unread')">Unread Only</button>
                                <button class="quick-filter-btn" data-filter="active" onclick="applyQuickFilter('active')">Active</button>
                                <button class="quick-filter-btn" data-filter="whatsapp" onclick="applyQuickFilter('whatsapp')">WhatsApp</button>
                            </div>
                        </div>

                        <!-- Search Criteria -->
                        <div class="search-criteria-grid">
                            <div class="form-group">
                                <label for="advancedSearchText">Search Text</label>
                                <input type="text" id="advancedSearchText" placeholder="Search in messages, contacts, or titles...">
                            </div>

                            <div class="form-group">
                                <label for="advancedChannelFilter">Channel</label>
                                <select id="advancedChannelFilter">
                                    <option value="">All Channels</option>
                                    <option value="whatsapp">WhatsApp</option>
                                    <option value="sms">SMS</option>
                                    <option value="email">Email</option>
                                    <option value="telegram">Telegram</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="advancedStatusFilter">Status</label>
                                <select id="advancedStatusFilter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="archived">Archived</option>
                                    <option value="pending">Pending</option>
                                    <option value="closed">Closed</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="advancedContactFilter">Contact</label>
                                <input type="text" id="advancedContactFilter" placeholder="Search by contact name or phone...">
                            </div>

                            <div class="form-group search-criteria-full">
                                <label>Date Range</label>
                                <div class="date-range-group">
                                    <input type="date" id="dateFrom" placeholder="From">
                                    <span>to</span>
                                    <input type="date" id="dateTo" placeholder="To">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="advancedMessageCount">Message Count</label>
                                <select id="advancedMessageCount">
                                    <option value="">Any</option>
                                    <option value="1">Single message</option>
                                    <option value="2-5">2-5 messages</option>
                                    <option value="6-10">6-10 messages</option>
                                    <option value="10+">10+ messages</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="advancedResponseTime">Response Time</label>
                                <select id="advancedResponseTime">
                                    <option value="">Any</option>
                                    <option value="fast">Fast (< 1 hour)</option>
                                    <option value="medium">Medium (1-24 hours)</option>
                                    <option value="slow">Slow (> 24 hours)</option>
                                </select>
                            </div>
                        </div>

                        <!-- Search Results Summary -->
                        <div id="searchResultsSummary" class="search-results-summary" style="display: none;">
                            <span id="resultsCount">0</span> results found
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="clearAdvancedSearch()">Clear All</button>
                        <button class="btn btn-primary" onclick="executeAdvancedSearch()">Search</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Set default date range (last 30 days)
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
            document.getElementById('dateTo').value = today.toISOString().split('T')[0];
            document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];
        }

        function applyQuickFilter(filterType) {
            // Remove active class from all quick filter buttons
            document.querySelectorAll('.quick-filter-btn').forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            event.target.classList.add('active');

            const today = new Date();

            switch(filterType) {
                case 'today':
                    document.getElementById('dateFrom').value = today.toISOString().split('T')[0];
                    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
                    break;
                case 'week':
                    const weekAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
                    document.getElementById('dateFrom').value = weekAgo.toISOString().split('T')[0];
                    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
                    break;
                case 'month':
                    const monthAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
                    document.getElementById('dateFrom').value = monthAgo.toISOString().split('T')[0];
                    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
                    break;
                case 'unread':
                    // This will be handled in the search execution
                    break;
                case 'active':
                    document.getElementById('advancedStatusFilter').value = 'active';
                    break;
                case 'whatsapp':
                    document.getElementById('advancedChannelFilter').value = 'whatsapp';
                    break;
            }
        }

        function clearAdvancedSearch() {
            // Clear all form fields
            document.getElementById('advancedSearchText').value = '';
            document.getElementById('advancedChannelFilter').value = '';
            document.getElementById('advancedStatusFilter').value = '';
            document.getElementById('advancedContactFilter').value = '';
            document.getElementById('advancedMessageCount').value = '';
            document.getElementById('advancedResponseTime').value = '';

            // Reset date range to last 30 days
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
            document.getElementById('dateTo').value = today.toISOString().split('T')[0];
            document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];

            // Remove active class from quick filter buttons
            document.querySelectorAll('.quick-filter-btn').forEach(btn => btn.classList.remove('active'));

            // Hide results summary
            document.getElementById('searchResultsSummary').style.display = 'none';
        }

        async function executeAdvancedSearch() {
            const searchCriteria = {
                text: document.getElementById('advancedSearchText').value,
                channel: document.getElementById('advancedChannelFilter').value,
                status: document.getElementById('advancedStatusFilter').value,
                contact: document.getElementById('advancedContactFilter').value,
                date_from: document.getElementById('dateFrom').value,
                date_to: document.getElementById('dateTo').value,
                message_count: document.getElementById('advancedMessageCount').value,
                response_time: document.getElementById('advancedResponseTime').value
            };

            // Check for unread filter from quick filters
            const unreadFilter = document.querySelector('.quick-filter-btn[data-filter="unread"].active');
            if (unreadFilter) {
                searchCriteria.unread_only = true;
            }

            // Remove empty criteria
            Object.keys(searchCriteria).forEach(key => {
                if (!searchCriteria[key]) {
                    delete searchCriteria[key];
                }
            });

            try {
                // Execute search
                await fetchConversations(searchCriteria);

                // Show results summary
                const resultsCount = document.querySelectorAll('.conversation-item').length;
                document.getElementById('resultsCount').textContent = resultsCount;
                document.getElementById('searchResultsSummary').style.display = 'block';

                // Close modal
                closeModal();

                // Update main search input to show active search
                const mainSearchInput = document.getElementById('searchInput');
                if (searchCriteria.text) {
                    mainSearchInput.value = searchCriteria.text;
                }

                // Show search summary in conversations section
                showSearchSummary(searchCriteria, resultsCount);

            } catch (error) {
                console.error('Advanced search failed:', error);
                alert('Search failed. Please try again.');
            }
        }

        function showSearchSummary(criteria, resultsCount) {
            // Remove existing search summary
            const existingSummary = document.querySelector('.search-summary');
            if (existingSummary) {
                existingSummary.remove();
            }

            // Create search summary
            const summary = document.createElement('div');
            summary.className = 'search-summary';
            summary.innerHTML = `
                <div class="search-summary-content">
                    <span class="search-results-text">
                        <strong>${resultsCount}</strong> conversations found
                    </span>
                    <div class="active-filters">
                        ${Object.entries(criteria).map(([key, value]) => {
                            if (key === 'unread_only') return '<span class="filter-tag">Unread Only</span>';
                            if (key === 'date_from' || key === 'date_to') return '';
                            return `<span class="filter-tag">${key}: ${value}</span>`;
                        }).filter(Boolean).join('')}
                        ${criteria.date_from && criteria.date_to ?
                            `<span class="filter-tag">Date: ${criteria.date_from} to ${criteria.date_to}</span>` : ''}
                    </div>
                    <button class="clear-search-btn" onclick="clearActiveSearch()">Clear Search</button>
                </div>
            `;

            // Insert summary before conversations list
            const conversationsContainer = document.querySelector('.conversations-container');
            conversationsContainer.insertBefore(summary, conversationsContainer.firstChild);
        }

        function clearActiveSearch() {
            // Remove search summary
            const searchSummary = document.querySelector('.search-summary');
            if (searchSummary) {
                searchSummary.remove();
            }

            // Clear main search input
            document.getElementById('searchInput').value = '';

            // Reset filters and reload conversations
            document.getElementById('statusFilter').value = '';
            document.getElementById('channelFilter').value = '';
            document.getElementById('unreadOnlyFilter').checked = false;

            // Reload all conversations
            fetchConversations();
        }

        async function openConversation(conversationId) {
            console.log('Opening conversation:', conversationId);

            // Create conversation modal
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content conversation-modal">
                    <div class="conversation-header">
                        <div class="conversation-info">
                            <h3 id="conversationTitle">Loading...</h3>
                            <p id="conversationSubtitle">Loading conversation details...</p>
                        </div>
                        <div class="conversation-actions">
                            <button class="action-btn call-btn" onclick="initiateCall('voice')" title="Voice Call">
                                📞 Call
                            </button>
                            <button class="action-btn video-btn" onclick="initiateCall('video')" title="Video Call">
                                📹 Video
                            </button>
                            <button class="action-btn info-btn" onclick="showContactInfo()" title="Contact Info">
                                ℹ️ Info
                            </button>
                            <button class="modal-close" onclick="closeModal()">&times;</button>
                        </div>
                    </div>

                    <div class="conversation-body">
                        <div class="messages-container" id="messagesContainer">
                            <div class="loading-messages">
                                <div class="loading-spinner"></div>
                                <p>Loading messages...</p>
                            </div>
                        </div>

                        <div class="message-composer">
                            <div class="composer-header">
                                <div class="message-type-selector">
                                    <button class="message-type-btn active" data-type="text" onclick="selectMessageType('text')">
                                        💬 Text
                                    </button>
                                    <button class="message-type-btn" data-type="template" onclick="selectMessageType('template')">
                                        📋 Template
                                    </button>
                                    <button class="message-type-btn" data-type="media" onclick="selectMessageType('media')">
                                        📎 Media
                                    </button>
                                </div>
                            </div>

                            <div class="composer-body">
                                <!-- Text Message Composer -->
                                <div class="composer-section" id="textComposer">
                                    <div class="message-input-container">
                                        <textarea id="messageText" placeholder="Type your message..." rows="3"></textarea>
                                        <button class="send-btn" onclick="sendMessage('${conversationId}', 'text')">
                                            📤 Send
                                        </button>
                                    </div>
                                </div>

                                <!-- Template Message Composer -->
                                <div class="composer-section hidden" id="templateComposer">
                                    <div class="template-selector">
                                        <select id="templateSelect" onchange="loadTemplatePreview()">
                                            <option value="">Select a template...</option>
                                        </select>
                                        <button class="refresh-templates-btn" onclick="loadTemplates()">🔄</button>
                                    </div>
                                    <div class="template-preview" id="templatePreview">
                                        <p>Select a template to see preview</p>
                                    </div>
                                    <div class="template-parameters" id="templateParameters">
                                        <!-- Dynamic parameter inputs will be added here -->
                                    </div>
                                    <button class="send-btn" onclick="sendMessage('${conversationId}', 'template')">
                                        📤 Send Template
                                    </button>
                                </div>

                                <!-- Media Message Composer -->
                                <div class="composer-section hidden" id="mediaComposer">
                                    <div class="media-type-selector">
                                        <button class="media-type-btn active" data-media-type="image" onclick="selectMediaType('image')">
                                            🖼️ Image
                                        </button>
                                        <button class="media-type-btn" data-media-type="document" onclick="selectMediaType('document')">
                                            📄 Document
                                        </button>
                                        <button class="media-type-btn" data-media-type="audio" onclick="selectMediaType('audio')">
                                            🎵 Audio
                                        </button>
                                        <button class="media-type-btn" data-media-type="video" onclick="selectMediaType('video')">
                                            🎥 Video
                                        </button>
                                    </div>

                                    <div class="file-upload-area" onclick="triggerFileUpload()" ondrop="handleFileDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                                        <input type="file" id="mediaFile" class="file-upload-input" accept="image/*,video/*,audio/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document" multiple onchange="handleFileSelect(event)" />
                                        <div class="file-upload-icon">📎</div>
                                        <div class="file-upload-text">Click to upload or drag and drop files</div>
                                        <div class="file-upload-hint">Images, videos, audio, documents (Max 16MB each)</div>
                                    </div>

                                    <div class="media-preview" id="mediaPreview">
                                        <!-- Selected files will be previewed here -->
                                    </div>

                                    <div class="file-upload-progress hidden" id="uploadProgress">
                                        <div class="file-upload-progress-bar" id="progressBar"></div>
                                    </div>

                                    <textarea class="media-caption-input" id="mediaCaption" placeholder="Add a caption (optional)" rows="2"></textarea>

                                    <button class="send-btn" onclick="sendMessage('${conversationId}', 'media')" id="sendMediaBtn" disabled>
                                        📤 Send Media
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Load conversation details and messages
            await loadConversationDetails(conversationId);
            await loadConversationMessages(conversationId);
            await loadTemplates();

            // Start polling for new messages
            startMessagePolling(conversationId);
        }

        // Conversation Management Functions
        async function loadConversationDetails(conversationId) {
            try {
                const response = await fetch(`${API_BASE}/api/conversations/${conversationId}`, {
                    credentials: "include"
                });

                if (response.ok) {
                    const conversation = await response.json();
                    document.getElementById('conversationTitle').textContent =
                        `${conversation.contact_name || 'Unknown Contact'} (${conversation.channel})`;
                    document.getElementById('conversationSubtitle').textContent =
                        `${conversation.channel_id} • Status: ${conversation.status}`;
                } else {
                    document.getElementById('conversationTitle').textContent = 'Conversation';
                    document.getElementById('conversationSubtitle').textContent = 'Unable to load details';
                }
            } catch (error) {
                console.error('Error loading conversation details:', error);
                document.getElementById('conversationTitle').textContent = 'Conversation';
                document.getElementById('conversationSubtitle').textContent = 'Error loading details';
            }
        }

        async function loadConversationMessages(conversationId) {
            try {
                const response = await fetch(`${API_BASE}/api/conversations/${conversationId}/messages`, {
                    credentials: "include"
                });

                const messagesContainer = document.getElementById('messagesContainer');

                if (response.ok) {
                    const messages = await response.json();

                    // Store messages globally for reply context
                    currentConversationMessages = messages;

                    if (messages.length === 0) {
                        messagesContainer.innerHTML = `
                            <div class="no-messages">
                                <p>No messages yet. Start the conversation!</p>
                            </div>
                        `;
                    } else {
                        messagesContainer.innerHTML = renderMessagesWithThreading(messages);
                    }
                } else {
                    messagesContainer.innerHTML = `
                        <div class="error-messages">
                            <p>Unable to load messages. Please try again.</p>
                        </div>
                    `;
                }

                // Scroll to bottom
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            } catch (error) {
                console.error('Error loading messages:', error);
                document.getElementById('messagesContainer').innerHTML = `
                    <div class="error-messages">
                        <p>Error loading messages: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function loadTemplates() {
            try {
                // Load WhatsApp templates from the API
                const response = await fetch(`${API_BASE}/api/whatsapp/templates/`, {
                    credentials: "include"
                });

                const templateSelect = document.getElementById('templateSelect');

                if (response.ok) {
                    const templates = await response.json();
                    templateSelect.innerHTML = '<option value="">Select a template...</option>' +
                        templates.map(template =>
                            `<option value="${template.id}" data-template='${JSON.stringify(template)}'>${template.name} (${template.language})</option>`
                        ).join('');
                } else {
                    // Fallback to mock templates
                    templateSelect.innerHTML = `
                        <option value="">Select a template...</option>
                        <option value="welcome" data-template='{"name":"welcome","language":"en","body":"Hello {{1}}, welcome to our service!"}'>Welcome Message</option>
                        <option value="order_confirmation" data-template='{"name":"order_confirmation","language":"en","body":"Your order {{1}} has been confirmed. Total: {{2}}"}'>Order Confirmation</option>
                        <option value="appointment_reminder" data-template='{"name":"appointment_reminder","language":"en","body":"Reminder: You have an appointment on {{1}} at {{2}}"}'>Appointment Reminder</option>
                    `;
                }
            } catch (error) {
                console.error('Error loading templates:', error);
            }
        }

        function selectMessageType(type) {
            // Update active button
            document.querySelectorAll('.message-type-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');

            // Show/hide composer sections
            document.querySelectorAll('.composer-section').forEach(section => {
                section.classList.add('hidden');
            });
            document.getElementById(`${type}Composer`).classList.remove('hidden');
        }

        function loadTemplatePreview() {
            const templateSelect = document.getElementById('templateSelect');
            const selectedOption = templateSelect.options[templateSelect.selectedIndex];
            const templatePreview = document.getElementById('templatePreview');
            const templateParameters = document.getElementById('templateParameters');

            if (selectedOption.value) {
                const template = JSON.parse(selectedOption.dataset.template);

                // Show template preview
                templatePreview.innerHTML = `
                    <div class="template-preview-content">
                        <h4>${template.name}</h4>
                        <p class="template-body">${template.body}</p>
                        <small>Language: ${template.language}</small>
                    </div>
                `;

                // Generate parameter inputs
                const paramMatches = template.body.match(/\{\{\d+\}\}/g) || [];
                if (paramMatches.length > 0) {
                    templateParameters.innerHTML = `
                        <h5>Template Parameters:</h5>
                        ${paramMatches.map((match, index) => {
                            const paramNum = match.replace(/[{}]/g, '');
                            return `
                                <div class="parameter-input">
                                    <label>Parameter ${paramNum}:</label>
                                    <input type="text" id="param_${paramNum}" placeholder="Enter value for parameter ${paramNum}" />
                                </div>
                            `;
                        }).join('')}
                    `;
                } else {
                    templateParameters.innerHTML = '<p>This template has no parameters.</p>';
                }
            } else {
                templatePreview.innerHTML = '<p>Select a template to see preview</p>';
                templateParameters.innerHTML = '';
            }
        }

        // Message Sending Functions
        async function sendMessage(conversationId, messageType) {
            try {
                let messageData = {};
                let endpoint = '';

                if (messageType === 'text') {
                    const messageText = document.getElementById('messageText').value.trim();
                    if (!messageText) {
                        alert('Please enter a message');
                        return;
                    }

                    messageData = {
                        type: 'text',
                        text: {
                            body: messageText
                        }
                    };

                    // Add reply context if replying to a message
                    if (replyToMessageId) {
                        messageData.reply_to_id = replyToMessageId;
                    }
                    endpoint = `${API_BASE}/api/whatsapp/send-message`;

                } else if (messageType === 'template') {
                    const templateSelect = document.getElementById('templateSelect');
                    if (!templateSelect.value) {
                        alert('Please select a template');
                        return;
                    }

                    const selectedOption = templateSelect.options[templateSelect.selectedIndex];
                    const template = JSON.parse(selectedOption.dataset.template);

                    // Collect parameters
                    const parameters = [];
                    const paramMatches = template.body.match(/\{\{\d+\}\}/g) || [];
                    for (const match of paramMatches) {
                        const paramNum = match.replace(/[{}]/g, '');
                        const paramValue = document.getElementById(`param_${paramNum}`)?.value;
                        if (!paramValue) {
                            alert(`Please fill in parameter ${paramNum}`);
                            return;
                        }
                        parameters.push({ type: 'text', text: paramValue });
                    }

                    messageData = {
                        type: 'template',
                        template: {
                            name: template.name,
                            language: {
                                code: template.language
                            },
                            components: parameters.length > 0 ? [{
                                type: 'body',
                                parameters: parameters
                            }] : []
                        }
                    };
                    endpoint = `${API_BASE}/api/whatsapp/send-message`;

                } else if (messageType === 'media') {
                    if (selectedFiles.length === 0) {
                        alert('Please select at least one media file');
                        return;
                    }

                    const mediaCaption = document.getElementById('mediaCaption').value.trim();

                    // Show upload progress
                    const progressContainer = document.getElementById('uploadProgress');
                    const progressBar = document.getElementById('progressBar');
                    progressContainer.classList.remove('hidden');
                    progressBar.style.width = '0%';

                    try {
                        // Process each file
                        for (let i = 0; i < selectedFiles.length; i++) {
                            const file = selectedFiles[i];

                            // Update progress
                            const progress = ((i + 1) / selectedFiles.length) * 100;
                            progressBar.style.width = progress + '%';

                            // Create FormData for file upload
                            const formData = new FormData();
                            formData.append('file', file);
                            formData.append('caption', mediaCaption);
                            formData.append('type', getMediaMessageType(file.type));

                            // Add reply context if replying to a message
                            if (replyToMessageId) {
                                formData.append('reply_to_id', replyToMessageId);
                            }

                            // In a real implementation, you would upload to your media service
                            // For now, we'll simulate the upload and show a placeholder
                            await simulateMediaUpload(file, mediaCaption);
                        }

                        // Clear selected files and reset UI
                        selectedFiles = [];
                        updateMediaPreview();
                        updateSendButton();
                        document.getElementById('mediaCaption').value = '';
                        progressContainer.classList.add('hidden');

                        // Clear reply context
                        clearReplyContext();

                        // Reload messages to show the sent media
                        await loadConversationMessages(conversationId);

                        return; // Exit early since we handled media upload differently

                    } catch (error) {
                        console.error('Media upload failed:', error);
                        alert('Failed to upload media. Please try again.');
                        progressContainer.classList.add('hidden');
                        return;
                    }
                }

                // Get conversation details to extract phone number
                const conversationResponse = await fetch(`${API_BASE}/api/conversations/${conversationId}`, {
                    credentials: "include"
                });

                if (!conversationResponse.ok) {
                    throw new Error('Unable to get conversation details');
                }

                const conversation = await conversationResponse.json();
                const phoneNumber = conversation.channel_id; // Assuming channel_id contains phone number

                // Send the message
                const sendResponse = await fetch(endpoint, {
                    method: 'POST',
                    credentials: "include",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone_number: phoneNumber,
                        message_data: messageData
                    })
                });

                if (sendResponse.ok) {
                    const result = await sendResponse.json();

                    // Clear the input
                    if (messageType === 'text') {
                        document.getElementById('messageText').value = '';
                    } else if (messageType === 'template') {
                        document.getElementById('templateSelect').value = '';
                        loadTemplatePreview();
                    }

                    // Clear reply context
                    clearReplyContext();

                    // Reload messages to show the sent message
                    await loadConversationMessages(conversationId);

                    // Show success message
                    showMessage('✅ Message sent successfully!');

                } else {
                    const error = await sendResponse.json();
                    throw new Error(error.detail || 'Failed to send message');
                }

            } catch (error) {
                console.error('Error sending message:', error);
                alert(`❌ Error sending message: ${error.message}`);
            }
        }

        // Logout functionality
        document.addEventListener('click', (e) => {
            if (e.target.id === 'dashboardLogoutBtn') {
                logout();
            }
        });

        async function logout() {
            try {
                const response = await fetch(`${API_BASE}/auth/logout`, {
                    method: "POST",
                    credentials: "include",
                });

                currentUser = null;
                loginContainer.style.display = "block";
                platformDashboard.style.display = "none";
                loginForm.reset();
                showMessage("✅ Logged out successfully!");
            } catch (error) {
                showMessage(`❌ Error during logout: ${error.message}`, true);
            }
        }

        // Test platform button
        document.getElementById('testPlatformBtn')?.addEventListener('click', () => {
            if (currentUser) {
                showPlatform();
            } else {
                showMessage("Please login first", true);
            }
        });

        // Initialize the application when page loads
        window.addEventListener("load", () => {
            initApp();
        });

    </script>
</body>
</html>
