<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Omnichannel Platform - Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: #dc3545;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
        .success {
            color: #155724;
            margin-top: 10px;
            padding: 10px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
        }
        .test-credentials {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-credentials h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .test-credentials p {
            margin: 5px 0;
            font-size: 14px;
        }
        .user-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .button-group button {
            flex: 1;
        }
        .check-btn {
            background-color: #28a745;
        }
        .check-btn:hover {
            background-color: #218838;
        }
        .logout-btn {
            background-color: #dc3545;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        .nav-btn {
            padding: 15px;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        .nav-btn:hover {
            opacity: 0.8;
        }
        .content-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .metric-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .conversation-item, .contact-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .conversation-item h4, .contact-item h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .conversation-item p, .contact-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Omnichannel Platform</h1>

        <div class="status">
            <strong>Backend Status:</strong> <span id="backendStatus">Checking...</span>
        </div>

        <div class="test-credentials">
            <h3>Test Credentials:</h3>
            <p><strong>Super Admin:</strong> <EMAIL> / admin123</p>
            <p><strong>Test User:</strong> <EMAIL> / test123</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" id="loginBtn">Login</button>
        </form>

        <div id="message"></div>
        <div id="userInfo" class="user-info" style="display: none;"></div>

        <div class="button-group">
            <button id="checkAuthBtn" class="check-btn">Check Auth</button>
            <button id="logoutBtn" class="logout-btn">Logout</button>
            <button id="testPlatformBtn" style="background: #17a2b8;">Test Platform</button>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <a href="http://localhost:8000/docs" target="_blank" style="color: #007bff;">
                📚 Open API Documentation
            </a>
        </div>
    </div>

    <!-- Platform Dashboard (hidden initially) -->
    <div id="platformDashboard" class="container" style="display: none;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <h1>🚀 Omnichannel Platform Dashboard</h1>
            <button id="dashboardLogoutBtn" class="logout-btn" style="width: auto; padding: 8px 16px;">
                Logout
            </button>
        </div>

        <div id="userWelcome" style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 30px;"></div>

        <!-- Navigation Menu -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <button class="nav-btn" onclick="showSection('dashboard')" style="background: #007bff;">
                📊 Dashboard
            </button>
            <button class="nav-btn" onclick="showSection('conversations')" style="background: #28a745;">
                💬 Conversations
            </button>
            <button class="nav-btn" onclick="showSection('contacts')" style="background: #17a2b8;">
                👥 Contacts
            </button>
            <button class="nav-btn" onclick="showSection('analytics')" style="background: #ffc107; color: #000;">
                📈 Analytics
            </button>
            <button class="nav-btn" onclick="showSection('settings')" style="background: #6c757d;">
                ⚙️ Settings
            </button>
            <button class="nav-btn" onclick="showSection('integrations')" style="background: #e83e8c;">
                🔗 Integrations
            </button>
        </div>

        <!-- Content Sections -->
        <div id="dashboardSection" class="content-section">
            <h2>📊 Dashboard Overview</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div class="metric-card">
                    <h3>Total Messages</h3>
                    <div class="metric-value" id="totalMessages">Loading...</div>
                </div>
                <div class="metric-card">
                    <h3>Active Conversations</h3>
                    <div class="metric-value" id="activeConversations">Loading...</div>
                </div>
                <div class="metric-card">
                    <h3>Response Rate</h3>
                    <div class="metric-value" id="responseRate">Loading...</div>
                </div>
                <div class="metric-card">
                    <h3>Avg Response Time</h3>
                    <div class="metric-value" id="avgResponseTime">Loading...</div>
                </div>
            </div>
        </div>

        <div id="conversationsSection" class="content-section" style="display: none;">
            <h2>💬 Conversations</h2>
            <div id="conversationsList">Loading conversations...</div>
        </div>

        <div id="contactsSection" class="content-section" style="display: none;">
            <h2>👥 Contacts</h2>
            <div id="contactsList">Loading contacts...</div>
        </div>

        <div id="analyticsSection" class="content-section" style="display: none;">
            <h2>📈 Analytics</h2>
            <div id="analyticsData">Loading analytics...</div>
        </div>

        <div id="settingsSection" class="content-section" style="display: none;">
            <h2>⚙️ Settings</h2>
            <div id="settingsContent">Loading settings...</div>
        </div>

        <div id="integrationsSection" class="content-section" style="display: none;">
            <h2>🔗 Integrations</h2>
            <div id="integrationsContent">Loading integrations...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/api';

        const loginForm = document.getElementById("loginForm");
        const loginBtn = document.getElementById("loginBtn");
        const checkAuthBtn = document.getElementById("checkAuthBtn");
        const logoutBtn = document.getElementById("logoutBtn");
        const testPlatformBtn = document.getElementById("testPlatformBtn");
        const messageDiv = document.getElementById("message");
        const userInfoDiv = document.getElementById("userInfo");
        const backendStatusSpan = document.getElementById("backendStatus");

        function showMessage(message, isError = false) {
            messageDiv.innerHTML = `<div class="${isError ? "error" : "success"}">${message}</div>`;
        }

        function showUserInfo(user) {
            userInfoDiv.innerHTML = `
                <h3>✅ Logged in as:</h3>
                <p><strong>Name:</strong> ${user.first_name} ${user.last_name}</p>
                <p><strong>Email:</strong> ${user.email}</p>
                <p><strong>Role:</strong> ${user.role}</p>
                <p><strong>Company ID:</strong> ${user.company_id}</p>
            `;
            userInfoDiv.style.display = "block";
        }

        function hideUserInfo() {
            userInfoDiv.style.display = "none";
        }

        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    backendStatusSpan.textContent = "✅ Online";
                    backendStatusSpan.style.color = "#28a745";
                } else {
                    backendStatusSpan.textContent = "❌ Error";
                    backendStatusSpan.style.color = "#dc3545";
                }
            } catch (error) {
                backendStatusSpan.textContent = "❌ Offline";
                backendStatusSpan.style.color = "#dc3545";
            }
        }

        loginForm.addEventListener("submit", async (e) => {
            e.preventDefault();

            const email = document.getElementById("email").value;
            const password = document.getElementById("password").value;

            loginBtn.disabled = true;
            loginBtn.textContent = "Logging in...";

            try {
                const response = await fetch(`${API_BASE}/auth/token`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    credentials: "include",
                    body: JSON.stringify({ email, password }),
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log("Login successful, user data:", data.user);
                    showMessage("✅ Login successful! Opening platform...");
                    showUserInfo(data.user);
                    loginForm.reset();
                    // Show the platform dashboard immediately
                    setTimeout(() => {
                        console.log("Showing platform for user:", data.user);
                        showPlatform(data.user);
                    }, 500);
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    showMessage(`❌ ${errorData.detail || "Login failed"}`, true);
                    hideUserInfo();
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, true);
                hideUserInfo();
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = "Login";
            }
        });

        checkAuthBtn.addEventListener("click", async () => {
            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    credentials: "include",
                });

                if (response.ok) {
                    const user = await response.json();
                    showMessage("✅ Authentication valid!");
                    showUserInfo(user);
                    // Show the platform dashboard if authenticated
                    showPlatform(user);
                } else {
                    showMessage("❌ Not authenticated", true);
                    hideUserInfo();
                }
            } catch (error) {
                showMessage(`❌ Error checking authentication: ${error.message}`, true);
                hideUserInfo();
            }
        });

        logoutBtn.addEventListener("click", async () => {
            try {
                const response = await fetch(`${API_BASE}/auth/logout`, {
                    method: "POST",
                    credentials: "include",
                });

                showMessage("✅ Logged out successfully!");
                hideUserInfo();
                loginForm.reset();
            } catch (error) {
                showMessage(`❌ Error during logout: ${error.message}`, true);
            }
        });

        // Dashboard functionality
        const loginContainer = document.querySelector('.container');
        const dashboardContainer = document.getElementById('platformDashboard');
        const dashboardLogoutBtn = document.getElementById('dashboardLogoutBtn');
        const userWelcome = document.getElementById('userWelcome');

        function showPlatform(user) {
            console.log("showPlatform called with user:", user);
            console.log("loginContainer:", loginContainer);
            console.log("dashboardContainer:", dashboardContainer);

            if (!loginContainer || !dashboardContainer) {
                console.error("Required containers not found!");
                return;
            }

            loginContainer.style.display = 'none';
            dashboardContainer.style.display = 'block';
            userWelcome.innerHTML = `
                <h3>Welcome back, ${user.first_name} ${user.last_name}!</h3>
                <p>Role: ${user.role} | Company ID: ${user.company_id}</p>
            `;
            console.log("Platform should now be visible");
            loadDashboardData();
        }

        function hidePlatform() {
            loginContainer.style.display = 'block';
            dashboardContainer.style.display = 'none';
        }

        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // Show selected section
            document.getElementById(sectionName + 'Section').style.display = 'block';

            // Load section data
            switch(sectionName) {
                case 'dashboard':
                    loadDashboardData();
                    break;
                case 'conversations':
                    loadConversations();
                    break;
                case 'contacts':
                    loadContacts();
                    break;
                case 'analytics':
                    loadAnalytics();
                    break;
                case 'settings':
                    loadSettings();
                    break;
                case 'integrations':
                    loadIntegrations();
                    break;
            }
        }

        async function loadDashboardData() {
            try {
                const response = await fetch(`${API_BASE}/analytics/dashboard-metrics`);
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('totalMessages').textContent = data.total_messages || '0';
                    document.getElementById('activeConversations').textContent = data.active_conversations || '0';
                    document.getElementById('responseRate').textContent = (data.response_rate || 0) + '%';
                    document.getElementById('avgResponseTime').textContent = (data.avg_response_time || 0) + 'min';
                } else {
                    // Show sample data if API fails
                    document.getElementById('totalMessages').textContent = '1,234';
                    document.getElementById('activeConversations').textContent = '56';
                    document.getElementById('responseRate').textContent = '94%';
                    document.getElementById('avgResponseTime').textContent = '2.3min';
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                // Show sample data
                document.getElementById('totalMessages').textContent = '1,234';
                document.getElementById('activeConversations').textContent = '56';
                document.getElementById('responseRate').textContent = '94%';
                document.getElementById('avgResponseTime').textContent = '2.3min';
            }
        }

        async function loadConversations() {
            const conversationsList = document.getElementById('conversationsList');
            conversationsList.innerHTML = `
                <div class="conversation-item">
                    <h4>Customer Support - John Doe</h4>
                    <p>Last message: "Thank you for your help!" - 2 minutes ago</p>
                </div>
                <div class="conversation-item">
                    <h4>Sales Inquiry - Jane Smith</h4>
                    <p>Last message: "I'm interested in your premium plan" - 15 minutes ago</p>
                </div>
                <div class="conversation-item">
                    <h4>Technical Support - Mike Johnson</h4>
                    <p>Last message: "The integration is working perfectly now" - 1 hour ago</p>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #666;">
                    💡 This is sample data. Real conversations will be loaded from the API.
                </p>
            `;
        }

        async function loadContacts() {
            const contactsList = document.getElementById('contactsList');
            contactsList.innerHTML = `
                <div class="contact-item">
                    <h4>John Doe</h4>
                    <p>Email: <EMAIL> | Phone: +****************</p>
                </div>
                <div class="contact-item">
                    <h4>Jane Smith</h4>
                    <p>Email: <EMAIL> | Phone: +****************</p>
                </div>
                <div class="contact-item">
                    <h4>Mike Johnson</h4>
                    <p>Email: <EMAIL> | Phone: +****************</p>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #666;">
                    💡 This is sample data. Real contacts will be loaded from the API.
                </p>
            `;
        }

        async function loadAnalytics() {
            const analyticsData = document.getElementById('analyticsData');
            analyticsData.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="metric-card">
                        <h3>Messages This Week</h3>
                        <div class="metric-value">2,847</div>
                    </div>
                    <div class="metric-card">
                        <h3>New Customers</h3>
                        <div class="metric-value">127</div>
                    </div>
                    <div class="metric-card">
                        <h3>Conversion Rate</h3>
                        <div class="metric-value">12.4%</div>
                    </div>
                    <div class="metric-card">
                        <h3>Customer Satisfaction</h3>
                        <div class="metric-value">4.8/5</div>
                    </div>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #666;">
                    💡 This is sample data. Real analytics will be loaded from the API.
                </p>
            `;
        }

        async function loadSettings() {
            const settingsContent = document.getElementById('settingsContent');
            settingsContent.innerHTML = `
                <div style="max-width: 600px;">
                    <h3>Account Settings</h3>
                    <div style="margin-bottom: 20px;">
                        <label>Company Name:</label>
                        <input type="text" value="Default Company" style="width: 100%; margin-top: 5px;">
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label>Notification Preferences:</label>
                        <div style="margin-top: 10px;">
                            <label><input type="checkbox" checked> Email notifications</label><br>
                            <label><input type="checkbox" checked> SMS notifications</label><br>
                            <label><input type="checkbox"> Push notifications</label>
                        </div>
                    </div>
                    <button style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
                        Save Settings
                    </button>
                </div>
                <p style="margin-top: 20px; color: #666;">
                    💡 This is a demo interface. Real settings will be loaded from the API.
                </p>
            `;
        }

        async function loadIntegrations() {
            const integrationsContent = document.getElementById('integrationsContent');
            integrationsContent.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div class="metric-card">
                        <h3>WhatsApp Business</h3>
                        <div style="color: #28a745; font-weight: bold;">✅ Connected</div>
                        <button style="margin-top: 10px; padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px;">
                            Configure
                        </button>
                    </div>
                    <div class="metric-card">
                        <h3>Facebook Messenger</h3>
                        <div style="color: #dc3545; font-weight: bold;">❌ Not Connected</div>
                        <button style="margin-top: 10px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;">
                            Connect
                        </button>
                    </div>
                    <div class="metric-card">
                        <h3>Telegram</h3>
                        <div style="color: #dc3545; font-weight: bold;">❌ Not Connected</div>
                        <button style="margin-top: 10px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;">
                            Connect
                        </button>
                    </div>
                    <div class="metric-card">
                        <h3>Email</h3>
                        <div style="color: #28a745; font-weight: bold;">✅ Connected</div>
                        <button style="margin-top: 10px; padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px;">
                            Configure
                        </button>
                    </div>
                </div>
                <p style="text-align: center; margin-top: 20px; color: #666;">
                    💡 This is a demo interface. Real integrations will be loaded from the API.
                </p>
            `;
        }

        // Test platform button
        testPlatformBtn.addEventListener("click", () => {
            console.log("Test platform button clicked");
            const testUser = {
                first_name: "Test",
                last_name: "User",
                email: "<EMAIL>",
                role: "admin",
                company_id: 1
            };
            showPlatform(testUser);
        });

        // Dashboard logout
        dashboardLogoutBtn.addEventListener("click", async () => {
            try {
                await fetch(`${API_BASE}/auth/logout`, {
                    method: "POST",
                    credentials: "include",
                });
                hidePlatform();
                showMessage("✅ Logged out successfully!");
                hideUserInfo();
                loginForm.reset();
            } catch (error) {
                showMessage(`❌ Error during logout: ${error.message}`, true);
            }
        });

        // Check backend status and authentication on page load
        window.addEventListener("load", () => {
            checkBackendStatus();
            checkAuthBtn.click();
        });
    </script>
</body>
</html>
