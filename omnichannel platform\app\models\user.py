"""User authentication and management models."""


from sqlmodel import Field, Relationship, Column, SQLModel
from sqlalchemy import String, Boolean, Enum, Text, ForeignKey, DateTime, Integer
from enum import Enum as PyEnum
from datetime import datetime
from typing import Optional, List, TYPE_CHECKING

if TYPE_CHECKING:
    from .whatsapp import WhatsAppAccount
    from .company import Company
    from .analytics import MessageAnalytics


class UserRole(PyEnum):
    """User role for permission management."""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    MANAGER = "manager"
    USER = "user"


class UserStatus(PyEnum):
    """User account status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING_VERIFICATION = "pending_verification"
    SUSPENDED = "suspended"


class User(SQLModel, table=True):
    """User model for authentication and authorization."""
    __tablename__ = "users"
    __table_args__ = {"extend_existing": True}

    # Primary key
    id: Optional[int] = Field(default=None, primary_key=True)

    # Multi-tenancy
    company_id: int = Field(sa_column=Column(Integer, ForeignKey("companies.id", ondelete="CASCADE"), nullable=False, index=True))
    company: Optional["Company"] = Relationship(back_populates="users")

    # Basic information
    email: str = Field(sa_column=Column(String(255), unique=True, index=True, nullable=False))
    username: str = Field(sa_column=Column(String(100), unique=True, index=True, nullable=False))
    hashed_password: str = Field(sa_column=Column(String(255), nullable=False))
    
    # Profile information
    first_name: str = Field(sa_column=Column(String(100), nullable=False))
    last_name: str = Field(sa_column=Column(String(100), nullable=False))
    phone_number: Optional[str] = Field(default=None, sa_column=Column(String(20), nullable=True))
    avatar_url: Optional[str] = Field(default=None, sa_column=Column(String(500), nullable=True))
    bio: Optional[str] = Field(default=None, sa_column=Column(Text, nullable=True))
    
    # Account settings
    role: UserRole = Field(sa_column=Column(Enum(UserRole), default=UserRole.USER))
    status: UserStatus = Field(sa_column=Column(Enum(UserStatus), default=UserStatus.PENDING_VERIFICATION))
    is_email_verified: bool = Field(default=False, sa_column=Column(Boolean, default=False, nullable=False))
    is_phone_verified: bool = Field(default=False, sa_column=Column(Boolean, default=False, nullable=False))
    
    # Security settings
    two_factor_enabled: bool = Field(default=False, sa_column=Column(Boolean, default=False, nullable=False))
    two_factor_secret: Optional[str] = Field(default=None, sa_column=Column(String(255), nullable=True))
    last_login: Optional[datetime] = Field(default=None, sa_column=Column(DateTime(timezone=True), nullable=True))
    failed_login_attempts: int = Field(default=0, sa_column=Column(Integer, default=0, nullable=False))
    locked_until: Optional[datetime] = Field(default=None, sa_column=Column(DateTime(timezone=True), nullable=True))
    
    # Preferences
    timezone: str = Field(default="UTC", sa_column=Column(String(50), default="UTC", nullable=False))
    language: str = Field(default="en", sa_column=Column(String(10), default="en", nullable=False))
    theme: str = Field(default="light", sa_column=Column(String(20), default="light", nullable=False))
    
    # Email preferences
    email_notifications: bool = Field(default=True, sa_column=Column(Boolean, default=True, nullable=False))
    marketing_emails: bool = Field(default=False, sa_column=Column(Boolean, default=False, nullable=False))
    
    # Relationships
    whatsapp_accounts: List["WhatsAppAccount"] = Relationship(back_populates="user")
    telegram_accounts: List["TelegramAccount"] = Relationship(back_populates="user")
    sessions: List["UserSession"] = Relationship(back_populates="user")
    message_analytics: List["MessageAnalytics"] = Relationship(back_populates="user")
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.role in [UserRole.SUPER_ADMIN, UserRole.ADMIN]
    
    @property
    def is_manager(self) -> bool:
        """Check if user has manager privileges."""
        return self.role in [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER]
    
    @property
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def can_access_feature(self, feature: str) -> bool:
        """Check if user can access a specific feature."""
        feature_permissions = {
            "admin_panel": [UserRole.SUPER_ADMIN, UserRole.ADMIN],
            "user_management": [UserRole.SUPER_ADMIN, UserRole.ADMIN],
            "campaign_management": [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER],
            "bot_testing": [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER, UserRole.USER],
            "analytics": [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER, UserRole.USER],
            "bulk_messaging": [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER, UserRole.USER],
        }
        
        allowed_roles = feature_permissions.get(feature, [])
        return self.role in allowed_roles


class UserSession(SQLModel, table=True):
    """User session tracking for security and analytics."""
    __tablename__ = "user_sessions"
    __table_args__ = {"extend_existing": True}
    
    # Primary key
    id: Optional[int] = Field(default=None, primary_key=True)

    # Foreign key to user
    user_id: int = Field(sa_column=Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True))
    
    # Session information
    session_token: str = Field(sa_column=Column(String(255), unique=True, index=True, nullable=False))
    ip_address: Optional[str] = Field(default=None, sa_column=Column(String(50), nullable=True))
    user_agent: Optional[str] = Field(default=None, sa_column=Column(String(500), nullable=True))
    device_type: Optional[str] = Field(default=None, sa_column=Column(String(50), nullable=True))
    
    # Session timing
    created_at: datetime = Field(sa_column=Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False))
    expires_at: datetime = Field(sa_column=Column(DateTime(timezone=True), nullable=False))
    last_activity: datetime = Field(sa_column=Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False))
    
    # Session status
    is_active: bool = Field(default=True, sa_column=Column(Boolean, default=True, nullable=False))
    
    # Relationship
    user: User = Relationship(back_populates="sessions")
