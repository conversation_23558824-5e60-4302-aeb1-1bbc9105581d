import React, { useState } from 'react';
import {
  FormControl,
  FormLabel,
  Input,
  Button,
  VStack,
  useToast,
} from '@chakra-ui/react';
import axios from 'axios';

interface TelegramConnectorProps {
  onSuccess: () => void;
}

const TelegramConnector: React.FC<TelegramConnectorProps> = ({ onSuccess }) => {
  const [accountName, setAccountName] = useState('');
  const [botToken, setBotToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await axios.post('/api/telegram/accounts/', {
        account_name: accountName,
        bot_token: botToken,
      });
      toast({
        title: 'Telegram Account Created',
        description: 'Your Telegram bot has been configured.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      onSuccess();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.detail || 'Failed to configure Telegram bot. Please check your token and try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <VStack as="form" spacing={4} align="stretch">
      <FormControl isRequired>
        <FormLabel>Account Name</FormLabel>
        <Input 
          type="text" 
          value={accountName} 
          onChange={(e) => setAccountName(e.target.value)} 
          placeholder="e.g., My Telegram Bot"
        />
      </FormControl>
      <FormControl isRequired>
        <FormLabel>Bot Token</FormLabel>
        <Input 
          type="password" 
          value={botToken} 
          onChange={(e) => setBotToken(e.target.value)} 
          placeholder="Enter your Telegram Bot Token"
        />
      </FormControl>
      <Button 
        colorScheme="blue" 
        onClick={handleSave} 
        isLoading={isLoading}
      >
        Save Configuration
      </Button>
    </VStack>
  );
};

export default TelegramConnector;
