import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box, Button, FormControl, FormLabel, Input, Heading, Alert, AlertIcon, VStack, Text
} from '@chakra-ui/react';
import { useAuth } from '../components/AuthContext';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const success = await login(email, password);
      if (success) {
        const from = location.state?.from?.pathname || '/';
        navigate(from, { replace: true });
      } else {
        setError('Invalid email or password');
      }
    } catch (err) {
      setError('Network error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box maxW="sm" mx="auto" mt={24} p={8} borderWidth={1} borderRadius="lg" boxShadow="lg">
      <Heading mb={6} size="lg">Sign in to your account</Heading>

      {/* Test Credentials */}
      <Box mb={6} p={4} bg="blue.50" borderRadius="md" borderLeft="4px" borderLeftColor="blue.400">
        <Text fontSize="sm" fontWeight="bold" mb={2}>Test Credentials:</Text>
        <Text fontSize="xs" mb={1}><strong>Super Admin:</strong> <EMAIL> / admin123</Text>
        <Text fontSize="xs"><strong>Test User:</strong> <EMAIL> / test123</Text>
      </Box>
      <form onSubmit={handleSubmit}>
        <VStack spacing={4} align="stretch">
          <FormControl id="email" isRequired>
            <FormLabel>Email address</FormLabel>
            <Input type="email" value={email} onChange={e => setEmail(e.target.value)} autoFocus />
          </FormControl>
          <FormControl id="password" isRequired>
            <FormLabel>Password</FormLabel>
            <Input type="password" value={password} onChange={e => setPassword(e.target.value)} />
          </FormControl>
          {error && (
            <Alert status="error">
              <AlertIcon />
              {error}
            </Alert>
          )}
          <Button type="submit" colorScheme="blue" isLoading={loading} width="full">
            Sign In
          </Button>
        </VStack>
      </form>
    </Box>
  );
};

export default Login;
