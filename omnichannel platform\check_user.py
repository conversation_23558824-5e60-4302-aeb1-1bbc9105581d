import sqlite3

def check_admin_user(db_path, email):
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, email, hashed_password, role FROM user WHERE email = ?", (email,))
        user = cursor.fetchone()
        if user:
            print(f"User found: ID={user[0]}, Email={user[1]}, Hashed Password={user[2]}, Role={user[3]}")
            return True
        else:
            print(f"User with email {email} not found.")
            return False
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    db_file = "F:\\great_things\\omnichannel platform\\whatsapp_platform_dev.db"
    admin_email = "<EMAIL>"
    check_admin_user(db_file, admin_email)