<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Function Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/function.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="There are a few functions specific to Python functions." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="There are a few functions specific to Python functions." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Function Objects &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Instance Method Objects" href="method.html" />
    <link rel="prev" title="Set Objects" href="set.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/function.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="set.html"
                          title="previous chapter">Set Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="method.html"
                          title="next chapter">Instance Method Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/function.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="method.html" title="Instance Method Objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="set.html" title="Set Objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" accesskey="U">Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Function Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="function-objects">
<span id="id1"></span><h1>Function Objects<a class="headerlink" href="#function-objects" title="Link to this heading">¶</a></h1>
<p id="index-0">There are a few functions specific to Python functions.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyFunctionObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunctionObject</span></span></span><a class="headerlink" href="#c.PyFunctionObject" title="Link to this definition">¶</a><br /></dt>
<dd><p>The C structure used for functions.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyFunction_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_Type</span></span></span><a class="headerlink" href="#c.PyFunction_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-1">This is an instance of <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> and represents the Python function
type.  It is exposed to Python programmers as <code class="docutils literal notranslate"><span class="pre">types.FunctionType</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>o</em> is a function object (has type <a class="reference internal" href="#c.PyFunction_Type" title="PyFunction_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyFunction_Type</span></code></a>).
The parameter must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_New">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_New</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">code</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Return a new function object associated with the code object <em>code</em>. <em>globals</em>
must be a dictionary with the global variables accessible to the function.</p>
<p>The function’s docstring and name are retrieved from the code object.
<a class="reference internal" href="../reference/datamodel.html#function.__module__" title="function.__module__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__module__</span></code></a>
is retrieved from <em>globals</em>. The argument defaults, annotations and closure are
set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. <a class="reference internal" href="../reference/datamodel.html#function.__qualname__" title="function.__qualname__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__qualname__</span></code></a> is set to the same value as
the code object’s <a class="reference internal" href="../reference/datamodel.html#codeobject.co_qualname" title="codeobject.co_qualname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">co_qualname</span></code></a> field.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_NewWithQualName">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_NewWithQualName</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">code</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">qualname</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_NewWithQualName" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>As <a class="reference internal" href="#c.PyFunction_New" title="PyFunction_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFunction_New()</span></code></a>, but also allows setting the function object’s
<a class="reference internal" href="../reference/datamodel.html#function.__qualname__" title="function.__qualname__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__qualname__</span></code></a> attribute.
<em>qualname</em> should be a unicode object or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>;
if <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the <code class="xref py py-attr docutils literal notranslate"><span class="pre">__qualname__</span></code> attribute is set to the same value as
the code object’s <a class="reference internal" href="../reference/datamodel.html#codeobject.co_qualname" title="codeobject.co_qualname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">co_qualname</span></code></a> field.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_GetCode">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_GetCode</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_GetCode" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return the code object associated with the function object <em>op</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_GetGlobals">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_GetGlobals</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_GetGlobals" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return the globals dictionary associated with the function object <em>op</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_GetModule">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_GetModule</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_GetModule" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return a <a class="reference internal" href="../glossary.html#term-borrowed-reference"><span class="xref std std-term">borrowed reference</span></a> to the <a class="reference internal" href="../reference/datamodel.html#function.__module__" title="function.__module__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__module__</span></code></a>
attribute of the <a class="reference internal" href="../reference/datamodel.html#user-defined-funcs"><span class="std std-ref">function object</span></a> <em>op</em>.
It can be <em>NULL</em>.</p>
<p>This is normally a <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">string</span></code></a> containing the module name,
but can be set to any other object by Python code.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_GetDefaults">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_GetDefaults</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_GetDefaults" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return the argument default values of the function object <em>op</em>. This can be a
tuple of arguments or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_SetDefaults">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_SetDefaults</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">defaults</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_SetDefaults" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the argument default values for the function object <em>op</em>. <em>defaults</em> must be
<code class="docutils literal notranslate"><span class="pre">Py_None</span></code> or a tuple.</p>
<p>Raises <a class="reference internal" href="../library/exceptions.html#SystemError" title="SystemError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemError</span></code></a> and returns <code class="docutils literal notranslate"><span class="pre">-1</span></code> on failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_SetVectorcall">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_SetVectorcall</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyFunctionObject" title="PyFunctionObject"><span class="n"><span class="pre">PyFunctionObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">func</span></span>, <a class="reference internal" href="call.html#c.vectorcallfunc" title="vectorcallfunc"><span class="n"><span class="pre">vectorcallfunc</span></span></a><span class="w"> </span><span class="n"><span class="pre">vectorcall</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_SetVectorcall" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the vectorcall field of a given function object <em>func</em>.</p>
<p>Warning: extensions using this API must preserve the behavior
of the unaltered (default) vectorcall function!</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_GetClosure">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_GetClosure</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_GetClosure" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return the closure associated with the function object <em>op</em>. This can be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>
or a tuple of cell objects.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_SetClosure">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_SetClosure</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">closure</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_SetClosure" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the closure associated with the function object <em>op</em>. <em>closure</em> must be
<code class="docutils literal notranslate"><span class="pre">Py_None</span></code> or a tuple of cell objects.</p>
<p>Raises <a class="reference internal" href="../library/exceptions.html#SystemError" title="SystemError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemError</span></code></a> and returns <code class="docutils literal notranslate"><span class="pre">-1</span></code> on failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_GetAnnotations">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_GetAnnotations</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_GetAnnotations" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Return the annotations of the function object <em>op</em>. This can be a
mutable dictionary or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_SetAnnotations">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_SetAnnotations</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">annotations</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_SetAnnotations" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the annotations for the function object <em>op</em>. <em>annotations</em>
must be a dictionary or <code class="docutils literal notranslate"><span class="pre">Py_None</span></code>.</p>
<p>Raises <a class="reference internal" href="../library/exceptions.html#SystemError" title="SystemError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemError</span></code></a> and returns <code class="docutils literal notranslate"><span class="pre">-1</span></code> on failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_AddWatcher">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_AddWatcher</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyFunction_WatchCallback" title="PyFunction_WatchCallback"><span class="n"><span class="pre">PyFunction_WatchCallback</span></span></a><span class="w"> </span><span class="n"><span class="pre">callback</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_AddWatcher" title="Link to this definition">¶</a><br /></dt>
<dd><p>Register <em>callback</em> as a function watcher for the current interpreter.
Return an ID which may be passed to <a class="reference internal" href="#c.PyFunction_ClearWatcher" title="PyFunction_ClearWatcher"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFunction_ClearWatcher()</span></code></a>.
In case of error (e.g. no more watcher IDs available),
return <code class="docutils literal notranslate"><span class="pre">-1</span></code> and set an exception.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyFunction_ClearWatcher">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_ClearWatcher</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">watcher_id</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyFunction_ClearWatcher" title="Link to this definition">¶</a><br /></dt>
<dd><p>Clear watcher identified by <em>watcher_id</em> previously returned from
<a class="reference internal" href="#c.PyFunction_AddWatcher" title="PyFunction_AddWatcher"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFunction_AddWatcher()</span></code></a> for the current interpreter.
Return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, or <code class="docutils literal notranslate"><span class="pre">-1</span></code> and set an exception on error
(e.g.  if the given <em>watcher_id</em> was never registered.)</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyFunction_WatchEvent">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_WatchEvent</span></span></span><a class="headerlink" href="#c.PyFunction_WatchEvent" title="Link to this definition">¶</a><br /></dt>
<dd><blockquote>
<div><p>Enumeration of possible function watcher events:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_CREATE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_DESTROY</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_MODIFY_CODE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_MODIFY_DEFAULTS</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_MODIFY_KWDEFAULTS</span></code></p></li>
</ul>
</div></blockquote>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyFunction_WatchCallback">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyFunction_WatchCallback</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="#c.PyFunction_WatchEvent" title="PyFunction_WatchEvent"><span class="n"><span class="pre">PyFunction_WatchEvent</span></span></a><span class="w"> </span><span class="n"><span class="pre">event</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="#c.PyFunctionObject" title="PyFunctionObject"><span class="n"><span class="pre">PyFunctionObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">func</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">new_value</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.PyFunction_WatchCallback" title="Link to this definition">¶</a><br /></dt>
<dd><p>Type of a function watcher callback function.</p>
<p>If <em>event</em> is <code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_CREATE</span></code> or <code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_DESTROY</span></code>
then <em>new_value</em> will be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. Otherwise, <em>new_value</em> will hold a
<a class="reference internal" href="../glossary.html#term-borrowed-reference"><span class="xref std std-term">borrowed reference</span></a> to the new value that is about to be stored in
<em>func</em> for the attribute that is being modified.</p>
<p>The callback may inspect but must not modify <em>func</em>; doing so could have
unpredictable effects, including infinite recursion.</p>
<p>If <em>event</em> is <code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_CREATE</span></code>, then the callback is invoked
after <em>func</em> has been fully initialized. Otherwise, the callback is invoked
before the modification to <em>func</em> takes place, so the prior state of <em>func</em>
can be inspected. The runtime is permitted to optimize away the creation of
function objects when possible. In such cases no event will be emitted.
Although this creates the possibility of an observable difference of
runtime behavior depending on optimization decisions, it does not change
the semantics of the Python code being executed.</p>
<p>If <em>event</em> is <code class="docutils literal notranslate"><span class="pre">PyFunction_EVENT_DESTROY</span></code>,  Taking a reference in the
callback to the about-to-be-destroyed function will resurrect it, preventing
it from being freed at this time. When the resurrected object is destroyed
later, any watcher callbacks active at that time will be called again.</p>
<p>If the callback sets an exception, it must return <code class="docutils literal notranslate"><span class="pre">-1</span></code>; this exception will
be printed as an unraisable exception using <a class="reference internal" href="exceptions.html#c.PyErr_WriteUnraisable" title="PyErr_WriteUnraisable"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_WriteUnraisable()</span></code></a>.
Otherwise it should return <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<p>There may already be a pending exception set on entry to the callback. In
this case, the callback should return <code class="docutils literal notranslate"><span class="pre">0</span></code> with the same exception still
set. This means the callback may not call any other API that can set an
exception unless it saves and clears the exception state first, and restores
it before returning.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.12.</span></p>
</div>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="set.html"
                          title="previous chapter">Set Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="method.html"
                          title="next chapter">Instance Method Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/function.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="method.html" title="Instance Method Objects"
             >next</a> |</li>
        <li class="right" >
          <a href="set.html" title="Set Objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" >Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Function Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>