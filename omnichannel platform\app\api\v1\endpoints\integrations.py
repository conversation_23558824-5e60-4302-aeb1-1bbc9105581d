from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas
from app.schemas import integrations
from app import services # Assuming services.integration_service will be found
# from app import models # models.integrations might not be directly needed here if service layer handles all
from app.db.session import get_db # Or however get_db is structured
from app.api.security import get_current_user
from app.models.user import User
from app.services.exceptions import BulkMessagingError

router = APIRouter()

@router.post("/whatsapp", response_model=schemas.integration.IntegrationRead)
def configure_whatsapp(
    *,
    db: Session = Depends(get_db),
    creds_in: integrations.WhatsAppCredentialsCreate,
        current_user: User = Depends(get_current_user)
) -> Any:
    """
    Configure WhatsApp integration. Creates or updates existing WhatsApp settings.
    Activates WhatsApp and deactivates other primary messaging channels.
    """
    try:
        integration_setting = services.integration_service.create_or_update_whatsapp_integration(
            db=db, creds_in=creds_in, company_id=current_user.company_id
        )
        return schemas.integration.IntegrationRead.from_orm(integration_setting)
    except BulkMessagingError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except ValueError as e: # For Pydantic validation errors or other value errors
        raise HTTPException(status_code=422, detail=str(e))


@router.get("/", response_model=List[schemas.integration.IntegrationRead])
def read_integrations(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Retrieve all integration configurations for the user.
    Sensitive data like access tokens are not included.
    """
    db_integrations = services.integration_service.get_integrations(db, skip=skip, limit=limit, company_id=current_user.company_id)
    return [schemas.integration.IntegrationRead.from_orm(integ) for integ in db_integrations]

@router.put("/{integration_id}/status", response_model=schemas.integration.IntegrationRead)
def update_integration_status(
    *,
    db: Session = Depends(get_db),
    integration_id: int,
    is_active: bool, # Pass is_active as a query parameter or in a small request body
        current_user: User = Depends(get_current_user)
) -> Any:
    """
    Activate or deactivate an integration.
    Ensures exclusivity if activating a primary messaging channel (WhatsApp/Test Sender).
    """
    try:
        updated_integration = services.integration_service.toggle_integration_status(
            db=db, integration_id=integration_id, activate=is_active, company_id=current_user.company_id
        )
        if not updated_integration:
             raise HTTPException(status_code=404, detail="Integration not found")
        return schemas.integration.IntegrationRead.from_orm(updated_integration)
    except BulkMessagingError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{integration_id}", response_model=schemas.integration.IntegrationRead)
def read_integration(
    *,
    db: Session = Depends(get_db),
    integration_id: int,
        current_user: User = Depends(get_current_user)
) -> Any:
    """
    Retrieve details for a specific integration.
    """
    db_integration = services.integration_service.get_integration(db, setting_id=integration_id, company_id=current_user.company_id)
    if not db_integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    return schemas.integration.IntegrationRead.from_orm(db_integration)
