"""Main FastAPI application for the WhatsApp Business Platform API."""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .whatsapp import router as whatsapp_router
from .bot_flows import router as bot_flows_router
from .telegram import router as telegram_router
from .telegram_webhooks import router as telegram_webhooks_router
from .bulk import router as bulk_router
from .company import router as company_router
from .user import router as user_router
from .integration import router as integration_router
from .onboarding import router as onboarding_router
from .inbox import router as inbox_router
from .analytics import router as analytics_router
from .auth import router as auth_router
from .endpoints.conversations import router as conversations_router
from app.api.v1.endpoints import integrations as v1_integrations_router # New V1 router

def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title="WhatsApp Business Platform API",
        description="API for managing WhatsApp Business Platform resources",
        version="1.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:5174", "http://127.0.0.1:5174"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(whatsapp_router, prefix="/api")
    app.include_router(telegram_router, prefix="/api")
    app.include_router(telegram_webhooks_router, prefix="/api")
    app.include_router(bot_flows_router, prefix="/api")
    app.include_router(bulk_router, prefix="/api")
    app.include_router(company_router, prefix="/api")
    app.include_router(user_router, prefix="/api")
    app.include_router(integration_router, prefix="/api")
    app.include_router(onboarding_router, prefix="/api")
    app.include_router(inbox_router, prefix="/api")
    app.include_router(analytics_router, prefix="/api")
    app.include_router(auth_router, prefix="/api")
    app.include_router(conversations_router, prefix="/api/conversations")

    # Include the new V1 router for specific integration settings
    app.include_router(v1_integrations_router.router, prefix="/api/v1/integrations", tags=["v1_integrations"])

    # Health check endpoint
    @app.get("/api/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "ok"}

    return app

# Create the FastAPI application
app = create_app()
