import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';

// Define the base URL for API requests
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// Define interface for API request options
interface ApiRequestOptions extends AxiosRequestConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: any;
  params?: any;
  headers?: Record<string, string>;
}

// Define interface for API response
export interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

/**
 * Make an API request with the given options
 * @param options Request options
 * @returns Promise with the response data
 */
export const apiRequest = async <T = any>(options: ApiRequestOptions): Promise<ApiResponse<T>> => {
  try {
    // Get the auth token from localStorage if available
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Make the request
    const response: AxiosResponse<T> = await axios({
      baseURL: API_BASE_URL,
      ...options,
      headers,
    });

    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>,
    };
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      // Return the error response
      return {
        data: error.response.data as T,
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers as Record<string, string>,
      };
    }
    
    // Re-throw other errors
    throw error;
  }
};

// Dashboard API functions
export const getDashboardMetrics = async (): Promise<ApiResponse<any>> => {
  return apiRequest({
    url: '/analytics/dashboard-metrics-test',
    method: 'GET',
  });
};
