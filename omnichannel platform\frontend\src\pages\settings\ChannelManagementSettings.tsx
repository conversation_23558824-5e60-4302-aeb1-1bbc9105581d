import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Heading,
  VStack,
  Text,
  Spinner,
  useToast,
  SimpleGrid,
  Card,
  CardBody,
  Flex,
  Spacer,
  Switch,
  FormControl,
  FormLabel,
} from '@chakra-ui/react';
import { listChannels, updateChannel } from '../../api/inboxApi';
import type { Channel } from '../../api/inboxApi';

const ChannelManagementSettings: React.FC = () => {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();

  const fetchChannels = async () => {
    try {
      const data = await listChannels();
      setChannels(data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch channels:', err);
      setError('Failed to load channels. Please try again later.');
      toast({
        title: 'Error',
        description: 'Failed to fetch channels. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchChannels();
  }, []);

  const handleToggleActive = async (channelId: number, currentStatus: boolean) => {
    try {
      // Optimistic update
      setChannels(prevChannels =>
        prevChannels.map(channel =>
          channel.id === channelId ? { ...channel, is_active: !currentStatus } : channel
        )
      );

      await updateChannel(channelId, { is_active: !currentStatus });
      toast({
        title: 'Channel Updated',
        description: `Channel status toggled successfully.`, 
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Failed to update channel status:', err);
      // Revert optimistic update on error
      setChannels(prevChannels =>
        prevChannels.map(channel =>
          channel.id === channelId ? { ...channel, is_active: currentStatus } : channel
        )
      );
      toast({
        title: 'Error',
        description: 'Failed to update channel status. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  if (isLoading) {
    return (
      <Flex justify="center" align="center" minH="200px">
        <Spinner size="xl" />
      </Flex>
    );
  }

  if (error) {
    return <Text color="red.500">{error}</Text>;
  }

  return (
    <Box>
      <Heading as="h3" size="lg" mb={4}>Manage Channels</Heading>
      {channels.length === 0 ? (
        <Text>No channels found. Add channels from the Inbox section.</Text>
      ) : (
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
          {channels.map((channel) => (
            <Card key={channel.id} p={4} borderWidth="1px" borderRadius="lg">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Flex width="100%" alignItems="center">
                    <Heading size="md">{channel.name}</Heading>
                    <Spacer />
                    <FormControl display="flex" alignItems="center">
                      <FormLabel htmlFor={`toggle-channel-${channel.id}`} mb="0">
                        {channel.is_active ? 'Active' : 'Inactive'}
                      </FormLabel>
                      <Switch
                        id={`toggle-channel-${channel.id}`}
                        isChecked={channel.is_active}
                        onChange={() => handleToggleActive(channel.id, channel.is_active)}
                      />
                    </FormControl>
                  </Flex>
                  <Text fontSize="sm" color="gray.500">Type: {channel.channel_type}</Text>
                  <Text fontSize="sm" color="gray.500">Status: {channel.status}</Text>
                  {channel.description && (
                    <Text fontSize="sm" color="gray.600">{channel.description}</Text>
                  )}
                </VStack>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>
      )}
    </Box>
  );
};

export default ChannelManagementSettings;
