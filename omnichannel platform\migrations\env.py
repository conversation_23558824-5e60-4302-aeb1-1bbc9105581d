"""Alembic migration environment configuration."""
import os
import sys
from logging.config import fileConfig
from pathlib import Path

# Add the project root to the Python path
project_root = str(Path(__file__).parent.parent.absolute())
sys.path.insert(0, project_root)

from sqlalchemy import engine_from_config, pool
from alembic import context
from app.database import DATABASE_URL, engine
from app.models.base import BaseModel

# Import all models to ensure they are registered with SQLAlchemy
# This is necessary for 'autogenerate' to work properly
import app.models.whatsapp  # noqa: F401
import app.models.omnichannel  # noqa: F401
import app.models.user  # noqa: F401
import app.models.flow_models  # noqa: F401
import app.models.telegram # noqa: F401

# For type hints
from typing import List, Dict, Any, Union, Optional
from types import ModuleType

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Add your model's MetaData object here for 'autogenerate' support
target_metadata = BaseModel.metadata

# Override the SQLAlchemy URL from our configuration
config.set_main_option('sqlalchemy.url', DATABASE_URL)

# For autogenerate support
def include_object(
    object: Any,
    name: str,
    type_: str,
    reflected: bool,
    compare_to: Any
) -> bool:
    """Include or exclude objects for autogenerate.
    
    This is useful for excluding views, etc.
    """
    if type_ == "table" and name.startswith("sqlite_"):
        return False
    return True

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.
    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        include_schemas=True,
        include_object=include_object,
        render_as_batch=True  # For SQLite compatibility
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.
    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            compare_server_default=True,
            include_schemas=True,
            include_object=include_object,
            render_as_batch=True  # For SQLite compatibility
        )

        with context.begin_transaction():
            context.run_migrations()

# For direct execution
if __name__ == '__main__':
    if context.is_offline_mode():
        run_migrations_offline()
    else:
        run_migrations_online()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
