"""Telegram-related database models."""

from enum import Enum
from datetime import datetime
from typing import Optional, List, Dict, Any, TYPE_CHECKING
from sqlmodel import Field, SQLModel, Relationship, Column, JSON
from sqlalchemy import String, Text, Integer, Boolean, ForeignKey, DateTime, Table, text

from .base import BaseModel
from .omnichannel import OmnichannelMessage, MessageStatus, MessageType, MessageDirection

if TYPE_CHECKING:
    from .user import User
    from .analytics import MessageAnalytics
    from .bot import BotTest


class TelegramAccountStatus(str, Enum):
    ACTIVE = "ACTIVE"
    PENDING = "PENDING"
    SUSPENDED = "SUSPENDED"
    DELETED = "DELETED"


class TelegramAccount(BaseModel, table=True):
    """Telegram Bot Account model.
    
    Represents a connected Telegram Bot Account with its configuration and credentials.
    """
    __tablename__ = "telegram_accounts"
    __table_args__ = {'extend_existing': True}
    
    # Relationships
    user_id: int = Field(foreign_key="users.id", index=True)
    user: "User" = Relationship(back_populates="telegram_accounts")
    
    # Account details
    account_name: str = Field(
        max_length=255, 
        nullable=False, 
        description="User-friendly name for the Telegram bot account"
    )
    bot_token: str = Field(
        nullable=False,
        description="Telegram bot token"
    )
    username: Optional[str] = Field(
        default=None,
        max_length=255,
        description="Telegram bot username"
    )
    
    # Account status
    status: TelegramAccountStatus = Field(
        default=TelegramAccountStatus.PENDING,
        description="Current status of the Telegram account"
    )
    
    # Metadata field
    metadata_: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON, nullable=True, default=dict, server_default=text("'{}'"))
    )
    
    is_verified: bool = Field(
        default=False,
        nullable=False,
        description="Whether the Telegram bot token has been verified"
    )
    verified_at: Optional[datetime] = Field(
        default=None,
        description="When the Telegram bot token was verified"
    )
    
    # Relationships (similar to WhatsAppAccount, adapt as needed for Telegram specifics)
    # channels: List["TelegramChannel"] = Relationship(back_populates="account") # If you have Telegram-specific channels
    # templates: List["MessageTemplate"] = Relationship(back_populates="account") # If Telegram has similar template concepts
    # flows: List["TelegramFlow"] = Relationship(back_populates="account") # If Telegram has similar flow concepts
    # campaigns: List["Campaign"] = Relationship(back_populates="account") # Campaigns can be cross-channel
    # contacts: List["Contact"] = Relationship(back_populates="account") # Contacts can be cross-channel
    # contact_groups: List["ContactGroup"] = Relationship(back_populates="account") # Contact groups can be cross-channel
    # message_analytics: List["MessageAnalytics"] = Relationship(back_populates="account") # Analytics can be cross-channel
    
    def __repr__(self) -> str:
        return f"<TelegramAccount {self.account_name} ({self.username or 'No Username'})>"
    
    @property
    def is_active(self) -> bool:
        """Check if the account is active."""
        return self.status == TelegramAccountStatus.ACTIVE and self.is_verified

# Rebuild models to resolve forward references
if hasattr(TelegramAccount, 'model_rebuild'):
    TelegramAccount.model_rebuild()
