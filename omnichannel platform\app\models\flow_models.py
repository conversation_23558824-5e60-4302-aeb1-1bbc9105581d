"""SQLModel models for bot flow functionality.

This module defines the database models for the bot flow builder, including:
- Flow: Represents a complete bot flow
- FlowNode: Represents a node in a flow (start, message, condition, end, etc.)
- FlowEdge: Represents a connection between nodes in a flow
"""
from typing import Optional, List, Dict, Any, TypeVar, Generic, Type, Union
from datetime import datetime
from uuid import uuid4, UUID

from sqlmodel import SQLModel, Field, Relationship, create_engine, Session, select, JSON, Column
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from pydantic import BaseModel as PydanticBaseModel, Field as PydanticField

# Import the base model to ensure proper metadata registration
from app.models.base import BaseModel

# Type variables for generic model handling
T = TypeVar('T', bound='BaseModel')

# Base model with common fields for flow models
class FlowBaseModel(BaseModel):
    """Base model with common fields for all flow database models.
    
    This inherits from the main BaseModel to ensure proper metadata registration.
    """
    __abstract__ = True  # This ensures this class doesn't create its own table
    
    # Override the ID to use UUID instead of integer
    id: Optional[str] = Field(
        default_factory=lambda: str(uuid4()),
        primary_key=True,
        index=True,
        description="Unique identifier"
    )

# Flow Base Model
class FlowBase(SQLModel):
    """Base model for bot flows."""
    name: str = Field(..., max_length=255, description="Name of the flow")
    description: Optional[str] = Field(None, description="Description of the flow")
    is_active: bool = Field(default=True, description="Whether the flow is active")
    version: str = Field(default="1.0.0", description="Version of the flow")
    flow_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Additional metadata for the flow"
    )

# Flow Node Base Model
class FlowNodeBase(SQLModel):
    """Base model for flow nodes."""
    node_id: str = Field(
        ...,
        index=True,
        description="Unique identifier for the node within the flow"
    )
    type: str = Field(description="Type of the node (e.g., 'start', 'message', 'condition', 'end')")
    position_x: float = Field(description="X position of the node in the flow editor")
    position_y: float = Field(description="Y position of the node in the flow editor")
    
    # Node data and styling
    data: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Node data including message content, conditions, etc."
    )
    style: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Styling information for the node"
    )
    
    # Metadata
    z_index: Optional[int] = Field(default=0, description="Z-index for rendering order")
    selected: Optional[bool] = Field(default=False, description="Whether the node is selected")
    dragging: Optional[bool] = Field(default=False, description="Whether the node is being dragged")
    
    # Status flags
    is_valid: Optional[bool] = Field(default=True, description="Whether the node configuration is valid")
    validation_errors: Optional[List[str]] = Field(
        default_factory=list,
        sa_column=Column(JSON),
        description="List of validation errors if the node is invalid"
    )
    
    # Position and dimensions for rendering
    width: Optional[float] = Field(default=200, description="Width of the node in pixels")
    height: Optional[float] = Field(default=50, description="Height of the node in pixels")
    
    # Node handles configuration
    source_handles: Optional[Dict[str, Dict[str, Any]]] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Configuration for output handles"
    )
    target_handles: Optional[Dict[str, Dict[str, Any]]] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Configuration for input handles"
    )
    
    # Node metadata
    node_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Additional metadata for the node"
    )

# Flow Edge Base Model
class FlowEdgeBase(SQLModel):
    """Base model for flow edges."""
    # Edge data
    source_handle: Optional[str] = Field(
        default=None,
        description="The handle ID on the source node where this edge starts"
    )
    target_handle: Optional[str] = Field(
        default=None,
        description="The handle ID on the target node where this edge ends"
    )
    
    # Edge styling and metadata
    animated: Optional[bool] = Field(
        default=False,
        description="Whether the edge is animated"
    )
    
    data: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Additional data for the edge"
    )
    
    style: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Styling information for the edge"
    )
    
    # Metadata
    z_index: Optional[int] = Field(
        default=0,
        description="Z-index for rendering order"
    )
    
    selected: Optional[bool] = Field(
        default=False,
        description="Whether the edge is selected"
    )
    
    # Edge type and behavior
    edge_type: Optional[str] = Field(
        default="default",
        description="Type of the edge (e.g., 'default', 'conditional', 'fallback')"
    )
    
    # Validation and status
    is_valid: Optional[bool] = Field(
        default=True,
        description="Whether the edge configuration is valid"
    )
    
    validation_errors: Optional[List[str]] = Field(
        default_factory=list,
        sa_column=Column(JSON),
        description="List of validation errors if the edge is invalid"
    )
    
    # Edge routing
    waypoints: Optional[List[Dict[str, float]]] = Field(
        default_factory=list,
        sa_column=Column(JSON),
        description="List of waypoints for edge routing"
    )
    
    # Edge metadata
    edge_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Additional metadata for the edge"
    )

# Flow Model
class Flow(FlowBase, FlowBaseModel, table=True):
    """Bot flow configuration."""
    __tablename__ = "bot_flows"
    __table_args__ = {'extend_existing': True}  # Allow table extension if it exists
    
    # Relationships
    nodes: List["FlowNode"] = Relationship(
        back_populates="flow",
        sa_relationship=relationship(
            "FlowNode",
            back_populates="flow",
            cascade="all, delete-orphan",
            passive_deletes=True
        )
    )
    
    edges: List["FlowEdge"] = Relationship(
        back_populates="flow",
        sa_relationship=relationship(
            "FlowEdge",
            back_populates="flow",
            cascade="all, delete-orphan",
            passive_deletes=True
        )
    )
    
    # Helper methods
    def add_node(self, node: "FlowNode", session: Session) -> "FlowNode":
        """Add a node to this flow."""
        node.flow_id = self.id
        session.add(node)
        session.commit()
        session.refresh(node)
        return node
    
    def add_edge(self, edge: "FlowEdge", session: Session) -> "FlowEdge":
        """Add an edge to this flow."""
        edge.flow_id = self.id
        session.add(edge)
        session.commit()
        session.refresh(edge)
        return edge

# Flow Node Model
class FlowNode(FlowNodeBase, FlowBaseModel, table=True):
    """A node in a bot flow."""
    __tablename__ = "flow_nodes"
    __table_args__ = {'extend_existing': True}  # Allow table extension if it exists
    
    # Foreign key to flow
    flow_id: str = Field(
        ...,
        foreign_key="bot_flows.id",
        index=True,
        description="ID of the flow this node belongs to"
    )
    
    # Relationships
    flow: "Flow" = Relationship(back_populates="nodes")
    
    # Relationships for edges where this node is the source or target
    source_edges: List["FlowEdge"] = Relationship(
        back_populates="source_node",
        sa_relationship=relationship(
            "FlowEdge",
            foreign_keys="[FlowEdge.source_node_id]",
            primaryjoin="FlowNode.id == FlowEdge.source_node_id",
            viewonly=True
        )
    )
    
    target_edges: List["FlowEdge"] = Relationship(
        back_populates="target_node",
        sa_relationship=relationship(
            "FlowEdge",
            foreign_keys="[FlowEdge.target_node_id]",
            primaryjoin="FlowNode.id == FlowEdge.target_node_id",
            viewonly=True
        )
    )
    
    # Helper methods
    def add_source_edge(self, edge: "FlowEdge", session: Session) -> "FlowEdge":
        """Add an outgoing edge from this node."""
        edge.source_node_id = self.id
        session.add(edge)
        session.commit()
        session.refresh(edge)
        return edge
    
    def add_target_edge(self, edge: "FlowEdge", session: Session) -> "FlowEdge":
        """Add an incoming edge to this node."""
        edge.target_node_id = self.id
        session.add(edge)
        session.commit()
        session.refresh(edge)
        return edge

# Flow Edge Model
class FlowEdge(FlowEdgeBase, FlowBaseModel, table=True):
    """Edge between nodes in a bot flow."""
    __tablename__ = "flow_edges"
    __table_args__ = {'extend_existing': True}  # Allow table extension if it exists
    
    # Foreign key to flow
    flow_id: str = Field(
        ...,
        foreign_key="bot_flows.id",
        index=True,
        description="ID of the flow this edge belongs to"
    )
    
    # Foreign keys to source and target nodes
    source_node_id: str = Field(
        ...,
        foreign_key="flow_nodes.id",
        index=True,
        description="ID of the source node"
    )
    
    target_node_id: str = Field(
        ...,
        foreign_key="flow_nodes.id",
        index=True,
        description="ID of the target node"
    )
    
    # Relationships
    flow: "Flow" = Relationship(back_populates="edges")
    
    source_node: "FlowNode" = Relationship(
        back_populates="source_edges",
        sa_relationship=relationship(
            "FlowNode",
            foreign_keys="[FlowEdge.source_node_id]",
            primaryjoin="FlowNode.id == FlowEdge.source_node_id",
            viewonly=True
        )
    )
    
    target_node: "FlowNode" = Relationship(
        back_populates="target_edges",
        sa_relationship=relationship(
            "FlowNode",
            foreign_keys="[FlowEdge.target_node_id]",
            primaryjoin="FlowNode.id == FlowEdge.target_node_id",
            viewonly=True
        )
    )

# Pydantic models for API requests/responses
class FlowCreate(FlowBase):
    """Pydantic model for creating a new flow."""
    pass

class FlowRead(FlowBase):
    """Pydantic model for reading flow data."""
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class FlowUpdate(SQLModel):
    """Pydantic model for updating a flow."""
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    version: Optional[str] = None
    flow_metadata: Optional[Dict[str, Any]] = None

class FlowNodeCreate(FlowNodeBase):
    """Pydantic model for creating a new flow node."""
    flow_id: str

class FlowNodeRead(FlowNodeBase):
    """Pydantic model for reading flow node data."""
    id: str
    flow_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class FlowNodeUpdate(SQLModel):
    """Pydantic model for updating a flow node."""
    node_id: Optional[str] = None
    type: Optional[str] = None
    position_x: Optional[float] = None
    position_y: Optional[float] = None
    data: Optional[Dict[str, Any]] = None
    style: Optional[Dict[str, Any]] = None
    z_index: Optional[int] = None
    selected: Optional[bool] = None
    dragging: Optional[bool] = None
    is_valid: Optional[bool] = None
    validation_errors: Optional[List[str]] = None
    width: Optional[float] = None
    height: Optional[float] = None
    source_handles: Optional[Dict[str, Dict[str, Any]]] = None
    target_handles: Optional[Dict[str, Dict[str, Any]]] = None
    node_metadata: Optional[Dict[str, Any]] = None

class FlowEdgeCreate(FlowEdgeBase):
    """Pydantic model for creating a new flow edge."""
    flow_id: str
    source_node_id: str
    target_node_id: str

class FlowEdgeRead(FlowEdgeBase):
    """Pydantic model for reading flow edge data."""
    id: str
    flow_id: str
    source_node_id: str
    target_node_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class FlowEdgeUpdate(SQLModel):
    """Pydantic model for updating a flow edge."""
    source_handle: Optional[str] = None
    target_handle: Optional[str] = None
    animated: Optional[bool] = None
    data: Optional[Dict[str, Any]] = None
    style: Optional[Dict[str, Any]] = None
    z_index: Optional[int] = None
    selected: Optional[bool] = None
    edge_type: Optional[str] = None
    is_valid: Optional[bool] = None
    validation_errors: Optional[List[str]] = None
    waypoints: Optional[List[Dict[str, float]]] = None
    edge_metadata: Optional[Dict[str, Any]] = None

# Update forward refs for proper type hints

