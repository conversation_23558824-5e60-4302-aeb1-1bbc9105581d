<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Call Protocol" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/call.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="CPython supports two different calling protocols: tp_call and vectorcall. The tp_call Protocol: Instances of classes that set tp_call are callable. The signature of the slot is: A call is made usin..." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="CPython supports two different calling protocols: tp_call and vectorcall. The tp_call Protocol: Instances of classes that set tp_call are callable. The signature of the slot is: A call is made usin..." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Call Protocol &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Number Protocol" href="number.html" />
    <link rel="prev" title="Object Protocol" href="object.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/call.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Call Protocol</a><ul>
<li><a class="reference internal" href="#the-tp-call-protocol">The <em>tp_call</em> Protocol</a></li>
<li><a class="reference internal" href="#the-vectorcall-protocol">The Vectorcall Protocol</a><ul>
<li><a class="reference internal" href="#recursion-control">Recursion Control</a></li>
<li><a class="reference internal" href="#vectorcall-support-api">Vectorcall Support API</a></li>
</ul>
</li>
<li><a class="reference internal" href="#object-calling-api">Object Calling API</a></li>
<li><a class="reference internal" href="#call-support-api">Call Support API</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="object.html"
                          title="previous chapter">Object Protocol</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="number.html"
                          title="next chapter">Number Protocol</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/call.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="number.html" title="Number Protocol"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="object.html" title="Object Protocol"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="abstract.html" accesskey="U">Abstract Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Call Protocol</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="call-protocol">
<span id="call"></span><h1>Call Protocol<a class="headerlink" href="#call-protocol" title="Link to this heading">¶</a></h1>
<p>CPython supports two different calling protocols:
<em>tp_call</em> and vectorcall.</p>
<section id="the-tp-call-protocol">
<h2>The <em>tp_call</em> Protocol<a class="headerlink" href="#the-tp-call-protocol" title="Link to this heading">¶</a></h2>
<p>Instances of classes that set <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a> are callable.
The signature of the slot is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">tp_call</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">callable</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">,</span><span class="w"> </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">kwargs</span><span class="p">);</span>
</pre></div>
</div>
<p>A call is made using a tuple for the positional arguments
and a dict for the keyword arguments, similarly to
<code class="docutils literal notranslate"><span class="pre">callable(*args,</span> <span class="pre">**kwargs)</span></code> in Python code.
<em>args</em> must be non-NULL (use an empty tuple if there are no arguments)
but <em>kwargs</em> may be <em>NULL</em> if there are no keyword arguments.</p>
<p>This convention is not only used by <em>tp_call</em>:
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_new" title="PyTypeObject.tp_new"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_new</span></code></a> and <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_init" title="PyTypeObject.tp_init"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_init</span></code></a>
also pass arguments this way.</p>
<p>To call an object, use <a class="reference internal" href="#c.PyObject_Call" title="PyObject_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Call()</span></code></a> or another
<a class="reference internal" href="#capi-call"><span class="std std-ref">call API</span></a>.</p>
</section>
<section id="the-vectorcall-protocol">
<span id="vectorcall"></span><h2>The Vectorcall Protocol<a class="headerlink" href="#the-vectorcall-protocol" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
<p>The vectorcall protocol was introduced in <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0590/"><strong>PEP 590</strong></a> as an additional protocol
for making calls more efficient.</p>
<p>As rule of thumb, CPython will prefer the vectorcall for internal calls
if the callable supports it. However, this is not a hard rule.
Additionally, some third-party extensions use <em>tp_call</em> directly
(rather than using <a class="reference internal" href="#c.PyObject_Call" title="PyObject_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Call()</span></code></a>).
Therefore, a class supporting vectorcall must also implement
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a>.
Moreover, the callable must behave the same
regardless of which protocol is used.
The recommended way to achieve this is by setting
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a> to <a class="reference internal" href="#c.PyVectorcall_Call" title="PyVectorcall_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyVectorcall_Call()</span></code></a>.
This bears repeating:</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>A class supporting vectorcall <strong>must</strong> also implement
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a> with the same semantics.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_HAVE_VECTORCALL" title="Py_TPFLAGS_HAVE_VECTORCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></code></a> flag is now removed from a class
when the class’s <a class="reference internal" href="../reference/datamodel.html#object.__call__" title="object.__call__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__call__()</span></code></a> method is reassigned.
(This internally sets <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a> only, and thus
may make it behave differently than the vectorcall function.)
In earlier Python versions, vectorcall should only be used with
<a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_IMMUTABLETYPE" title="Py_TPFLAGS_IMMUTABLETYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">immutable</span></code></a> or static types.</p>
</div>
<p>A class should not implement vectorcall if that would be slower
than <em>tp_call</em>. For example, if the callee needs to convert
the arguments to an args tuple and kwargs dict anyway, then there is no point
in implementing vectorcall.</p>
<p>Classes can implement the vectorcall protocol by enabling the
<a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_HAVE_VECTORCALL" title="Py_TPFLAGS_HAVE_VECTORCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></code></a> flag and setting
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_vectorcall_offset" title="PyTypeObject.tp_vectorcall_offset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_vectorcall_offset</span></code></a> to the offset inside the
object structure where a <em>vectorcallfunc</em> appears.
This is a pointer to a function with the following signature:</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.vectorcallfunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">vectorcallfunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="n"><span class="pre">size_t</span></span><span class="w"> </span><span class="n"><span class="pre">nargsf</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">kwnames</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.vectorcallfunc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em></dd></dl>

<ul class="simple">
<li><p><em>callable</em> is the object being called.</p></li>
<li><dl class="simple">
<dt><em>args</em> is a C array consisting of the positional arguments followed by the</dt><dd><p>values of the keyword arguments.
This can be <em>NULL</em> if there are no arguments.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><em>nargsf</em> is the number of positional arguments plus possibly the</dt><dd><p><a class="reference internal" href="#c.PY_VECTORCALL_ARGUMENTS_OFFSET" title="PY_VECTORCALL_ARGUMENTS_OFFSET"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_VECTORCALL_ARGUMENTS_OFFSET</span></code></a> flag.
To get the actual number of positional arguments from <em>nargsf</em>,
use <a class="reference internal" href="#c.PyVectorcall_NARGS" title="PyVectorcall_NARGS"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyVectorcall_NARGS()</span></code></a>.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><em>kwnames</em> is a tuple containing the names of the keyword arguments;</dt><dd><p>in other words, the keys of the kwargs dict.
These names must be strings (instances of <code class="docutils literal notranslate"><span class="pre">str</span></code> or a subclass)
and they must be unique.
If there are no keyword arguments, then <em>kwnames</em> can instead be <em>NULL</em>.</p>
</dd>
</dl>
</li>
</ul>
<dl class="c macro">
<dt class="sig sig-object c" id="c.PY_VECTORCALL_ARGUMENTS_OFFSET">
<span class="sig-name descname"><span class="n"><span class="pre">PY_VECTORCALL_ARGUMENTS_OFFSET</span></span></span><a class="headerlink" href="#c.PY_VECTORCALL_ARGUMENTS_OFFSET" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em><p>If this flag is set in a vectorcall <em>nargsf</em> argument, the callee is allowed
to temporarily change <code class="docutils literal notranslate"><span class="pre">args[-1]</span></code>. In other words, <em>args</em> points to
argument 1 (not 0) in the allocated vector.
The callee must restore the value of <code class="docutils literal notranslate"><span class="pre">args[-1]</span></code> before returning.</p>
<p>For <a class="reference internal" href="#c.PyObject_VectorcallMethod" title="PyObject_VectorcallMethod"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_VectorcallMethod()</span></code></a>, this flag means instead that
<code class="docutils literal notranslate"><span class="pre">args[0]</span></code> may be changed.</p>
<p>Whenever they can do so cheaply (without additional allocation), callers
are encouraged to use <a class="reference internal" href="#c.PY_VECTORCALL_ARGUMENTS_OFFSET" title="PY_VECTORCALL_ARGUMENTS_OFFSET"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_VECTORCALL_ARGUMENTS_OFFSET</span></code></a>.
Doing so will allow callables such as bound methods to make their onward
calls (which include a prepended <em>self</em> argument) very efficiently.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.8.</span></p>
</div>
</dd></dl>

<p>To call an object that implements vectorcall, use a <a class="reference internal" href="#capi-call"><span class="std std-ref">call API</span></a>
function as with any other callable.
<a class="reference internal" href="#c.PyObject_Vectorcall" title="PyObject_Vectorcall"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Vectorcall()</span></code></a> will usually be most efficient.</p>
<section id="recursion-control">
<h3>Recursion Control<a class="headerlink" href="#recursion-control" title="Link to this heading">¶</a></h3>
<p>When using <em>tp_call</em>, callees do not need to worry about
<a class="reference internal" href="exceptions.html#recursion"><span class="std std-ref">recursion</span></a>: CPython uses
<a class="reference internal" href="exceptions.html#c.Py_EnterRecursiveCall" title="Py_EnterRecursiveCall"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_EnterRecursiveCall()</span></code></a> and <a class="reference internal" href="exceptions.html#c.Py_LeaveRecursiveCall" title="Py_LeaveRecursiveCall"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_LeaveRecursiveCall()</span></code></a>
for calls made using <em>tp_call</em>.</p>
<p>For efficiency, this is not the case for calls done using vectorcall:
the callee should use <em>Py_EnterRecursiveCall</em> and <em>Py_LeaveRecursiveCall</em>
if needed.</p>
</section>
<section id="vectorcall-support-api">
<h3>Vectorcall Support API<a class="headerlink" href="#vectorcall-support-api" title="Link to this heading">¶</a></h3>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyVectorcall_NARGS">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyVectorcall_NARGS</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">size_t</span></span><span class="w"> </span><span class="n"><span class="pre">nargsf</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyVectorcall_NARGS" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em><p>Given a vectorcall <em>nargsf</em> argument, return the actual number of
arguments.
Currently equivalent to:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">Py_ssize_t</span><span class="p">)(</span><span class="n">nargsf</span><span class="w"> </span><span class="o">&amp;</span><span class="w"> </span><span class="o">~</span><span class="n">PY_VECTORCALL_ARGUMENTS_OFFSET</span><span class="p">)</span>
</pre></div>
</div>
<p>However, the function <code class="docutils literal notranslate"><span class="pre">PyVectorcall_NARGS</span></code> should be used to allow
for future extensions.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyVectorcall_Function">
<a class="reference internal" href="#c.vectorcallfunc" title="vectorcallfunc"><span class="n"><span class="pre">vectorcallfunc</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyVectorcall_Function</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyVectorcall_Function" title="Link to this definition">¶</a><br /></dt>
<dd><p>If <em>op</em> does not support the vectorcall protocol (either because the type
does not or because the specific instance does not), return <em>NULL</em>.
Otherwise, return the vectorcall function pointer stored in <em>op</em>.
This function never raises an exception.</p>
<p>This is mostly useful to check whether or not <em>op</em> supports vectorcall,
which can be done by checking <code class="docutils literal notranslate"><span class="pre">PyVectorcall_Function(op)</span> <span class="pre">!=</span> <span class="pre">NULL</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyVectorcall_Call">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyVectorcall_Call</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tuple</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">dict</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyVectorcall_Call" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em><p>Call <em>callable</em>’s <a class="reference internal" href="#c.vectorcallfunc" title="vectorcallfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">vectorcallfunc</span></code></a> with positional and keyword
arguments given in a tuple and dict, respectively.</p>
<p>This is a specialized function, intended to be put in the
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_call" title="PyTypeObject.tp_call"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_call</span></code></a> slot or be used in an implementation of <code class="docutils literal notranslate"><span class="pre">tp_call</span></code>.
It does not check the <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_HAVE_VECTORCALL" title="Py_TPFLAGS_HAVE_VECTORCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_HAVE_VECTORCALL</span></code></a> flag
and it does not fall back to <code class="docutils literal notranslate"><span class="pre">tp_call</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.8.</span></p>
</div>
</dd></dl>

</section>
</section>
<section id="object-calling-api">
<span id="capi-call"></span><h2>Object Calling API<a class="headerlink" href="#object-calling-api" title="Link to this heading">¶</a></h2>
<p>Various functions are available for calling a Python object.
Each converts its arguments to a convention supported by the called object –
either <em>tp_call</em> or vectorcall.
In order to do as little conversion as possible, pick one that best fits
the format of data you have available.</p>
<p>The following table summarizes the available functions;
please see individual documentation for details.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Function</p></th>
<th class="head"><p>callable</p></th>
<th class="head"><p>args</p></th>
<th class="head"><p>kwargs</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyObject_Call" title="PyObject_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Call()</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PyObject</span> <span class="pre">*</span></code></p></td>
<td><p>tuple</p></td>
<td><p>dict/<code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyObject_CallNoArgs" title="PyObject_CallNoArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallNoArgs()</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PyObject</span> <span class="pre">*</span></code></p></td>
<td><p>—</p></td>
<td><p>—</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyObject_CallOneArg" title="PyObject_CallOneArg"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallOneArg()</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PyObject</span> <span class="pre">*</span></code></p></td>
<td><p>1 object</p></td>
<td><p>—</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyObject_CallObject" title="PyObject_CallObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallObject()</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PyObject</span> <span class="pre">*</span></code></p></td>
<td><p>tuple/<code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p>—</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyObject_CallFunction" title="PyObject_CallFunction"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallFunction()</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PyObject</span> <span class="pre">*</span></code></p></td>
<td><p>format</p></td>
<td><p>—</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyObject_CallMethod" title="PyObject_CallMethod"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallMethod()</span></code></a></p></td>
<td><p>obj + <code class="docutils literal notranslate"><span class="pre">char*</span></code></p></td>
<td><p>format</p></td>
<td><p>—</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyObject_CallFunctionObjArgs" title="PyObject_CallFunctionObjArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallFunctionObjArgs()</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PyObject</span> <span class="pre">*</span></code></p></td>
<td><p>variadic</p></td>
<td><p>—</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyObject_CallMethodObjArgs" title="PyObject_CallMethodObjArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallMethodObjArgs()</span></code></a></p></td>
<td><p>obj + name</p></td>
<td><p>variadic</p></td>
<td><p>—</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyObject_CallMethodNoArgs" title="PyObject_CallMethodNoArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallMethodNoArgs()</span></code></a></p></td>
<td><p>obj + name</p></td>
<td><p>—</p></td>
<td><p>—</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyObject_CallMethodOneArg" title="PyObject_CallMethodOneArg"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallMethodOneArg()</span></code></a></p></td>
<td><p>obj + name</p></td>
<td><p>1 object</p></td>
<td><p>—</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyObject_Vectorcall" title="PyObject_Vectorcall"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Vectorcall()</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PyObject</span> <span class="pre">*</span></code></p></td>
<td><p>vectorcall</p></td>
<td><p>vectorcall</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyObject_VectorcallDict" title="PyObject_VectorcallDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_VectorcallDict()</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PyObject</span> <span class="pre">*</span></code></p></td>
<td><p>vectorcall</p></td>
<td><p>dict/<code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyObject_VectorcallMethod" title="PyObject_VectorcallMethod"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_VectorcallMethod()</span></code></a></p></td>
<td><p>arg + name</p></td>
<td><p>vectorcall</p></td>
<td><p>vectorcall</p></td>
</tr>
</tbody>
</table>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_Call">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_Call</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">kwargs</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_Call" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Call a callable Python object <em>callable</em>, with arguments given by the
tuple <em>args</em>, and named arguments given by the dictionary <em>kwargs</em>.</p>
<p><em>args</em> must not be <em>NULL</em>; use an empty tuple if no arguments are needed.
If no named arguments are needed, <em>kwargs</em> can be <em>NULL</em>.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<p>This is the equivalent of the Python expression:
<code class="docutils literal notranslate"><span class="pre">callable(*args,</span> <span class="pre">**kwargs)</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallNoArgs">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallNoArgs</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallNoArgs" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Call a callable Python object <em>callable</em> without any arguments. It is the
most efficient way to call a callable Python object without any argument.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallOneArg">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallOneArg</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">arg</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallOneArg" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Call a callable Python object <em>callable</em> with exactly 1 positional argument
<em>arg</em> and no keyword arguments.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallObject">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallObject</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Call a callable Python object <em>callable</em>, with arguments given by the
tuple <em>args</em>.  If no arguments are needed, then <em>args</em> can be <em>NULL</em>.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<p>This is the equivalent of the Python expression: <code class="docutils literal notranslate"><span class="pre">callable(*args)</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallFunction">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallFunction</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">format</span></span>, <span class="p"><span class="pre">...</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallFunction" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Call a callable Python object <em>callable</em>, with a variable number of C arguments.
The C arguments are described using a <a class="reference internal" href="arg.html#c.Py_BuildValue" title="Py_BuildValue"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_BuildValue()</span></code></a> style format
string.  The format can be <em>NULL</em>, indicating that no arguments are provided.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<p>This is the equivalent of the Python expression: <code class="docutils literal notranslate"><span class="pre">callable(*args)</span></code>.</p>
<p>Note that if you only pass <span class="c-expr sig sig-inline c"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> args,
<a class="reference internal" href="#c.PyObject_CallFunctionObjArgs" title="PyObject_CallFunctionObjArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallFunctionObjArgs()</span></code></a> is a faster alternative.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The type of <em>format</em> was changed from <code class="docutils literal notranslate"><span class="pre">char</span> <span class="pre">*</span></code>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallMethod">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallMethod</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">format</span></span>, <span class="p"><span class="pre">...</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallMethod" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Call the method named <em>name</em> of object <em>obj</em> with a variable number of C
arguments.  The C arguments are described by a <a class="reference internal" href="arg.html#c.Py_BuildValue" title="Py_BuildValue"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_BuildValue()</span></code></a> format
string that should produce a tuple.</p>
<p>The format can be <em>NULL</em>, indicating that no arguments are provided.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<p>This is the equivalent of the Python expression:
<code class="docutils literal notranslate"><span class="pre">obj.name(arg1,</span> <span class="pre">arg2,</span> <span class="pre">...)</span></code>.</p>
<p>Note that if you only pass <span class="c-expr sig sig-inline c"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> args,
<a class="reference internal" href="#c.PyObject_CallMethodObjArgs" title="PyObject_CallMethodObjArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallMethodObjArgs()</span></code></a> is a faster alternative.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The types of <em>name</em> and <em>format</em> were changed from <code class="docutils literal notranslate"><span class="pre">char</span> <span class="pre">*</span></code>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallFunctionObjArgs">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallFunctionObjArgs</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <span class="p"><span class="pre">...</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallFunctionObjArgs" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Call a callable Python object <em>callable</em>, with a variable number of
<span class="c-expr sig sig-inline c"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> arguments.  The arguments are provided as a variable number
of parameters followed by <em>NULL</em>.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<p>This is the equivalent of the Python expression:
<code class="docutils literal notranslate"><span class="pre">callable(arg1,</span> <span class="pre">arg2,</span> <span class="pre">...)</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallMethodObjArgs">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallMethodObjArgs</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <span class="p"><span class="pre">...</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallMethodObjArgs" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Call a method of the Python object <em>obj</em>, where the name of the method is given as a
Python string object in <em>name</em>.  It is called with a variable number of
<span class="c-expr sig sig-inline c"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> arguments.  The arguments are provided as a variable number
of parameters followed by <em>NULL</em>.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallMethodNoArgs">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallMethodNoArgs</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallMethodNoArgs" title="Link to this definition">¶</a><br /></dt>
<dd><p>Call a method of the Python object <em>obj</em> without arguments,
where the name of the method is given as a Python string object in <em>name</em>.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CallMethodOneArg">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CallMethodOneArg</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">arg</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CallMethodOneArg" title="Link to this definition">¶</a><br /></dt>
<dd><p>Call a method of the Python object <em>obj</em> with a single positional argument
<em>arg</em>, where the name of the method is given as a Python string object in
<em>name</em>.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_Vectorcall">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_Vectorcall</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span>, <span class="n"><span class="pre">size_t</span></span><span class="w"> </span><span class="n"><span class="pre">nargsf</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">kwnames</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_Vectorcall" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em><p>Call a callable Python object <em>callable</em>.
The arguments are the same as for <a class="reference internal" href="#c.vectorcallfunc" title="vectorcallfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">vectorcallfunc</span></code></a>.
If <em>callable</em> supports <a class="reference internal" href="#vectorcall">vectorcall</a>, this directly calls
the vectorcall function stored in <em>callable</em>.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_VectorcallDict">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_VectorcallDict</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">callable</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span>, <span class="n"><span class="pre">size_t</span></span><span class="w"> </span><span class="n"><span class="pre">nargsf</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">kwdict</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_VectorcallDict" title="Link to this definition">¶</a><br /></dt>
<dd><p>Call <em>callable</em> with positional arguments passed exactly as in the <a class="reference internal" href="#vectorcall">vectorcall</a> protocol,
but with keyword arguments passed as a dictionary <em>kwdict</em>.
The <em>args</em> array contains only the positional arguments.</p>
<p>Regardless of which protocol is used internally,
a conversion of arguments needs to be done.
Therefore, this function should only be used if the caller
already has a dictionary ready to use for the keyword arguments,
but not a tuple for the positional arguments.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_VectorcallMethod">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_VectorcallMethod</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span>, <span class="n"><span class="pre">size_t</span></span><span class="w"> </span><span class="n"><span class="pre">nargsf</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">kwnames</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_VectorcallMethod" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.12.</em><p>Call a method using the vectorcall calling convention. The name of the method
is given as a Python string <em>name</em>. The object whose method is called is
<em>args[0]</em>, and the <em>args</em> array starting at <em>args[1]</em> represents the arguments
of the call. There must be at least one positional argument.
<em>nargsf</em> is the number of positional arguments including <em>args[0]</em>,
plus <a class="reference internal" href="#c.PY_VECTORCALL_ARGUMENTS_OFFSET" title="PY_VECTORCALL_ARGUMENTS_OFFSET"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_VECTORCALL_ARGUMENTS_OFFSET</span></code></a> if the value of <code class="docutils literal notranslate"><span class="pre">args[0]</span></code> may
temporarily be changed. Keyword arguments can be passed just like in
<a class="reference internal" href="#c.PyObject_Vectorcall" title="PyObject_Vectorcall"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Vectorcall()</span></code></a>.</p>
<p>If the object has the <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_METHOD_DESCRIPTOR" title="Py_TPFLAGS_METHOD_DESCRIPTOR"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_METHOD_DESCRIPTOR</span></code></a> feature,
this will call the unbound method object with the full
<em>args</em> vector as arguments.</p>
<p>Return the result of the call on success, or raise an exception and return
<em>NULL</em> on failure.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

</section>
<section id="call-support-api">
<h2>Call Support API<a class="headerlink" href="#call-support-api" title="Link to this heading">¶</a></h2>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyCallable_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyCallable_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyCallable_Check" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Determine if the object <em>o</em> is callable.  Return <code class="docutils literal notranslate"><span class="pre">1</span></code> if the object is callable
and <code class="docutils literal notranslate"><span class="pre">0</span></code> otherwise.  This function always succeeds.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Call Protocol</a><ul>
<li><a class="reference internal" href="#the-tp-call-protocol">The <em>tp_call</em> Protocol</a></li>
<li><a class="reference internal" href="#the-vectorcall-protocol">The Vectorcall Protocol</a><ul>
<li><a class="reference internal" href="#recursion-control">Recursion Control</a></li>
<li><a class="reference internal" href="#vectorcall-support-api">Vectorcall Support API</a></li>
</ul>
</li>
<li><a class="reference internal" href="#object-calling-api">Object Calling API</a></li>
<li><a class="reference internal" href="#call-support-api">Call Support API</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="object.html"
                          title="previous chapter">Object Protocol</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="number.html"
                          title="next chapter">Number Protocol</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/call.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="number.html" title="Number Protocol"
             >next</a> |</li>
        <li class="right" >
          <a href="object.html" title="Object Protocol"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="abstract.html" >Abstract Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Call Protocol</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>