"""API endpoints for conversations and messages."""
from typing import List, Optional
from uuid import UUID
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.conversation import (
    Conversation, 
    ConversationCreate, 
    ConversationRead, 
    ConversationUpdate,
    ConversationMessage as Message,
    ConversationMessageCreate as MessageCreate,
    ConversationMessageRead as MessageRead,
    ConversationStatus,
    MessageStatus
)
from app.repositories.conversation_repository import ConversationRepository
from app.api.dependencies import get_current_user

router = APIRouter()

@router.get("/", response_model=List[ConversationRead])
def list_conversations(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    status: Optional[ConversationStatus] = None,
    channel: Optional[str] = None,
    contact_id: Optional[str] = None,
    search: Optional[str] = None,
    unread_only: bool = False,
    updated_after: Optional[datetime] = None,
    current_user = Depends(get_current_user),
):
    """
    List conversations with optional filtering.
    """
    repo = ConversationRepository(db)
    conversations = repo.get_conversations(
        skip=skip,
        limit=limit,
        status=status,
        channel=channel,
        contact_id=contact_id,
        search=search,
        unread_only=unread_only,
        updated_after=updated_after,
    )
    
    # Convert to Pydantic models for response
    return conversations

@router.post("/", response_model=ConversationRead, status_code=status.HTTP_201_CREATED)
def create_conversation(
    conversation: ConversationCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Create a new conversation.
    """
    repo = ConversationRepository(db)
    
    # Check if conversation already exists
    existing = repo.get_conversation_by_channel_and_contact(
        channel=conversation.channel,
        channel_id=conversation.channel_id,
        contact_id=conversation.contact_id
    )
    
    if existing:
        return existing
        
    db_conversation = repo.create_conversation(conversation.dict())
    return db_conversation

@router.get("/{conversation_id}", response_model=ConversationRead)
def get_conversation(
    conversation_id: UUID,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Get a conversation by ID.
    """
    repo = ConversationRepository(db)
    conversation = repo.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    return conversation

@router.patch("/{conversation_id}", response_model=ConversationRead)
def update_conversation(
    conversation_id: UUID,
    conversation_update: ConversationUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Update a conversation.
    """
    repo = ConversationRepository(db)
    db_conversation = repo.update_conversation(conversation_id, conversation_update)
    if not db_conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    return db_conversation

@router.delete("/{conversation_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_conversation(
    conversation_id: UUID,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Delete a conversation.
    """
    repo = ConversationRepository(db)
    success = repo.delete_conversation(conversation_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    return None

@router.get("/{conversation_id}/messages", response_model=List[MessageRead])
def list_messages(
    conversation_id: UUID,
    skip: int = 0,
    limit: int = 100,
    before: Optional[datetime] = None,
    after: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    List messages in a conversation.
    """
    repo = ConversationRepository(db)
    
    # Verify conversation exists
    conversation = repo.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    messages = repo.get_messages(
        conversation_id=conversation_id,
        skip=skip,
        limit=limit,
        before=before,
        after=after,
    )
    
    # Mark messages as read if they're from the other party
    if current_user:
        repo.mark_messages_as_read(
            conversation_id=conversation_id,
            user_id=current_user.id,
            message_ids=[msg.id for msg in messages if msg.sender_id != current_user.id]
        )
    
    return messages

@router.post("/{conversation_id}/messages", response_model=MessageRead, status_code=status.HTTP_201_CREATED)
def create_message(
    conversation_id: UUID,
    message: MessageCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Create a new message in a conversation.
    """
    repo = ConversationRepository(db)
    
    # Verify conversation exists
    conversation = repo.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    # Set sender info
    message_data = message.dict()
    message_data['sender_id'] = str(current_user.id)
    message_data['status'] = MessageStatus.SENT
    
    db_message = repo.add_message(conversation_id, message_data)
    if not db_message:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create message"
        )
    
    return db_message

@router.post("/{conversation_id}/read", status_code=status.HTTP_200_OK)
def mark_conversation_as_read(
    conversation_id: UUID,
    message_ids: Optional[List[UUID]] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Mark messages in a conversation as read.
    """
    repo = ConversationRepository(db)
    
    # Verify conversation exists
    conversation = repo.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    count = repo.mark_messages_as_read(
        conversation_id=conversation_id,
        user_id=str(current_user.id),
        message_ids=message_ids
    )
    
    return {"detail": f"Marked {count} messages as read"}
