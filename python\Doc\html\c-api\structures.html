<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Common Object Structures" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/structures.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="There are a large number of structures which are used in the definition of object types for Python. This section describes these structures and how they are used. Base object types and macros: All ..." />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="There are a large number of structures which are used in the definition of object types for Python. This section describes these structures and how they are used. Base object types and macros: All ..." />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Common Object Structures &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Type Object Structures" href="typeobj.html" />
    <link rel="prev" title="Allocating Objects on the Heap" href="allocation.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/structures.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Common Object Structures</a><ul>
<li><a class="reference internal" href="#base-object-types-and-macros">Base object types and macros</a></li>
<li><a class="reference internal" href="#implementing-functions-and-methods">Implementing functions and methods</a></li>
<li><a class="reference internal" href="#accessing-attributes-of-extension-types">Accessing attributes of extension types</a><ul>
<li><a class="reference internal" href="#member-flags">Member flags</a></li>
<li><a class="reference internal" href="#member-types">Member types</a></li>
<li><a class="reference internal" href="#defining-getters-and-setters">Defining Getters and Setters</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="allocation.html"
                          title="previous chapter">Allocating Objects on the Heap</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="typeobj.html"
                          title="next chapter">Type Object Structures</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/structures.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="typeobj.html" title="Type Object Structures"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="allocation.html" title="Allocating Objects on the Heap"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="objimpl.html" accesskey="U">Object Implementation Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Common Object Structures</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="common-object-structures">
<span id="common-structs"></span><h1>Common Object Structures<a class="headerlink" href="#common-object-structures" title="Link to this heading">¶</a></h1>
<p>There are a large number of structures which are used in the definition of
object types for Python.  This section describes these structures and how they
are used.</p>
<section id="base-object-types-and-macros">
<h2>Base object types and macros<a class="headerlink" href="#base-object-types-and-macros" title="Link to this heading">¶</a></h2>
<p>All Python objects ultimately share a small number of fields at the beginning
of the object’s representation in memory.  These are represented by the
<a class="reference internal" href="#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> and <a class="reference internal" href="#c.PyVarObject" title="PyVarObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyVarObject</span></code></a> types, which are defined, in turn,
by the expansions of some macros also used, whether directly or indirectly, in
the definition of all other Python objects.  Additional macros can be found
under <a class="reference internal" href="refcounting.html#countingrefs"><span class="std std-ref">reference counting</span></a>.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyObject</span></span></span><a class="headerlink" href="#c.PyObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Limited API</span></a>. (Only some members are part of the stable ABI.)</em><p>All object types are extensions of this type.  This is a type which
contains the information Python needs to treat a pointer to an object as an
object.  In a normal “release” build, it contains only the object’s
reference count and a pointer to the corresponding type object.
Nothing is actually declared to be a <a class="reference internal" href="#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a>, but every pointer
to a Python object can be cast to a <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span>.  Access to the
members must be done by using the macros <a class="reference internal" href="refcounting.html#c.Py_REFCNT" title="Py_REFCNT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_REFCNT</span></code></a> and
<a class="reference internal" href="#c.Py_TYPE" title="Py_TYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TYPE</span></code></a>.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyVarObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyVarObject</span></span></span><a class="headerlink" href="#c.PyVarObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Limited API</span></a>. (Only some members are part of the stable ABI.)</em><p>This is an extension of <a class="reference internal" href="#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> that adds the <a class="reference internal" href="typeobj.html#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a>
field.  This is only used for objects that have some notion of <em>length</em>.
This type does not often appear in the Python/C API.
Access to the members must be done by using the macros
<a class="reference internal" href="refcounting.html#c.Py_REFCNT" title="Py_REFCNT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_REFCNT</span></code></a>, <a class="reference internal" href="#c.Py_TYPE" title="Py_TYPE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TYPE</span></code></a>, and <a class="reference internal" href="#c.Py_SIZE" title="Py_SIZE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_SIZE</span></code></a>.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyObject_HEAD">
<span class="sig-name descname"><span class="n"><span class="pre">PyObject_HEAD</span></span></span><a class="headerlink" href="#c.PyObject_HEAD" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a macro used when declaring new types which represent objects
without a varying length.  The PyObject_HEAD macro expands to:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="n">ob_base</span><span class="p">;</span>
</pre></div>
</div>
<p>See documentation of <a class="reference internal" href="#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> above.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyObject_VAR_HEAD">
<span class="sig-name descname"><span class="n"><span class="pre">PyObject_VAR_HEAD</span></span></span><a class="headerlink" href="#c.PyObject_VAR_HEAD" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a macro used when declaring new types which represent objects
with a length that varies from instance to instance.
The PyObject_VAR_HEAD macro expands to:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyVarObject</span><span class="w"> </span><span class="n">ob_base</span><span class="p">;</span>
</pre></div>
</div>
<p>See documentation of <a class="reference internal" href="#c.PyVarObject" title="PyVarObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyVarObject</span></code></a> above.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyBaseObject_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBaseObject_Type</span></span></span><a class="headerlink" href="#c.PyBaseObject_Type" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>The base class of all other objects, the same as <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> in Python.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_Is">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_Is</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span>, <a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">y</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_Is" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Test if the <em>x</em> object is the <em>y</em> object, the same as <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">is</span> <span class="pre">y</span></code> in Python.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_IsNone">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_IsNone</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_IsNone" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Test if an object is the <code class="docutils literal notranslate"><span class="pre">None</span></code> singleton,
the same as <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">is</span> <span class="pre">None</span></code> in Python.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_IsTrue">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_IsTrue</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_IsTrue" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Test if an object is the <code class="docutils literal notranslate"><span class="pre">True</span></code> singleton,
the same as <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">is</span> <span class="pre">True</span></code> in Python.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_IsFalse">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_IsFalse</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_IsFalse" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Test if an object is the <code class="docutils literal notranslate"><span class="pre">False</span></code> singleton,
the same as <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">is</span> <span class="pre">False</span></code> in Python.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_TYPE">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_TYPE</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_TYPE" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_borrowed_ref">Return value: Borrowed reference.</em><p>Get the type of the Python object <em>o</em>.</p>
<p>Return a <a class="reference internal" href="../glossary.html#term-borrowed-reference"><span class="xref std std-term">borrowed reference</span></a>.</p>
<p>Use the <a class="reference internal" href="#c.Py_SET_TYPE" title="Py_SET_TYPE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SET_TYPE()</span></code></a> function to set an object type.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="#c.Py_TYPE" title="Py_TYPE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_TYPE()</span></code></a> is changed to an inline static function.
The parameter type is no longer <span class="c-expr sig sig-inline c"><span class="k">const</span><span class="w"> </span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_IS_TYPE">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_IS_TYPE</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span>, <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_IS_TYPE" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return non-zero if the object <em>o</em> type is <em>type</em>. Return zero otherwise.
Equivalent to: <code class="docutils literal notranslate"><span class="pre">Py_TYPE(o)</span> <span class="pre">==</span> <span class="pre">type</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_SET_TYPE">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_SET_TYPE</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span>, <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_SET_TYPE" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the object <em>o</em> type to <em>type</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_SIZE">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_SIZE</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyVarObject" title="PyVarObject"><span class="n"><span class="pre">PyVarObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_SIZE" title="Link to this definition">¶</a><br /></dt>
<dd><p>Get the size of the Python object <em>o</em>.</p>
<p>Use the <a class="reference internal" href="#c.Py_SET_SIZE" title="Py_SET_SIZE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SET_SIZE()</span></code></a> function to set an object size.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="#c.Py_SIZE" title="Py_SIZE"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SIZE()</span></code></a> is changed to an inline static function.
The parameter type is no longer <span class="c-expr sig sig-inline c"><span class="k">const</span><span class="w"> </span><a class="reference internal" href="#c.PyVarObject" title="PyVarObject"><span class="n">PyVarObject</span></a><span class="p">*</span></span>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_SET_SIZE">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_SET_SIZE</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyVarObject" title="PyVarObject"><span class="n"><span class="pre">PyVarObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">size</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_SET_SIZE" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the object <em>o</em> size to <em>size</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyObject_HEAD_INIT">
<span class="sig-name descname"><span class="n"><span class="pre">PyObject_HEAD_INIT</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_HEAD_INIT" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a macro which expands to initialization values for a new
<a class="reference internal" href="#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> type.  This macro expands to:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">_PyObject_EXTRA_INIT</span>
<span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">type</span><span class="p">,</span>
</pre></div>
</div>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyVarObject_HEAD_INIT">
<span class="sig-name descname"><span class="n"><span class="pre">PyVarObject_HEAD_INIT</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">type</span></span>, <span class="n"><span class="pre">size</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyVarObject_HEAD_INIT" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a macro which expands to initialization values for a new
<a class="reference internal" href="#c.PyVarObject" title="PyVarObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyVarObject</span></code></a> type, including the <a class="reference internal" href="typeobj.html#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">ob_size</span></code></a> field.
This macro expands to:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">_PyObject_EXTRA_INIT</span>
<span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">type</span><span class="p">,</span><span class="w"> </span><span class="n">size</span><span class="p">,</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="implementing-functions-and-methods">
<h2>Implementing functions and methods<a class="headerlink" href="#implementing-functions-and-methods" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyCFunction">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyCFunction</span></span></span><a class="headerlink" href="#c.PyCFunction" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Type of the functions used to implement most Python callables in C.
Functions of this type take two <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> parameters and return
one such value.  If the return value is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, an exception shall have
been set.  If not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the return value is interpreted as the return
value of the function as exposed in Python.  The function must return a new
reference.</p>
<p>The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">PyCFunction</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span>
<span class="w">                      </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">);</span>
</pre></div>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyCFunctionWithKeywords">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyCFunctionWithKeywords</span></span></span><a class="headerlink" href="#c.PyCFunctionWithKeywords" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Type of the functions used to implement Python callables in C
with signature <a class="reference internal" href="#meth-varargs-meth-keywords"><span class="std std-ref">METH_VARARGS | METH_KEYWORDS</span></a>.
The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">PyCFunctionWithKeywords</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span>
<span class="w">                                  </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">,</span>
<span class="w">                                  </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">kwargs</span><span class="p">);</span>
</pre></div>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyCFunctionFast">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyCFunctionFast</span></span></span><a class="headerlink" href="#c.PyCFunctionFast" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.13.</em><p>Type of the functions used to implement Python callables in C
with signature <a class="reference internal" href="#c.METH_FASTCALL" title="METH_FASTCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">METH_FASTCALL</span></code></a>.
The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">PyCFunctionFast</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span>
<span class="w">                          </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="k">const</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">,</span>
<span class="w">                          </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="n">nargs</span><span class="p">);</span>
</pre></div>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyCFunctionFastWithKeywords">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyCFunctionFastWithKeywords</span></span></span><a class="headerlink" href="#c.PyCFunctionFastWithKeywords" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.13.</em><p>Type of the functions used to implement Python callables in C
with signature <a class="reference internal" href="#meth-fastcall-meth-keywords"><span class="std std-ref">METH_FASTCALL | METH_KEYWORDS</span></a>.
The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">PyCFunctionFastWithKeywords</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span>
<span class="w">                                      </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="k">const</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">,</span>
<span class="w">                                      </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="n">nargs</span><span class="p">,</span>
<span class="w">                                      </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">kwnames</span><span class="p">);</span>
</pre></div>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyCMethod">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyCMethod</span></span></span><a class="headerlink" href="#c.PyCMethod" title="Link to this definition">¶</a><br /></dt>
<dd><p>Type of the functions used to implement Python callables in C
with signature <a class="reference internal" href="#meth-method-meth-fastcall-meth-keywords"><span class="std std-ref">METH_METHOD | METH_FASTCALL | METH_KEYWORDS</span></a>.
The function signature is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">PyCMethod</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">self</span><span class="p">,</span>
<span class="w">                    </span><span class="n">PyTypeObject</span><span class="w"> </span><span class="o">*</span><span class="n">defining_class</span><span class="p">,</span>
<span class="w">                    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="k">const</span><span class="w"> </span><span class="o">*</span><span class="n">args</span><span class="p">,</span>
<span class="w">                    </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="n">nargs</span><span class="p">,</span>
<span class="w">                    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">kwnames</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyMethodDef">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMethodDef</span></span></span><a class="headerlink" href="#c.PyMethodDef" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> (including all members).</em><p>Structure used to describe a method of an extension type.  This structure has
four fields:</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyMethodDef.ml_name">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">ml_name</span></span></span><a class="headerlink" href="#c.PyMethodDef.ml_name" title="Link to this definition">¶</a><br /></dt>
<dd><p>Name of the method.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMethodDef.ml_meth">
<a class="reference internal" href="#c.PyCFunction" title="PyCFunction"><span class="n"><span class="pre">PyCFunction</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ml_meth</span></span></span><a class="headerlink" href="#c.PyMethodDef.ml_meth" title="Link to this definition">¶</a><br /></dt>
<dd><p>Pointer to the C implementation.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMethodDef.ml_flags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ml_flags</span></span></span><a class="headerlink" href="#c.PyMethodDef.ml_flags" title="Link to this definition">¶</a><br /></dt>
<dd><p>Flags bits indicating how the call should be constructed.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMethodDef.ml_doc">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">ml_doc</span></span></span><a class="headerlink" href="#c.PyMethodDef.ml_doc" title="Link to this definition">¶</a><br /></dt>
<dd><p>Points to the contents of the docstring.</p>
</dd></dl>

</dd></dl>

<p>The <a class="reference internal" href="#c.PyMethodDef.ml_meth" title="PyMethodDef.ml_meth"><code class="xref c c-member docutils literal notranslate"><span class="pre">ml_meth</span></code></a> is a C function pointer.
The functions may be of different
types, but they always return <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span>.  If the function is not of
the <a class="reference internal" href="#c.PyCFunction" title="PyCFunction"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunction</span></code></a>, the compiler will require a cast in the method table.
Even though <a class="reference internal" href="#c.PyCFunction" title="PyCFunction"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunction</span></code></a> defines the first parameter as
<span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span>, it is common that the method implementation uses the
specific C type of the <em>self</em> object.</p>
<p>The <a class="reference internal" href="#c.PyMethodDef.ml_flags" title="PyMethodDef.ml_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">ml_flags</span></code></a> field is a bitfield which can include
the following flags.
The individual flags indicate either a calling convention or a binding
convention.</p>
<p>There are these calling conventions:</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_VARARGS">
<span class="sig-name descname"><span class="n"><span class="pre">METH_VARARGS</span></span></span><a class="headerlink" href="#c.METH_VARARGS" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is the typical calling convention, where the methods have the type
<a class="reference internal" href="#c.PyCFunction" title="PyCFunction"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunction</span></code></a>. The function expects two <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> values.
The first one is the <em>self</em> object for methods; for module functions, it is
the module object.  The second parameter (often called <em>args</em>) is a tuple
object representing all arguments. This parameter is typically processed
using <a class="reference internal" href="arg.html#c.PyArg_ParseTuple" title="PyArg_ParseTuple"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_ParseTuple()</span></code></a> or <a class="reference internal" href="arg.html#c.PyArg_UnpackTuple" title="PyArg_UnpackTuple"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_UnpackTuple()</span></code></a>.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_KEYWORDS">
<span class="sig-name descname"><span class="n"><span class="pre">METH_KEYWORDS</span></span></span><a class="headerlink" href="#c.METH_KEYWORDS" title="Link to this definition">¶</a><br /></dt>
<dd><p>Can only be used in certain combinations with other flags:
<a class="reference internal" href="#meth-varargs-meth-keywords"><span class="std std-ref">METH_VARARGS | METH_KEYWORDS</span></a>,
<a class="reference internal" href="#meth-fastcall-meth-keywords"><span class="std std-ref">METH_FASTCALL | METH_KEYWORDS</span></a> and
<a class="reference internal" href="#meth-method-meth-fastcall-meth-keywords"><span class="std std-ref">METH_METHOD | METH_FASTCALL | METH_KEYWORDS</span></a>.</p>
</dd></dl>

<dl class="simple" id="meth-varargs-meth-keywords">
<dt><span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.METH_VARARGS" title="METH_VARARGS"><span class="n">METH_VARARGS</span></a><span class="w"> </span><span class="o">|</span><span class="w"> </span><a class="reference internal" href="#c.METH_KEYWORDS" title="METH_KEYWORDS"><span class="n">METH_KEYWORDS</span></a></span></dt><dd><p>Methods with these flags must be of type <a class="reference internal" href="#c.PyCFunctionWithKeywords" title="PyCFunctionWithKeywords"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunctionWithKeywords</span></code></a>.
The function expects three parameters: <em>self</em>, <em>args</em>, <em>kwargs</em> where
<em>kwargs</em> is a dictionary of all the keyword arguments or possibly <code class="docutils literal notranslate"><span class="pre">NULL</span></code>
if there are no keyword arguments.  The parameters are typically processed
using <a class="reference internal" href="arg.html#c.PyArg_ParseTupleAndKeywords" title="PyArg_ParseTupleAndKeywords"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_ParseTupleAndKeywords()</span></code></a>.</p>
</dd>
</dl>
<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_FASTCALL">
<span class="sig-name descname"><span class="n"><span class="pre">METH_FASTCALL</span></span></span><a class="headerlink" href="#c.METH_FASTCALL" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fast calling convention supporting only positional arguments.
The methods have the type <a class="reference internal" href="#c.PyCFunctionFast" title="PyCFunctionFast"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunctionFast</span></code></a>.
The first parameter is <em>self</em>, the second parameter is a C array
of <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> values indicating the arguments and the third
parameter is the number of arguments (the length of the array).</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.7.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><code class="docutils literal notranslate"><span class="pre">METH_FASTCALL</span></code> is now part of the <a class="reference internal" href="stable.html#stable-abi"><span class="std std-ref">stable ABI</span></a>.</p>
</div>
</dd></dl>

<dl id="meth-fastcall-meth-keywords">
<dt><span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.METH_FASTCALL" title="METH_FASTCALL"><span class="n">METH_FASTCALL</span></a><span class="w"> </span><span class="o">|</span><span class="w"> </span><a class="reference internal" href="#c.METH_KEYWORDS" title="METH_KEYWORDS"><span class="n">METH_KEYWORDS</span></a></span></dt><dd><p>Extension of <a class="reference internal" href="#c.METH_FASTCALL" title="METH_FASTCALL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">METH_FASTCALL</span></code></a> supporting also keyword arguments,
with methods of type <a class="reference internal" href="#c.PyCFunctionFastWithKeywords" title="PyCFunctionFastWithKeywords"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunctionFastWithKeywords</span></code></a>.
Keyword arguments are passed the same way as in the
<a class="reference internal" href="call.html#vectorcall"><span class="std std-ref">vectorcall protocol</span></a>:
there is an additional fourth <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> parameter
which is a tuple representing the names of the keyword arguments
(which are guaranteed to be strings)
or possibly <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if there are no keywords.  The values of the keyword
arguments are stored in the <em>args</em> array, after the positional arguments.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.7.</span></p>
</div>
</dd>
</dl>
<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_METHOD">
<span class="sig-name descname"><span class="n"><span class="pre">METH_METHOD</span></span></span><a class="headerlink" href="#c.METH_METHOD" title="Link to this definition">¶</a><br /></dt>
<dd><p>Can only be used in the combination with other flags:
<a class="reference internal" href="#meth-method-meth-fastcall-meth-keywords"><span class="std std-ref">METH_METHOD | METH_FASTCALL | METH_KEYWORDS</span></a>.</p>
</dd></dl>

<dl id="meth-method-meth-fastcall-meth-keywords">
<dt><span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.METH_METHOD" title="METH_METHOD"><span class="n">METH_METHOD</span></a><span class="w"> </span><span class="o">|</span><span class="w"> </span><a class="reference internal" href="#c.METH_FASTCALL" title="METH_FASTCALL"><span class="n">METH_FASTCALL</span></a><span class="w"> </span><span class="o">|</span><span class="w"> </span><a class="reference internal" href="#c.METH_KEYWORDS" title="METH_KEYWORDS"><span class="n">METH_KEYWORDS</span></a></span></dt><dd><p>Extension of <a class="reference internal" href="#meth-fastcall-meth-keywords"><span class="std std-ref">METH_FASTCALL | METH_KEYWORDS</span></a>
supporting the <em>defining class</em>, that is,
the class that contains the method in question.
The defining class might be a superclass of <code class="docutils literal notranslate"><span class="pre">Py_TYPE(self)</span></code>.</p>
<p>The method needs to be of type <a class="reference internal" href="#c.PyCMethod" title="PyCMethod"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCMethod</span></code></a>, the same as for
<code class="docutils literal notranslate"><span class="pre">METH_FASTCALL</span> <span class="pre">|</span> <span class="pre">METH_KEYWORDS</span></code> with <code class="docutils literal notranslate"><span class="pre">defining_class</span></code> argument added after
<code class="docutils literal notranslate"><span class="pre">self</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd>
</dl>
<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_NOARGS">
<span class="sig-name descname"><span class="n"><span class="pre">METH_NOARGS</span></span></span><a class="headerlink" href="#c.METH_NOARGS" title="Link to this definition">¶</a><br /></dt>
<dd><p>Methods without parameters don’t need to check whether arguments are given if
they are listed with the <a class="reference internal" href="#c.METH_NOARGS" title="METH_NOARGS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">METH_NOARGS</span></code></a> flag.  They need to be of type
<a class="reference internal" href="#c.PyCFunction" title="PyCFunction"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunction</span></code></a>.  The first parameter is typically named <em>self</em> and will
hold a reference to the module or object instance.  In all cases the second
parameter will be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>The function must have 2 parameters. Since the second parameter is unused,
<a class="reference internal" href="intro.html#c.Py_UNUSED" title="Py_UNUSED"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_UNUSED</span></code></a> can be used to prevent a compiler warning.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_O">
<span class="sig-name descname"><span class="n"><span class="pre">METH_O</span></span></span><a class="headerlink" href="#c.METH_O" title="Link to this definition">¶</a><br /></dt>
<dd><p>Methods with a single object argument can be listed with the <a class="reference internal" href="#c.METH_O" title="METH_O"><code class="xref c c-macro docutils literal notranslate"><span class="pre">METH_O</span></code></a>
flag, instead of invoking <a class="reference internal" href="arg.html#c.PyArg_ParseTuple" title="PyArg_ParseTuple"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_ParseTuple()</span></code></a> with a <code class="docutils literal notranslate"><span class="pre">&quot;O&quot;</span></code> argument.
They have the type <a class="reference internal" href="#c.PyCFunction" title="PyCFunction"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunction</span></code></a>, with the <em>self</em> parameter, and a
<span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> parameter representing the single argument.</p>
</dd></dl>

<p>These two constants are not used to indicate the calling convention but the
binding when use with methods of classes.  These may not be used for functions
defined for modules.  At most one of these flags may be set for any given
method.</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_CLASS">
<span class="sig-name descname"><span class="n"><span class="pre">METH_CLASS</span></span></span><a class="headerlink" href="#c.METH_CLASS" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-0">The method will be passed the type object as the first parameter rather
than an instance of the type.  This is used to create <em>class methods</em>,
similar to what is created when using the <a class="reference internal" href="../library/functions.html#classmethod" title="classmethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">classmethod()</span></code></a> built-in
function.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_STATIC">
<span class="sig-name descname"><span class="n"><span class="pre">METH_STATIC</span></span></span><a class="headerlink" href="#c.METH_STATIC" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-1">The method will be passed <code class="docutils literal notranslate"><span class="pre">NULL</span></code> as the first parameter rather than an
instance of the type.  This is used to create <em>static methods</em>, similar to
what is created when using the <a class="reference internal" href="../library/functions.html#staticmethod" title="staticmethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">staticmethod()</span></code></a> built-in function.</p>
</dd></dl>

<p>One other constant controls whether a method is loaded in place of another
definition with the same method name.</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.METH_COEXIST">
<span class="sig-name descname"><span class="n"><span class="pre">METH_COEXIST</span></span></span><a class="headerlink" href="#c.METH_COEXIST" title="Link to this definition">¶</a><br /></dt>
<dd><p>The method will be loaded in place of existing definitions.  Without
<em>METH_COEXIST</em>, the default is to skip repeated definitions.  Since slot
wrappers are loaded before the method table, the existence of a
<em>sq_contains</em> slot, for example, would generate a wrapped method named
<a class="reference internal" href="../reference/datamodel.html#object.__contains__" title="object.__contains__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__contains__()</span></code></a> and preclude the loading of a corresponding
PyCFunction with the same name.  With the flag defined, the PyCFunction
will be loaded in place of the wrapper object and will co-exist with the
slot.  This is helpful because calls to PyCFunctions are optimized more
than wrapper object calls.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyCMethod_New">
<a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyCMethod_New</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMethodDef" title="PyMethodDef"><span class="n"><span class="pre">PyMethodDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ml</span></span>, <a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">self</span></span>, <a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">cls</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyCMethod_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.9.</em><p>Turn <em>ml</em> into a Python <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> object.
The caller must ensure that <em>ml</em> outlives the <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a>.
Typically, <em>ml</em> is defined as a static variable.</p>
<p>The <em>self</em> parameter will be passed as the <em>self</em> argument
to the C function in <code class="docutils literal notranslate"><span class="pre">ml-&gt;ml_meth</span></code> when invoked.
<em>self</em> can be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>The <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> object’s <code class="docutils literal notranslate"><span class="pre">__module__</span></code> attribute
can be set from the given <em>module</em> argument.
<em>module</em> should be a Python string,
which will be used as name of the module the function is defined in.
If unavailable, it can be set to <a class="reference internal" href="../library/constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="../reference/datamodel.html#function.__module__" title="function.__module__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">function.__module__</span></code></a></p>
</div>
<p>The <em>cls</em> parameter will be passed as the <em>defining_class</em>
argument to the C function.
Must be set if <a class="reference internal" href="#c.METH_METHOD" title="METH_METHOD"><code class="xref c c-macro docutils literal notranslate"><span class="pre">METH_METHOD</span></code></a> is set on <code class="docutils literal notranslate"><span class="pre">ml-&gt;ml_flags</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyCFunction_NewEx">
<a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyCFunction_NewEx</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMethodDef" title="PyMethodDef"><span class="n"><span class="pre">PyMethodDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ml</span></span>, <a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">self</span></span>, <a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyCFunction_NewEx" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Equivalent to <code class="docutils literal notranslate"><span class="pre">PyCMethod_New(ml,</span> <span class="pre">self,</span> <span class="pre">module,</span> <span class="pre">NULL)</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyCFunction_New">
<a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyCFunction_New</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyMethodDef" title="PyMethodDef"><span class="n"><span class="pre">PyMethodDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ml</span></span>, <a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">self</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyCFunction_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.4.</em><p>Equivalent to <code class="docutils literal notranslate"><span class="pre">PyCMethod_New(ml,</span> <span class="pre">self,</span> <span class="pre">NULL,</span> <span class="pre">NULL)</span></code>.</p>
</dd></dl>

</section>
<section id="accessing-attributes-of-extension-types">
<h2>Accessing attributes of extension types<a class="headerlink" href="#accessing-attributes-of-extension-types" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyMemberDef">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMemberDef</span></span></span><a class="headerlink" href="#c.PyMemberDef" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> (including all members).</em><p>Structure which describes an attribute of a type which corresponds to a C
struct member.
When defining a class, put a NULL-terminated array of these
structures in the <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_members" title="PyTypeObject.tp_members"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_members</span></code></a> slot.</p>
<p>Its fields are, in order:</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyMemberDef.name">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">name</span></span></span><a class="headerlink" href="#c.PyMemberDef.name" title="Link to this definition">¶</a><br /></dt>
<dd><p>Name of the member.
A NULL value marks the end of a <code class="docutils literal notranslate"><span class="pre">PyMemberDef[]</span></code> array.</p>
<p>The string should be static, no copy is made of it.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMemberDef.type">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">type</span></span></span><a class="headerlink" href="#c.PyMemberDef.type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The type of the member in the C struct.
See <a class="reference internal" href="#pymemberdef-types"><span class="std std-ref">Member types</span></a> for the possible values.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMemberDef.offset">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">offset</span></span></span><a class="headerlink" href="#c.PyMemberDef.offset" title="Link to this definition">¶</a><br /></dt>
<dd><p>The offset in bytes that the member is located on the type’s object struct.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMemberDef.flags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">flags</span></span></span><a class="headerlink" href="#c.PyMemberDef.flags" title="Link to this definition">¶</a><br /></dt>
<dd><p>Zero or more of the <a class="reference internal" href="#pymemberdef-flags"><span class="std std-ref">Member flags</span></a>, combined using bitwise OR.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyMemberDef.doc">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">doc</span></span></span><a class="headerlink" href="#c.PyMemberDef.doc" title="Link to this definition">¶</a><br /></dt>
<dd><p>The docstring, or NULL.
The string should be static, no copy is made of it.
Typically, it is defined using <a class="reference internal" href="intro.html#c.PyDoc_STR" title="PyDoc_STR"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyDoc_STR</span></code></a>.</p>
</dd></dl>

<p>By default (when <a class="reference internal" href="#c.PyMemberDef.flags" title="PyMemberDef.flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">flags</span></code></a> is <code class="docutils literal notranslate"><span class="pre">0</span></code>), members allow
both read and write access.
Use the <a class="reference internal" href="#c.Py_READONLY" title="Py_READONLY"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_READONLY</span></code></a> flag for read-only access.
Certain types, like <a class="reference internal" href="#c.Py_T_STRING" title="Py_T_STRING"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_T_STRING</span></code></a>, imply <a class="reference internal" href="#c.Py_READONLY" title="Py_READONLY"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_READONLY</span></code></a>.
Only <a class="reference internal" href="#c.Py_T_OBJECT_EX" title="Py_T_OBJECT_EX"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_T_OBJECT_EX</span></code></a> (and legacy <a class="reference internal" href="#c.T_OBJECT" title="T_OBJECT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">T_OBJECT</span></code></a>) members can
be deleted.</p>
<p id="pymemberdef-offsets">For heap-allocated types (created using <a class="reference internal" href="type.html#c.PyType_FromSpec" title="PyType_FromSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpec()</span></code></a> or similar),
<code class="docutils literal notranslate"><span class="pre">PyMemberDef</span></code> may contain a definition for the special member
<code class="docutils literal notranslate"><span class="pre">&quot;__vectorcalloffset__&quot;</span></code>, corresponding to
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_vectorcall_offset" title="PyTypeObject.tp_vectorcall_offset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_vectorcall_offset</span></code></a> in type objects.
These must be defined with <code class="docutils literal notranslate"><span class="pre">Py_T_PYSSIZET</span></code> and <code class="docutils literal notranslate"><span class="pre">Py_READONLY</span></code>, for example:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="n">PyMemberDef</span><span class="w"> </span><span class="n">spam_type_members</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="p">{</span><span class="s">&quot;__vectorcalloffset__&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">Py_T_PYSSIZET</span><span class="p">,</span>
<span class="w">     </span><span class="n">offsetof</span><span class="p">(</span><span class="n">Spam_object</span><span class="p">,</span><span class="w"> </span><span class="n">vectorcall</span><span class="p">),</span><span class="w"> </span><span class="n">Py_READONLY</span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span><span class="nb">NULL</span><span class="p">}</span><span class="w">  </span><span class="cm">/* Sentinel */</span>
<span class="p">};</span>
</pre></div>
</div>
<p>(You may need to <code class="docutils literal notranslate"><span class="pre">#include</span> <span class="pre">&lt;stddef.h&gt;</span></code> for <code class="xref c c-func docutils literal notranslate"><span class="pre">offsetof()</span></code>.)</p>
<p>The legacy offsets <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_dictoffset" title="PyTypeObject.tp_dictoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_dictoffset</span></code></a> and
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_weaklistoffset" title="PyTypeObject.tp_weaklistoffset"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_weaklistoffset</span></code></a> can be defined similarly using
<code class="docutils literal notranslate"><span class="pre">&quot;__dictoffset__&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;__weaklistoffset__&quot;</span></code> members, but extensions
are strongly encouraged to use <a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_MANAGED_DICT" title="Py_TPFLAGS_MANAGED_DICT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_DICT</span></code></a> and
<a class="reference internal" href="typeobj.html#c.Py_TPFLAGS_MANAGED_WEAKREF" title="Py_TPFLAGS_MANAGED_WEAKREF"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TPFLAGS_MANAGED_WEAKREF</span></code></a> instead.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><code class="docutils literal notranslate"><span class="pre">PyMemberDef</span></code> is always available.
Previously, it required including <code class="docutils literal notranslate"><span class="pre">&quot;structmember.h&quot;</span></code>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMember_GetOne">
<a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyMember_GetOne</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj_addr</span></span>, <span class="k"><span class="pre">struct</span></span><span class="w"> </span><a class="reference internal" href="#c.PyMemberDef" title="PyMemberDef"><span class="n"><span class="pre">PyMemberDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">m</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMember_GetOne" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Get an attribute belonging to the object at address <em>obj_addr</em>.  The
attribute is described by <code class="docutils literal notranslate"><span class="pre">PyMemberDef</span></code> <em>m</em>.  Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>
on error.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><code class="docutils literal notranslate"><span class="pre">PyMember_GetOne</span></code> is always available.
Previously, it required including <code class="docutils literal notranslate"><span class="pre">&quot;structmember.h&quot;</span></code>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyMember_SetOne">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyMember_SetOne</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj_addr</span></span>, <span class="k"><span class="pre">struct</span></span><span class="w"> </span><a class="reference internal" href="#c.PyMemberDef" title="PyMemberDef"><span class="n"><span class="pre">PyMemberDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">m</span></span>, <a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyMember_SetOne" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Set an attribute belonging to the object at address <em>obj_addr</em> to object <em>o</em>.
The attribute to set is described by <code class="docutils literal notranslate"><span class="pre">PyMemberDef</span></code> <em>m</em>.  Returns <code class="docutils literal notranslate"><span class="pre">0</span></code>
if successful and a negative value on failure.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><code class="docutils literal notranslate"><span class="pre">PyMember_SetOne</span></code> is always available.
Previously, it required including <code class="docutils literal notranslate"><span class="pre">&quot;structmember.h&quot;</span></code>.</p>
</div>
</dd></dl>

<section id="member-flags">
<span id="pymemberdef-flags"></span><h3>Member flags<a class="headerlink" href="#member-flags" title="Link to this heading">¶</a></h3>
<p>The following flags can be used with <a class="reference internal" href="#c.PyMemberDef.flags" title="PyMemberDef.flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyMemberDef.flags</span></code></a>:</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_READONLY">
<span class="sig-name descname"><span class="n"><span class="pre">Py_READONLY</span></span></span><a class="headerlink" href="#c.Py_READONLY" title="Link to this definition">¶</a><br /></dt>
<dd><p>Not writable.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_AUDIT_READ">
<span class="sig-name descname"><span class="n"><span class="pre">Py_AUDIT_READ</span></span></span><a class="headerlink" href="#c.Py_AUDIT_READ" title="Link to this definition">¶</a><br /></dt>
<dd><p>Emit an <code class="docutils literal notranslate"><span class="pre">object.__getattr__</span></code> <a class="reference internal" href="../library/audit_events.html#audit-events"><span class="std std-ref">audit event</span></a>
before reading.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_RELATIVE_OFFSET">
<span class="sig-name descname"><span class="n"><span class="pre">Py_RELATIVE_OFFSET</span></span></span><a class="headerlink" href="#c.Py_RELATIVE_OFFSET" title="Link to this definition">¶</a><br /></dt>
<dd><p>Indicates that the <a class="reference internal" href="#c.PyMemberDef.offset" title="PyMemberDef.offset"><code class="xref c c-member docutils literal notranslate"><span class="pre">offset</span></code></a> of this <code class="docutils literal notranslate"><span class="pre">PyMemberDef</span></code>
entry indicates an offset from the subclass-specific data, rather than
from <code class="docutils literal notranslate"><span class="pre">PyObject</span></code>.</p>
<p>Can only be used as part of <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_members" title="PyTypeObject.tp_members"><code class="xref c c-member docutils literal notranslate"><span class="pre">Py_tp_members</span></code></a>
<a class="reference internal" href="type.html#c.PyType_Slot" title="PyType_Slot"><code class="xref c c-type docutils literal notranslate"><span class="pre">slot</span></code></a> when creating a class using negative
<a class="reference internal" href="type.html#c.PyType_Spec.basicsize" title="PyType_Spec.basicsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">basicsize</span></code></a>.
It is mandatory in that case.</p>
<p>This flag is only used in <a class="reference internal" href="type.html#c.PyType_Slot" title="PyType_Slot"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyType_Slot</span></code></a>.
When setting <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_members" title="PyTypeObject.tp_members"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_members</span></code></a> during
class creation, Python clears it and sets
<a class="reference internal" href="#c.PyMemberDef.offset" title="PyMemberDef.offset"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyMemberDef.offset</span></code></a> to the offset from the <code class="docutils literal notranslate"><span class="pre">PyObject</span></code> struct.</p>
</dd></dl>

<div class="versionchanged" id="index-2">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <code class="xref c c-macro docutils literal notranslate"><span class="pre">RESTRICTED</span></code>, <code class="xref c c-macro docutils literal notranslate"><span class="pre">READ_RESTRICTED</span></code> and
<code class="xref c c-macro docutils literal notranslate"><span class="pre">WRITE_RESTRICTED</span></code> macros available with
<code class="docutils literal notranslate"><span class="pre">#include</span> <span class="pre">&quot;structmember.h&quot;</span></code> are deprecated.
<code class="xref c c-macro docutils literal notranslate"><span class="pre">READ_RESTRICTED</span></code> and <code class="xref c c-macro docutils literal notranslate"><span class="pre">RESTRICTED</span></code> are equivalent to
<a class="reference internal" href="#c.Py_AUDIT_READ" title="Py_AUDIT_READ"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_AUDIT_READ</span></code></a>; <code class="xref c c-macro docutils literal notranslate"><span class="pre">WRITE_RESTRICTED</span></code> does nothing.</p>
</div>
<div class="versionchanged" id="index-3">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The <code class="xref c c-macro docutils literal notranslate"><span class="pre">READONLY</span></code> macro was renamed to <a class="reference internal" href="#c.Py_READONLY" title="Py_READONLY"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_READONLY</span></code></a>.
The <code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_AUDIT_READ</span></code> macro was renamed with the <code class="docutils literal notranslate"><span class="pre">Py_</span></code> prefix.
The new names are now always available.
Previously, these required <code class="docutils literal notranslate"><span class="pre">#include</span> <span class="pre">&quot;structmember.h&quot;</span></code>.
The header is still available and it provides the old names.</p>
</div>
</section>
<section id="member-types">
<span id="pymemberdef-types"></span><h3>Member types<a class="headerlink" href="#member-types" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#c.PyMemberDef.type" title="PyMemberDef.type"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyMemberDef.type</span></code></a> can be one of the following macros corresponding
to various C types.
When the member is accessed in Python, it will be converted to the
equivalent Python type.
When it is set from Python, it will be converted back to the C type.
If that is not possible, an exception such as <a class="reference internal" href="../library/exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> or
<a class="reference internal" href="../library/exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
<p>Unless marked (D), attributes defined this way cannot be deleted
using e.g. <a class="reference internal" href="../reference/simple_stmts.html#del"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">del</span></code></a> or <a class="reference internal" href="../library/functions.html#delattr" title="delattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">delattr()</span></code></a>.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Macro name</p></th>
<th class="head"><p>C type</p></th>
<th class="head"><p>Python type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_BYTE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_BYTE</span></span></span><a class="headerlink" href="#c.Py_T_BYTE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">char</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_SHORT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_SHORT</span></span></span><a class="headerlink" href="#c.Py_T_SHORT" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">short</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_INT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_INT</span></span></span><a class="headerlink" href="#c.Py_T_INT" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">int</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_LONG">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_LONG</span></span></span><a class="headerlink" href="#c.Py_T_LONG" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">long</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_LONGLONG">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_LONGLONG</span></span></span><a class="headerlink" href="#c.Py_T_LONGLONG" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">long</span><span class="w"> </span><span class="kt">long</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_UBYTE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_UBYTE</span></span></span><a class="headerlink" href="#c.Py_T_UBYTE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">char</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_UINT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_UINT</span></span></span><a class="headerlink" href="#c.Py_T_UINT" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">int</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_USHORT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_USHORT</span></span></span><a class="headerlink" href="#c.Py_T_USHORT" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">short</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_ULONG">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_ULONG</span></span></span><a class="headerlink" href="#c.Py_T_ULONG" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">long</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_ULONGLONG">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_ULONGLONG</span></span></span><a class="headerlink" href="#c.Py_T_ULONGLONG" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="kt">long</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_PYSSIZET">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_PYSSIZET</span></span></span><a class="headerlink" href="#c.Py_T_PYSSIZET" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n">Py_ssize_t</span></a></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_FLOAT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_FLOAT</span></span></span><a class="headerlink" href="#c.Py_T_FLOAT" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">float</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_DOUBLE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_DOUBLE</span></span></span><a class="headerlink" href="#c.Py_T_DOUBLE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">double</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_BOOL">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_BOOL</span></span></span><a class="headerlink" href="#c.Py_T_BOOL" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">char</span></span>
(written as 0 or 1)</p></td>
<td><p><a class="reference internal" href="../library/functions.html#bool" title="bool"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_STRING">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_STRING</span></span></span><a class="headerlink" href="#c.Py_T_STRING" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="p">*</span></span> (*)</p></td>
<td><p><a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> (RO)</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_STRING_INPLACE">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_STRING_INPLACE</span></span></span><a class="headerlink" href="#c.Py_T_STRING_INPLACE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="p">[</span><span class="p">]</span></span> (*)</p></td>
<td><p><a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> (RO)</p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_CHAR">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_CHAR</span></span></span><a class="headerlink" href="#c.Py_T_CHAR" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><span class="kt">char</span></span> (0-127)</p></td>
<td><p><a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> (**)</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.Py_T_OBJECT_EX">
<span class="sig-name descname"><span class="n"><span class="pre">Py_T_OBJECT_EX</span></span></span><a class="headerlink" href="#c.Py_T_OBJECT_EX" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p><span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span></p></td>
<td><p><a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> (D)</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>(*): Zero-terminated, UTF8-encoded C string.
With <code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_T_STRING</span></code> the C representation is a pointer;
with <code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_T_STRING_INPLACE</span></code> the string is stored directly
in the structure.</p>
<p>(**): String of length 1. Only ASCII is accepted.</p>
<p>(RO): Implies <a class="reference internal" href="#c.Py_READONLY" title="Py_READONLY"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_READONLY</span></code></a>.</p>
<p>(D): Can be deleted, in which case the pointer is set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
Reading a <code class="docutils literal notranslate"><span class="pre">NULL</span></code> pointer raises <a class="reference internal" href="../library/exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a>.</p>
</div></blockquote>
<div class="versionadded" id="index-4">
<p><span class="versionmodified added">Added in version 3.12: </span>In previous versions, the macros were only available with
<code class="docutils literal notranslate"><span class="pre">#include</span> <span class="pre">&quot;structmember.h&quot;</span></code> and were named without the <code class="docutils literal notranslate"><span class="pre">Py_</span></code> prefix
(e.g. as <code class="docutils literal notranslate"><span class="pre">T_INT</span></code>).
The header is still available and contains the old names, along with
the following deprecated types:</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.T_OBJECT">
<span class="sig-name descname"><span class="n"><span class="pre">T_OBJECT</span></span></span><a class="headerlink" href="#c.T_OBJECT" title="Link to this definition">¶</a><br /></dt>
<dd><p>Like <code class="docutils literal notranslate"><span class="pre">Py_T_OBJECT_EX</span></code>, but <code class="docutils literal notranslate"><span class="pre">NULL</span></code> is converted to <code class="docutils literal notranslate"><span class="pre">None</span></code>.
This results in surprising behavior in Python: deleting the attribute
effectively sets it to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.T_NONE">
<span class="sig-name descname"><span class="n"><span class="pre">T_NONE</span></span></span><a class="headerlink" href="#c.T_NONE" title="Link to this definition">¶</a><br /></dt>
<dd><p>Always <code class="docutils literal notranslate"><span class="pre">None</span></code>. Must be used with <a class="reference internal" href="#c.Py_READONLY" title="Py_READONLY"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_READONLY</span></code></a>.</p>
</dd></dl>

</div>
</section>
<section id="defining-getters-and-setters">
<h3>Defining Getters and Setters<a class="headerlink" href="#defining-getters-and-setters" title="Link to this heading">¶</a></h3>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyGetSetDef">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyGetSetDef</span></span></span><a class="headerlink" href="#c.PyGetSetDef" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> (including all members).</em><p>Structure to define property-like access for a type. See also description of
the <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_getset" title="PyTypeObject.tp_getset"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyTypeObject.tp_getset</span></code></a> slot.</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyGetSetDef.name">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">name</span></span></span><a class="headerlink" href="#c.PyGetSetDef.name" title="Link to this definition">¶</a><br /></dt>
<dd><p>attribute name</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyGetSetDef.get">
<a class="reference internal" href="#c.getter" title="getter"><span class="n"><span class="pre">getter</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">get</span></span></span><a class="headerlink" href="#c.PyGetSetDef.get" title="Link to this definition">¶</a><br /></dt>
<dd><p>C function to get the attribute.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyGetSetDef.set">
<a class="reference internal" href="#c.setter" title="setter"><span class="n"><span class="pre">setter</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">set</span></span></span><a class="headerlink" href="#c.PyGetSetDef.set" title="Link to this definition">¶</a><br /></dt>
<dd><p>Optional C function to set or delete the attribute.
If <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the attribute is read-only.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyGetSetDef.doc">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">doc</span></span></span><a class="headerlink" href="#c.PyGetSetDef.doc" title="Link to this definition">¶</a><br /></dt>
<dd><p>optional docstring</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyGetSetDef.closure">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">closure</span></span></span><a class="headerlink" href="#c.PyGetSetDef.closure" title="Link to this definition">¶</a><br /></dt>
<dd><p>Optional user data pointer, providing additional data for getter and setter.</p>
</dd></dl>

</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.getter">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">getter</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.getter" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>The <code class="docutils literal notranslate"><span class="pre">get</span></code> function takes one <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> parameter (the
instance) and a user data pointer (the associated <code class="docutils literal notranslate"><span class="pre">closure</span></code>):</p>
<p>It should return a new reference on success or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> with a set exception
on failure.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.setter">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">setter</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.setter" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p><code class="docutils literal notranslate"><span class="pre">set</span></code> functions take two <span class="c-expr sig sig-inline c"><a class="reference internal" href="#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span> parameters (the instance and
the value to be set) and a user data pointer (the associated <code class="docutils literal notranslate"><span class="pre">closure</span></code>):</p>
<p>In case the attribute should be deleted the second parameter is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
Should return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success or <code class="docutils literal notranslate"><span class="pre">-1</span></code> with a set exception on failure.</p>
</dd></dl>

</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Common Object Structures</a><ul>
<li><a class="reference internal" href="#base-object-types-and-macros">Base object types and macros</a></li>
<li><a class="reference internal" href="#implementing-functions-and-methods">Implementing functions and methods</a></li>
<li><a class="reference internal" href="#accessing-attributes-of-extension-types">Accessing attributes of extension types</a><ul>
<li><a class="reference internal" href="#member-flags">Member flags</a></li>
<li><a class="reference internal" href="#member-types">Member types</a></li>
<li><a class="reference internal" href="#defining-getters-and-setters">Defining Getters and Setters</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="allocation.html"
                          title="previous chapter">Allocating Objects on the Heap</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="typeobj.html"
                          title="next chapter">Type Object Structures</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/structures.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="typeobj.html" title="Type Object Structures"
             >next</a> |</li>
        <li class="right" >
          <a href="allocation.html" title="Allocating Objects on the Heap"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="objimpl.html" >Object Implementation Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Common Object Structures</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>