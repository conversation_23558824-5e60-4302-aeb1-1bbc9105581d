from typing import Optional, List
from sqlmodel import Session, select
from app.models.integrations import IntegrationSetting
from app.schemas.integrations import IntegrationConfigCreate, IntegrationConfigUpdate # Ensure this import is correct based on file location

def create_integration_setting(db: Session, *, setting_in: IntegrationConfigCreate, company_id: int) -> IntegrationSetting:
    db_obj_data = setting_in.dict()
    db_obj_data["company_id"] = company_id
    db_setting = IntegrationSetting(**db_obj_data) 
    db.add(db_setting)
    db.commit()
    db.refresh(db_setting)
    return db_setting

def get_integration_setting(db: Session, setting_id: int, company_id: int) -> Optional[IntegrationSetting]:
    statement = select(IntegrationSetting).where(IntegrationSetting.id == setting_id, IntegrationSetting.company_id == company_id)
    return db.exec(statement).first()

def get_integration_setting_by_type(db: Session, *, integration_type: str, company_id: int) -> Optional[IntegrationSetting]:
    statement = select(IntegrationSetting).where(IntegrationSetting.integration_type == integration_type, IntegrationSetting.company_id == company_id)
    return db.exec(statement).first()

def get_all_integration_settings(db: Session, skip: int = 0, limit: int = 100, company_id: int) -> List[IntegrationSetting]:
    statement = select(IntegrationSetting).where(IntegrationSetting.company_id == company_id).offset(skip).limit(limit)
    return db.exec(statement).all()
    
def get_active_messaging_integration(db: Session, company_id: int) -> Optional[IntegrationSetting]:
    statement = select(IntegrationSetting).where(
        IntegrationSetting.is_active == True,
        IntegrationSetting.company_id == company_id
    ).where(
        IntegrationSetting.integration_type.in_(['whatsapp', 'test_sender'])
    )
    # It's possible multiple could be marked active if logic elsewhere doesn't enforce singularity.
    # This function will return the first one found by the database.
    return db.exec(statement).first()

def update_integration_setting(
    db: Session, *, db_setting: IntegrationSetting, setting_in: IntegrationConfigUpdate
) -> IntegrationSetting:
    setting_data = setting_in.dict(exclude_unset=True)
    for key, value in setting_data.items():
        setattr(db_setting, key, value)
    # Handle 'updated_at' if not automatically handled by DB/SQLModel trigger
    # db_setting.updated_at = datetime.utcnow() # If manual update is needed
    db.add(db_setting)
    db.commit()
    db.refresh(db_setting)
    return db_setting

def delete_integration_setting(db: Session, setting_id: int, company_id: int) -> Optional[IntegrationSetting]:
    db_setting = db.exec(select(IntegrationSetting).where(IntegrationSetting.id == setting_id, IntegrationSetting.company_id == company_id)).first()
    if db_setting:
        db.delete(db_setting)
        db.commit()
        # Optionally, return the deleted object or a confirmation
    return db_setting # Returns the object if found and deleted, else None
