<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Slice Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/slice.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Ellipsis Object:" />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Ellipsis Object:" />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Slice Objects &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="MemoryView objects" href="memoryview.html" />
    <link rel="prev" title="Descriptor Objects" href="descriptor.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/slice.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Slice Objects</a><ul>
<li><a class="reference internal" href="#ellipsis-object">Ellipsis Object</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="descriptor.html"
                          title="previous chapter">Descriptor Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="memoryview.html"
                          title="next chapter">MemoryView objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/slice.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="memoryview.html" title="MemoryView objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="descriptor.html" title="Descriptor Objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" accesskey="U">Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Slice Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="slice-objects">
<span id="id1"></span><h1>Slice Objects<a class="headerlink" href="#slice-objects" title="Link to this heading">¶</a></h1>
<dl class="c var">
<dt class="sig sig-object c" id="c.PySlice_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySlice_Type</span></span></span><a class="headerlink" href="#c.PySlice_Type" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>The type object for slice objects.  This is the same as <a class="reference internal" href="../library/functions.html#slice" title="slice"><code class="xref py py-class docutils literal notranslate"><span class="pre">slice</span></code></a> in the
Python layer.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PySlice_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySlice_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ob</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PySlice_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>ob</em> is a slice object; <em>ob</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  This
function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PySlice_New">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PySlice_New</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">stop</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">step</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PySlice_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return a new slice object with the given values.  The <em>start</em>, <em>stop</em>, and
<em>step</em> parameters are used as the values of the slice object attributes of
the same names.  Any of the values may be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, in which case the
<code class="docutils literal notranslate"><span class="pre">None</span></code> will be used for the corresponding attribute.</p>
<p>Return <code class="docutils literal notranslate"><span class="pre">NULL</span></code> with an exception set if
the new object could not be allocated.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PySlice_GetIndices">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySlice_GetIndices</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">slice</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">length</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">stop</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">step</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PySlice_GetIndices" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Retrieve the start, stop and step indices from the slice object <em>slice</em>,
assuming a sequence of length <em>length</em>. Treats indices greater than
<em>length</em> as errors.</p>
<p>Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> on success and <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error with no exception set (unless one of
the indices was not <code class="docutils literal notranslate"><span class="pre">None</span></code> and failed to be converted to an integer,
in which case <code class="docutils literal notranslate"><span class="pre">-1</span></code> is returned with an exception set).</p>
<p>You probably do not want to use this function.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The parameter type for the <em>slice</em> parameter was <code class="docutils literal notranslate"><span class="pre">PySliceObject*</span></code>
before.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PySlice_GetIndicesEx">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySlice_GetIndicesEx</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">slice</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">length</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">stop</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">step</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">slicelength</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PySlice_GetIndicesEx" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Usable replacement for <a class="reference internal" href="#c.PySlice_GetIndices" title="PySlice_GetIndices"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_GetIndices()</span></code></a>.  Retrieve the start,
stop, and step indices from the slice object <em>slice</em> assuming a sequence of
length <em>length</em>, and store the length of the slice in <em>slicelength</em>.  Out
of bounds indices are clipped in a manner consistent with the handling of
normal slices.</p>
<p>Return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success and <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error with an exception set.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function is considered not safe for resizable sequences.
Its invocation should be replaced by a combination of
<a class="reference internal" href="#c.PySlice_Unpack" title="PySlice_Unpack"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_Unpack()</span></code></a> and <a class="reference internal" href="#c.PySlice_AdjustIndices" title="PySlice_AdjustIndices"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_AdjustIndices()</span></code></a> where</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PySlice_GetIndicesEx</span><span class="p">(</span><span class="n">slice</span><span class="p">,</span><span class="w"> </span><span class="n">length</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">start</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">stop</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">step</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">slicelength</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// return error</span>
<span class="p">}</span>
</pre></div>
</div>
<p>is replaced by</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PySlice_Unpack</span><span class="p">(</span><span class="n">slice</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">start</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">stop</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">step</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// return error</span>
<span class="p">}</span>
<span class="n">slicelength</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PySlice_AdjustIndices</span><span class="p">(</span><span class="n">length</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">start</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">stop</span><span class="p">,</span><span class="w"> </span><span class="n">step</span><span class="p">);</span>
</pre></div>
</div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The parameter type for the <em>slice</em> parameter was <code class="docutils literal notranslate"><span class="pre">PySliceObject*</span></code>
before.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6.1: </span>If <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> is not set or set to the value between <code class="docutils literal notranslate"><span class="pre">0x03050400</span></code>
and <code class="docutils literal notranslate"><span class="pre">0x03060000</span></code> (not including) or <code class="docutils literal notranslate"><span class="pre">0x03060100</span></code> or higher
<code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_GetIndicesEx()</span></code> is implemented as a macro using
<code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_Unpack()</span></code> and <code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_AdjustIndices()</span></code>.
Arguments <em>start</em>, <em>stop</em> and <em>step</em> are evaluated more than once.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6.1: </span>If <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> is set to the value less than <code class="docutils literal notranslate"><span class="pre">0x03050400</span></code> or
between <code class="docutils literal notranslate"><span class="pre">0x03060000</span></code> and <code class="docutils literal notranslate"><span class="pre">0x03060100</span></code> (not including)
<code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_GetIndicesEx()</span></code> is a deprecated function.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PySlice_Unpack">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySlice_Unpack</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">slice</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">stop</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">step</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PySlice_Unpack" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Extract the start, stop and step data members from a slice object as
C integers.  Silently reduce values larger than <code class="docutils literal notranslate"><span class="pre">PY_SSIZE_T_MAX</span></code> to
<code class="docutils literal notranslate"><span class="pre">PY_SSIZE_T_MAX</span></code>, silently boost the start and stop values less than
<code class="docutils literal notranslate"><span class="pre">PY_SSIZE_T_MIN</span></code> to <code class="docutils literal notranslate"><span class="pre">PY_SSIZE_T_MIN</span></code>, and silently boost the step
values less than <code class="docutils literal notranslate"><span class="pre">-PY_SSIZE_T_MAX</span></code> to <code class="docutils literal notranslate"><span class="pre">-PY_SSIZE_T_MAX</span></code>.</p>
<p>Return <code class="docutils literal notranslate"><span class="pre">-1</span></code> with an exception set on error, <code class="docutils literal notranslate"><span class="pre">0</span></code> on success.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.6.1.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PySlice_AdjustIndices">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySlice_AdjustIndices</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">length</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">stop</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">step</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PySlice_AdjustIndices" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Adjust start/end slice indices assuming a sequence of the specified length.
Out of bounds indices are clipped in a manner consistent with the handling
of normal slices.</p>
<p>Return the length of the slice.  Always successful.  Doesn’t call Python
code.</p>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 3.6.1.</span></p>
</div>
</dd></dl>

<section id="ellipsis-object">
<h2>Ellipsis Object<a class="headerlink" href="#ellipsis-object" title="Link to this heading">¶</a></h2>
<dl class="c var">
<dt class="sig sig-object c" id="c.PyEllipsis_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEllipsis_Type</span></span></span><a class="headerlink" href="#c.PyEllipsis_Type" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>The type of Python <a class="reference internal" href="../library/constants.html#Ellipsis" title="Ellipsis"><code class="xref py py-const docutils literal notranslate"><span class="pre">Ellipsis</span></code></a> object.  Same as <a class="reference internal" href="../library/types.html#types.EllipsisType" title="types.EllipsisType"><code class="xref py py-class docutils literal notranslate"><span class="pre">types.EllipsisType</span></code></a>
in the Python layer.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_Ellipsis">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_Ellipsis</span></span></span><a class="headerlink" href="#c.Py_Ellipsis" title="Link to this definition">¶</a><br /></dt>
<dd><p>The Python <code class="docutils literal notranslate"><span class="pre">Ellipsis</span></code> object.  This object has no methods.  Like
<a class="reference internal" href="none.html#c.Py_None" title="Py_None"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_None</span></code></a>, it is an <a class="reference internal" href="../glossary.html#term-immortal"><span class="xref std std-term">immortal</span></a> singleton object.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><a class="reference internal" href="#c.Py_Ellipsis" title="Py_Ellipsis"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_Ellipsis</span></code></a> is immortal.</p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Slice Objects</a><ul>
<li><a class="reference internal" href="#ellipsis-object">Ellipsis Object</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="descriptor.html"
                          title="previous chapter">Descriptor Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="memoryview.html"
                          title="next chapter">MemoryView objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/slice.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="memoryview.html" title="MemoryView objects"
             >next</a> |</li>
        <li class="right" >
          <a href="descriptor.html" title="Descriptor Objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" >Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Slice Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>