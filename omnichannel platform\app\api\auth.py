from fastapi import APIRouter, Depends, HTTPException, Response, status, Request
from sqlmodel import Session, select
from typing import Optional
from app.models.user import User
from app.db.session import get_db
from app.schemas.user import UserLogin, UserRead
from app.core.config import settings
from app.api.security import get_current_user, get_current_user_from_cookie
from jose import jwt, JWTError
from datetime import datetime, timedelta
from passlib.context import CryptContext

router = APIRouter(prefix="/auth", tags=["auth"])

SECRET_KEY = settings.SECRET_KEY
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 1 day

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Helper to create JWT token
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

# Login endpoint
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

@router.post("/token")
def login_for_access_token(response: Response, login: UserLogin, db: Session = Depends(get_db)):
    user = db.exec(select(User).where(User.email == login.email)).first()
    if not user or not verify_password(login.password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Incorrect email or password")
    token = create_access_token({"sub": str(user.id), "role": user.role.value, "company_id": user.company_id})
    response.set_cookie(
        key="access_token",
        value=token,
        httponly=True,
        secure=True,
        samesite="lax",
        max_age=60 * 60 * 24,  # 1 day
        path="/"
    )
    return {
        "user": UserRead.from_orm(user),
        "access_token": token,
        "token_type": "bearer"
    }

@router.post("/logout")
def logout(response: Response):
    response.delete_cookie(key="access_token", path="/")
    return {"message": "Logged out"}

@router.get("/me")
def get_current_user_info(current_user: User = Depends(get_current_user_from_cookie)):
    """Get current user information."""
    return UserRead.from_orm(current_user)
