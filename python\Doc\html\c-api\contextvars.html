<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Context Variables Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/contextvars.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This section details the public C API for the contextvars module. Type-check macros: Context object management functions: Context variable functions:" />
<meta property="og:image" content="_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This section details the public C API for the contextvars module. Type-check macros: Context object management functions: Context variable functions:" />
<meta name="theme-color" content="#3776ab">
<meta property="og:image:width" content="200">
<meta property="og:image:height" content="200">

    <title>Context Variables Objects &#8212; Python 3.13.5 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
    <link rel="stylesheet" type="text/css" href="../_static/classic.css?v=234b1a7c" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=5ff89526" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=5349f25f" />
    
    <script src="../_static/documentation_options.js?v=32a6def9"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.13.5 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="DateTime Objects" href="datetime.html" />
    <link rel="prev" title="Coroutine Objects" href="coro.html" />
    
      
      <link rel="canonical" href="https://docs.python.org/3/c-api/contextvars.html">
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg">
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 
            <script type="text/javascript" src="../_static/rtd_switcher.js"></script>
            <meta name="readthedocs-addons-api-version" content="1">

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu">
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo">
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q">
                <input type="submit" value="Go">
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="coro.html"
                          title="previous chapter">Coroutine Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="datetime.html"
                          title="next chapter">DateTime Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/contextvars.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="datetime.html" title="DateTime Objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="coro.html" title="Coroutine Objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" accesskey="U">Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Context Variables Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="context-variables-objects">
<span id="contextvarsobjects"></span><h1>Context Variables Objects<a class="headerlink" href="#context-variables-objects" title="Link to this heading">¶</a></h1>
<div class="versionadded" id="contextvarsobjects-pointertype-change">
<p><span class="versionmodified added">Added in version 3.7.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7.1: </span></p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In Python 3.7.1 the signatures of all context variables
C APIs were <strong>changed</strong> to use <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> pointers instead
of <a class="reference internal" href="#c.PyContext" title="PyContext"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyContext</span></code></a>, <a class="reference internal" href="#c.PyContextVar" title="PyContextVar"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyContextVar</span></code></a>, and
<a class="reference internal" href="#c.PyContextToken" title="PyContextToken"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyContextToken</span></code></a>, e.g.:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="c1">// in 3.7.0:</span>
<span class="n">PyContext</span><span class="w"> </span><span class="o">*</span><span class="nf">PyContext_New</span><span class="p">(</span><span class="kt">void</span><span class="p">);</span>

<span class="c1">// in 3.7.1+:</span>
<span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="nf">PyContext_New</span><span class="p">(</span><span class="kt">void</span><span class="p">);</span>
</pre></div>
</div>
<p>See <a class="reference external" href="https://bugs.python.org/issue?&#64;action=redirect&amp;bpo=34762">bpo-34762</a> for more details.</p>
</div>
</div>
<p>This section details the public C API for the <a class="reference internal" href="../library/contextvars.html#module-contextvars" title="contextvars: Context Variables"><code class="xref py py-mod docutils literal notranslate"><span class="pre">contextvars</span></code></a> module.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyContext">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContext</span></span></span><a class="headerlink" href="#c.PyContext" title="Link to this definition">¶</a><br /></dt>
<dd><p>The C structure used to represent a <a class="reference internal" href="../library/contextvars.html#contextvars.Context" title="contextvars.Context"><code class="xref py py-class docutils literal notranslate"><span class="pre">contextvars.Context</span></code></a>
object.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyContextVar">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContextVar</span></span></span><a class="headerlink" href="#c.PyContextVar" title="Link to this definition">¶</a><br /></dt>
<dd><p>The C structure used to represent a <a class="reference internal" href="../library/contextvars.html#contextvars.ContextVar" title="contextvars.ContextVar"><code class="xref py py-class docutils literal notranslate"><span class="pre">contextvars.ContextVar</span></code></a>
object.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyContextToken">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContextToken</span></span></span><a class="headerlink" href="#c.PyContextToken" title="Link to this definition">¶</a><br /></dt>
<dd><p>The C structure used to represent a <a class="reference internal" href="../library/contextvars.html#contextvars.Token" title="contextvars.Token"><code class="xref py py-class docutils literal notranslate"><span class="pre">contextvars.Token</span></code></a> object.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyContext_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContext_Type</span></span></span><a class="headerlink" href="#c.PyContext_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The type object representing the <em>context</em> type.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyContextVar_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContextVar_Type</span></span></span><a class="headerlink" href="#c.PyContextVar_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The type object representing the <em>context variable</em> type.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyContextToken_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContextToken_Type</span></span></span><a class="headerlink" href="#c.PyContextToken_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The type object representing the <em>context variable token</em> type.</p>
</dd></dl>

<p>Type-check macros:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyContext_CheckExact">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContext_CheckExact</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContext_CheckExact" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>o</em> is of type <a class="reference internal" href="#c.PyContext_Type" title="PyContext_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyContext_Type</span></code></a>. <em>o</em> must not be
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContextVar_CheckExact">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContextVar_CheckExact</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContextVar_CheckExact" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>o</em> is of type <a class="reference internal" href="#c.PyContextVar_Type" title="PyContextVar_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyContextVar_Type</span></code></a>. <em>o</em> must not be
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContextToken_CheckExact">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContextToken_CheckExact</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">o</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContextToken_CheckExact" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>o</em> is of type <a class="reference internal" href="#c.PyContextToken_Type" title="PyContextToken_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyContextToken_Type</span></code></a>.
<em>o</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  This function always succeeds.</p>
</dd></dl>

<p>Context object management functions:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyContext_New">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyContext_New</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContext_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Create a new empty context object.  Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an error
has occurred.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContext_Copy">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyContext_Copy</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ctx</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContext_Copy" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Create a shallow copy of the passed <em>ctx</em> context object.
Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an error has occurred.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContext_CopyCurrent">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyContext_CopyCurrent</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContext_CopyCurrent" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Create a shallow copy of the current thread context.
Returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an error has occurred.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContext_Enter">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContext_Enter</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ctx</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContext_Enter" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set <em>ctx</em> as the current context for the current thread.
Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, and <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContext_Exit">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContext_Exit</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ctx</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContext_Exit" title="Link to this definition">¶</a><br /></dt>
<dd><p>Deactivate the <em>ctx</em> context and restore the previous context as the
current context for the current thread.  Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> on success,
and <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error.</p>
</dd></dl>

<p>Context variable functions:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyContextVar_New">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyContextVar_New</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContextVar_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Create a new <code class="docutils literal notranslate"><span class="pre">ContextVar</span></code> object.  The <em>name</em> parameter is used
for introspection and debug purposes.  The <em>def</em> parameter specifies
a default value for the context variable, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> for no default.
If an error has occurred, this function returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContextVar_Get">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContextVar_Get</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">var</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">default_value</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContextVar_Get" title="Link to this definition">¶</a><br /></dt>
<dd><p>Get the value of a context variable.  Returns <code class="docutils literal notranslate"><span class="pre">-1</span></code> if an error has
occurred during lookup, and <code class="docutils literal notranslate"><span class="pre">0</span></code> if no error occurred, whether or not
a value was found.</p>
<p>If the context variable was found, <em>value</em> will be a pointer to it.
If the context variable was <em>not</em> found, <em>value</em> will point to:</p>
<ul class="simple">
<li><p><em>default_value</em>, if not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>;</p></li>
<li><p>the default value of <em>var</em>, if not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></li>
</ul>
<p>Except for <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the function returns a new reference.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContextVar_Set">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyContextVar_Set</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">var</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContextVar_Set" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount return_new_ref">Return value: New reference.</em><p>Set the value of <em>var</em> to <em>value</em> in the current context.  Returns
a new token object for this change, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an error has occurred.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyContextVar_Reset">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyContextVar_Reset</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">var</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">token</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyContextVar_Reset" title="Link to this definition">¶</a><br /></dt>
<dd><p>Reset the state of the <em>var</em> context variable to that it was in before
<a class="reference internal" href="#c.PyContextVar_Set" title="PyContextVar_Set"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyContextVar_Set()</span></code></a> that returned the <em>token</em> was called.
This function returns <code class="docutils literal notranslate"><span class="pre">0</span></code> on success and <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error.</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="coro.html"
                          title="previous chapter">Coroutine Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="datetime.html"
                          title="next chapter">DateTime Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/contextvars.rst"
            rel="nofollow">Show source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="datetime.html" title="DateTime Objects"
             >next</a> |</li>
        <li class="right" >
          <a href="coro.html" title="Coroutine Objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.13.5 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" >Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Context Variables Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box">
          <input type="submit" value="Go">
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2025, Python Software Foundation.
    <br>
    This page is licensed under the Python Software Foundation License Version 2.
    <br>
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br>
    
      See <a href="/license.html">History and License</a> for more information.<br>
    
    
    <br>

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br>
    <br>
      Last updated on Jun 11, 2025 (15:56 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br>

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    </div>

  </body>
</html>