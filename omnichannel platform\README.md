# Omnichannel Platform

A modern communication platform to unify and automate conversations across WhatsApp, SMS, and more. Designed for marketing teams, support agents, and automation builders — all in one place.

---

## ⚙️ Architecture

This platform is built using a modern full-stack architecture:

| Layer      | Tech                            |
|------------|---------------------------------|
| Frontend   | React + Vite + Chakra UI        |
| Routing/UI | React Router + TanStack Query   |
| Backend    | Python + FastAPI + Uvicorn      |
| Messaging  | WhatsApp Business API, SMS APIs |
| Realtime   | (WebSocket layer optional)      |
| Database   | (PostgreSQL / MongoDB planned)  |

---

## ✅ Core Features

The following features are part of the current platform (some are partially implemented):

### 1.  Centralized Dashboard  
Overview of system metrics including:
- Total conversations  
- Active users  
- Messages sent  
- Average response time

### 2.  Unified Inbox  
Manage conversations from all channels in one view:
- Message history  
- Customer info  
- Status (active, archived)

### 3.  Campaign Management  
Create and track outbound messaging campaigns:
- Bulk WhatsApp & SMS sends  
- Templates & personalization  
- Status tracking

### 4.  Visual Bot Builder  
Drag-and-drop flow builder for automating conversations:
- Message, webhook, condition, handoff nodes  
- WhatsApp template + media support  
- JSON import/export  

### 5.  Message Templates  
Manage pre-approved WhatsApp templates and metadata:
- Names, languages, and parameters  
- Media templates coming soon

### 6.  Contact Management  
Light CRM to store and segment customers:
- Tags, groups, status  
- Used in campaigns and automations

### 7.  Bulk Messaging  
Send messages at scale across multiple channels:
- CSV imports or segment targeting  
- Throttle and compliance options

### 8.  Bot Testing  
Test your bot flows before going live:
- Simulate logic branches  
- See real output & debug paths

### 9.  Analytics & Reports  
Monitor key engagement KPIs:
- Total messages  
- Top channels  
- Flow success/fail rates  
- Campaign performance

### 10.  Channel & API Configuration  
Manage credentials for:
- WhatsApp Business Cloud API  
- SMS Gateway (Twilio, etc.)  
- Telegram, Email (planned)

### 11.  User Administration  
Multi-user support for managing platform access:
- Roles (Admin, Editor, Viewer)  
- User switching (coming soon)

---

##  In Progress / Coming Soon

- ️ **WhatsApp Carousel Messaging**  
  Showcase products in scrollable cards directly in WhatsApp.

-  **WhatsApp Flows Integration**  
  Build guided flows for lead capture, signups, feedback, etc.

-  **Telegram & Email Channel Support**  
  Pluggable architecture allows easy channel expansion.

-  **AI/NLP Bot Enhancements**  
  Route messages using intent detection or OpenAI integration.

-  **Flow Versioning & Auto-save**  
  Save, restore, and track bot flow versions over time.

---

##  Getting Started

### Prerequisites

- Node.js (v18+)  
- Python 3.9+  
- (Optional) PostgreSQL, Redis, etc.

### Setup

#### Frontend

```bash
cd frontend
npm install
npm run dev
```