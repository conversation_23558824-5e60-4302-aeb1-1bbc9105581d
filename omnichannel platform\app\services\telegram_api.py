import httpx
from typing import Dict, Any, Optional, Union

class TelegramBotAPI:
    BASE_URL = "https://api.telegram.org/bot"

    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.client = httpx.AsyncClient()

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        url = f"{self.BASE_URL}{self.bot_token}/{endpoint}"
        try:
            response = await self.client.post(url, json=data)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            print(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
            raise
        except httpx.RequestError as e:
            print(f"An error occurred while requesting {e.request.url!r}: {e}")
            raise

    async def get_me(self) -> Dict[str, Any]:
        """A simple method for testing your bot's authentication token."""
        return await self._make_request("POST", "getMe")

    async def send_message(self, chat_id: Union[int, str], text: str, **kwargs) -> Dict[str, Any]:
        """Send a text message."""
        data = {"chat_id": chat_id, "text": text, **kwargs}
        return await self._make_request("POST", "sendMessage", data)

    async def set_webhook(self, url: str, **kwargs) -> Dict[str, Any]:
        """Specify a URL to receive incoming updates via an outgoing webhook."""
        data = {"url": url, **kwargs}
        return await self._make_request("POST", "setWebhook", data)

    async def delete_webhook(self, **kwargs) -> Dict[str, Any]:
        """Remove webhook integration if currently set."""
        return await self._make_request("POST", "deleteWebhook", kwargs)

    async def close(self):
        await self.client.aclose()
