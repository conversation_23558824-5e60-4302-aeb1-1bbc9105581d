from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select
from typing import List, Optional, Union
from app.models.conversation import Conversation, ConversationMessage
from app.models.omnichannel import OmnichannelMessage, Channel, ChannelType, ChannelStatus
from app.db.session import get_db
from app.schemas.inbox import ConversationRead, MessageRead, ChannelRead
from app.api.dependencies import get_current_user
from app.models.user import User

router = APIRouter(prefix="/conversations", tags=["inbox"])

@router.get("/channels", response_model=List[ChannelRead])
async def list_channels(
    channel_type: Optional[ChannelType] = Query(
        None, 
        description="Filter by channel type"
    ),
    status: Optional[ChannelStatus] = Query(
        None, 
        description="Filter by channel status"
    ),
    is_active: Optional[bool] = Query(
        None, 
        description="Filter by active status"
    ),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List all channels (inboxes) available to the current user.
    
    - **Super Admins**: See all channels across all companies
    - **Regular Users**: Only see channels from their own company
    """
    query = select(Channel)
    
    # Apply company filter based on user role
    if current_user.role != "super_admin":
        query = query.where(Channel.company_id == current_user.company_id)
    
    # Apply filters if provided
    if channel_type is not None:
        query = query.where(Channel.channel_type == channel_type)
    if status is not None:
        query = query.where(Channel.status == status)
    if is_active is not None:
        query = query.where(Channel.is_active == is_active)
    
    # Order by name for consistent results
    query = query.order_by(Channel.name)
    
    channels = db.exec(query).all()
    return channels

@router.get("/", response_model=List[ConversationRead])
def list_conversations(
    company_id: Optional[int] = None,
    channel_id: Optional[int] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    query = select(Conversation)
    if current_user.role == "super_admin":
        if company_id:
            query = query.where(Conversation.company_id == company_id)
    else:
        query = query.where(Conversation.company_id == current_user.company_id)
    if channel_id:
        query = query.where(Conversation.channel_id == channel_id)
    if status:
        query = query.where(Conversation.status == status)
    return db.exec(query).all()

@router.get("/{conversation_id}", response_model=ConversationRead)
def get_conversation(conversation_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    conversation = db.get(Conversation, conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    if current_user.role != "super_admin" and conversation.company_id != current_user.company_id:
        raise HTTPException(status_code=403, detail="Forbidden")
    return conversation

@router.get("/{conversation_id}/messages", response_model=List[MessageRead])
def get_messages(conversation_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    conversation = db.get(Conversation, conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    if current_user.role != "super_admin" and conversation.company_id != current_user.company_id:
        raise HTTPException(status_code=403, detail="Forbidden")
    return conversation.messages

@router.post("/{conversation_id}/assign", response_model=ConversationRead)
def assign_conversation(conversation_id: int, user_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    conversation = db.get(Conversation, conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    if current_user.role != "super_admin" and conversation.company_id != current_user.company_id:
        raise HTTPException(status_code=403, detail="Forbidden")
    conversation.assigned_user_id = user_id
    db.commit()
    db.refresh(conversation)
    return conversation

@router.post("/{conversation_id}/tags", response_model=ConversationRead)
def add_tags(conversation_id: int, tags: List[str], db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    conversation = db.get(Conversation, conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    if current_user.role != "super_admin" and conversation.company_id != current_user.company_id:
        raise HTTPException(status_code=403, detail="Forbidden")
    if not conversation.metadata_:
        conversation.metadata_ = {}
    conversation.metadata_["tags"] = tags
    db.commit()
    db.refresh(conversation)
    return conversation
