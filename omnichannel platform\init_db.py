"""Initialize the database with required tables and initial data."""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add the project root to the Python path
project_root = str(Path(__file__).parent.absolute())
sys.path.insert(0, project_root)

from alembic.config import Config
from alembic import command
from sqlmodel import SQLModel
from app.database import engine, SQLALCHEMY_DATABASE_URL
from app.utils.security import hash_password as get_password_hash

def run_migrations(script_location: str, dsn: str) -> None:
    """Run migrations using Alembic.
    
    Args:
        script_location: Path to the migrations directory
        dsn: Database connection string
    """
    # Create Alembic config
    config = Config()
    config.set_main_option('script_location', script_location)
    config.set_main_option('sqlalchemy.url', dsn)
    
    # Run migrations
    command.upgrade(config, 'head')

def init_database(run_migrations_flag: bool = True) -> None:
    """Initialize database tables.
    
    Args:
        run_migrations_flag: If True, run Alembic migrations
    """
    if run_migrations_flag:
        # Run migrations using Alembic
        migrations_dir = str(Path(__file__).parent / "migrations")
        run_migrations(migrations_dir, SQLALCHEMY_DATABASE_URL)
        print("Database migrations applied successfully!")
        # Force create all tables to ensure schema is up-to-date with models
        SQLModel.metadata.create_all(engine)
        print("Forced creation of all tables.")
    else:
        # Fallback: Create all tables directly (only for development)
        print("Creating all tables directly (not using migrations)...")
        SQLModel.metadata.create_all(engine)
        print("Database tables created successfully!")


def create_admin_user():
    """Create an admin user if one doesn't exist."""
    from app.models.user import User, UserRole, UserStatus
    from app.database import get_session

    with get_session() as session:
        # Check if admin user exists
        admin = session.query(User).filter(User.email == "<EMAIL>").first()
        if not admin:
            admin = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=get_password_hash("admin123"),
                first_name="Admin",
                last_name="User",
                role=UserRole.ADMIN.value,  # Use .value for enums
                status=UserStatus.ACTIVE.value,  # Use .value for enums
                is_email_verified=True,
            )
            session.add(admin)
            session.commit()
            print("Created admin user with email: <EMAIL> and password: admin123")
        else:
            print("Admin user already exists")


def create_sample_data():
    """Create sample data for development."""
    from app.models.user import User, UserRole, UserStatus
    from app.database import get_session

    with get_session() as session:
        # Check if admin user already exists
        admin_user = session.query(User).filter(User.email == "<EMAIL>").first()
        
        if not admin_user:
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=get_password_hash("admin123"),
                first_name="Admin",
                last_name="User",
                role=UserRole.SUPER_ADMIN.value,  # Use .value for enums
                status=UserStatus.ACTIVE.value,  # Use .value for enums
                is_email_verified=True
            )
            session.add(admin_user)
            
            # Create demo user
            demo_user = User(
                email="<EMAIL>",
                username="demo",
                hashed_password=get_password_hash("demo123"),
                first_name="Demo",
                last_name="User",
                role=UserRole.USER.value,  # Use .value for enums
                status=UserStatus.ACTIVE.value,  # Use .value for enums
                is_email_verified=True
            )
            session.add(demo_user)
            
            session.commit()
            print("Sample users created:")
            print("Admin: <EMAIL> / admin123")
            print("Demo: <EMAIL> / demo123")


def main():
    """Initialize the database and create required tables."""
    print("Initializing database...")
    try:
        # First run migrations to ensure schema is up to date
        init_database(run_migrations_flag=True)
        
        # Then create admin user and sample data
        create_admin_user()
        create_sample_data()
        print("Database initialization completed successfully!")
    except Exception as e:
        print(f"Error initializing database: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
