"""Initialize the database with required tables and initial data."""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add the project root to the Python path
project_root = str(Path(__file__).parent.absolute())
sys.path.insert(0, project_root)

from alembic.config import Config
from alembic import command
from sqlmodel import SQLModel
from app.db.session import engine
from sqlmodel import Session, select
from app.utils.security import hash_password as get_password_hash

def run_migrations(script_location: str, dsn: str) -> None:
    """Run migrations using Alembic.
    
    Args:
        script_location: Path to the migrations directory
        dsn: Database connection string
    """
    # Create Alembic config
    config = Config()
    config.set_main_option('script_location', script_location)
    config.set_main_option('sqlalchemy.url', dsn)
    
    # Run migrations
    command.upgrade(config, 'head')

def init_database(run_migrations_flag: bool = True) -> None:
    """Initialize database tables.
    
    Args:
        run_migrations_flag: If True, run Alembic migrations
    """
    if run_migrations_flag:
        # Run migrations using Alembic
        migrations_dir = str(Path(__file__).parent / "migrations")
        run_migrations(migrations_dir, SQLALCHEMY_DATABASE_URL)
        print("Database migrations applied successfully!")
        # Force create all tables to ensure schema is up-to-date with models
        SQLModel.metadata.create_all(engine)
        print("Forced creation of all tables.")
    else:
        # Fallback: Create all tables directly (only for development)
        print("Creating all tables directly (not using migrations)...")
        SQLModel.metadata.create_all(engine)
        print("Database tables created successfully!")


def create_admin_user():
    """Create an admin user if one doesn't exist."""
    from app.models.user import User, UserRole, UserStatus
    from app.database import get_session

    with get_session() as session:
        # Check if admin user exists
        admin = session.query(User).filter(User.email == "<EMAIL>").first()
        if not admin:
            admin = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=get_password_hash("admin123"),
                first_name="Admin",
                last_name="User",
                role=UserRole.ADMIN.value,  # Use .value for enums
                status=UserStatus.ACTIVE.value,  # Use .value for enums
                is_email_verified=True,
            )
            session.add(admin)
            session.commit()
            print("Created admin user with email: <EMAIL> and password: admin123")
        else:
            print("Admin user already exists")


def create_sample_data():
    """Create sample data for development."""
    from app.models.user import User, UserRole, UserStatus
    from app.database import get_session

    with get_session() as session:
        # Check if admin user already exists
        admin_user = session.query(User).filter(User.email == "<EMAIL>").first()
        
        if not admin_user:
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=get_password_hash("admin123"),
                first_name="Admin",
                last_name="User",
                role=UserRole.SUPER_ADMIN.value,  # Use .value for enums
                status=UserStatus.ACTIVE.value,  # Use .value for enums
                is_email_verified=True
            )
            session.add(admin_user)
            
            # Create demo user
            demo_user = User(
                email="<EMAIL>",
                username="demo",
                hashed_password=get_password_hash("demo123"),
                first_name="Demo",
                last_name="User",
                role=UserRole.USER.value,  # Use .value for enums
                status=UserStatus.ACTIVE.value,  # Use .value for enums
                is_email_verified=True
            )
            session.add(demo_user)
            
            session.commit()
            print("Sample users created:")
            print("Admin: <EMAIL> / admin123")
            print("Demo: <EMAIL> / demo123")


def main():
    """Initialize the database and create required tables."""
    print("🔧 Initializing database...")
    try:
        # Skip migrations, just create tables directly
        print("Creating all tables directly...")
        SQLModel.metadata.create_all(engine)
        print("✅ Database tables created successfully!")

        # Create test users using new approach
        create_test_users_simple()
        print("🎉 Database initialization completed successfully!")
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def create_test_users_simple():
    """Create test users using the new database session."""
    from app.models.user import User, UserRole, UserStatus
    from app.models.company import Company
    from passlib.context import CryptContext

    pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')

    with Session(engine) as db:
        # Create company
        company = db.exec(select(Company)).first()
        if not company:
            company = Company(
                name='WhatsApp Platform',
                description='Main company for WhatsApp platform'
            )
            db.add(company)
            db.commit()
            db.refresh(company)
            print(f"✅ Created company: {company.name} (id={company.id})")

        # Create super admin user
        super_admin_email = '<EMAIL>'
        super_admin_password = 'admin123'

        existing_admin = db.exec(select(User).where(User.email == super_admin_email)).first()
        if not existing_admin:
            hashed_password = pwd_context.hash(super_admin_password)
            admin_user = User(
                email=super_admin_email,
                username='admin',
                hashed_password=hashed_password,
                first_name='Admin',
                last_name='User',
                company_id=company.id,
                role=UserRole.SUPER_ADMIN,
                status=UserStatus.ACTIVE,
                is_email_verified=True
            )
            db.add(admin_user)
            db.commit()
            print(f"✅ Created super admin: {super_admin_email} / {super_admin_password}")

        # Create test user
        test_email = '<EMAIL>'
        test_password = 'test123'

        existing_test = db.exec(select(User).where(User.email == test_email)).first()
        if not existing_test:
            hashed_password = pwd_context.hash(test_password)
            test_user = User(
                email=test_email,
                username='testuser',
                hashed_password=hashed_password,
                first_name='Test',
                last_name='User',
                company_id=company.id,
                role=UserRole.USER,
                status=UserStatus.ACTIVE,
                is_email_verified=True
            )
            db.add(test_user)
            db.commit()
            print(f"✅ Created test user: {test_email} / {test_password}")

        print("\n📋 Test Credentials:")
        print(f"   Super Admin: {super_admin_email} / {super_admin_password}")
        print(f"   Test User:   {test_email} / {test_password}")


if __name__ == "__main__":
    main()
